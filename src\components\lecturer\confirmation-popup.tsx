"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Trash2, Save } from "lucide-react"

interface ConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  message: string
  type: "save" | "delete" | "success" | "error"
  confirmText?: string
  cancelText?: string
}

export default function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  type,
  confirmText = "ยืนยัน",
  cancelText = "ยกเลิก",
}: ConfirmationModalProps) {
  if (!isOpen) return null

  const getTypeStyles = () => {
    switch (type) {
      case "save":
        return {
          iconBg: "bg-blue-100",
          iconColor: "text-[#293D97]",
          confirmBg: "bg-[#293D97] hover:bg-[#1e2d7a]",
          icon: Save,
        }
      case "delete":
        return {
          iconBg: "bg-red-100",
          iconColor: "text-red-600",
          confirmBg: "bg-red-600 hover:bg-red-700",
          icon: Trash2,
        }
      case "success":
        return {
          iconBg: "bg-green-100",
          iconColor: "text-[#008067]",
          confirmBg: "bg-[#008067] hover:bg-[#006b57]",
          icon: CheckCircle,
        }
      case "error":
        return {
          iconBg: "bg-red-100",
          iconColor: "text-red-600",
          confirmBg: "bg-red-600 hover:bg-red-700",
          icon: AlertTriangle,
        }
      default:
        return {
          iconBg: "bg-gray-100",
          iconColor: "text-gray-600",
          confirmBg: "bg-gray-600 hover:bg-gray-700",
          icon: AlertTriangle,
        }
    }
  }

  const styles = getTypeStyles()
  const IconComponent = styles.icon
  const isSuccessOrError = type === "success" || type === "error"

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${styles.iconBg}`}>
              <IconComponent className={`h-5 w-5 ${styles.iconColor}`} />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 transition-colors">
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          <p className="text-gray-600 leading-relaxed">{message}</p>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 p-4 border-t border-gray-200">
          {!isSuccessOrError && (
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            >
              {cancelText}
            </button>
          )}
          <button
            onClick={isSuccessOrError ? onClose : onConfirm}
            className={`px-4 py-2 text-white rounded-md transition-colors ${styles.confirmBg}`}
          >
            {isSuccessOrError ? "ตกลง" : confirmText}
          </button>
        </div>
      </div>
    </div>
  )
}
