"use client"

import type React from "react"
import { useState, useEffect, useCallback } from "react"
import { Plus, X, ChevronDown, ChevronLeft } from "lucide-react"
import Link from "next/link"
import { fetchAssignedStudentPathBySlug, updateAssignedStudentPaths, createAssignedStudentPaths, fetchAllAssignedStudentPaths, removeAssignedStudentPath } from "@/hook/assignpathService";
import { getPath } from "@/hook/pathService";
import { getUsers } from "@/hook/userService";
import type { AssignStudent, AssignedPath } from "@/types/api/assignpath";
import type { tableResponse } from "@/types/api/path";
import type { UserSlugResponse } from "@/types/api/users";
import { useRouter } from "next/navigation"
import { showSuccessAlert, showErrorAlert, showDeleteConfirmDialog, showConfirmDialog } from "@/lib/sweetAlert"

interface AssignEditorProps {
    mode: "create" | "edit"
    preSelectedStudentId?: string
    initialAssignedPaths?: string[]
}

interface SelectedPath {
    id: string
    name: string
    description: string
    courseCount: number
    learningType: boolean // true = sequential, false = freestyle
}

export default function AssignEditor({ mode, preSelectedStudentId, initialAssignedPaths = [] }: AssignEditorProps) {
    const router = useRouter()
    const [selectedStudentId, setSelectedStudentId] = useState(preSelectedStudentId || "")
    const [selectedPaths, setSelectedPaths] = useState<SelectedPath[]>([])
    const [showStudentDropdown, setShowStudentDropdown] = useState(false)
    const [showPathDropdown, setShowPathDropdown] = useState(false)
    const [studentSearchTerm, setStudentSearchTerm] = useState("")
    const [pathSearchTerm, setPathSearchTerm] = useState("")
    const [studentInfo, setStudentInfo] = useState<AssignStudent | null>(null)
    const [students, setStudents] = useState<UserSlugResponse[]>([])
    const [allPaths, setAllPaths] = useState<tableResponse[]>([])

    // Function to load assigned paths for a student (from API)
    const loadStudentAssignedPaths = useCallback(async (studentId: string) => {
        try {
            const data: AssignStudent = await fetchAssignedStudentPathBySlug(studentId)
            setStudentInfo(data)
            const paths = (data.assigned_paths || []).map((p: AssignedPath) => {
                const path = allPaths.find((lp) => String(lp.id) == String(p.path_id))
                // If p.learning_type exists and is boolean, use it. Otherwise default to false (freestyle)
                return {
                    id: String(p.path_id),
                    name: p.path_name,
                    description: path?.description || "",
                    courseCount: path?.amount || 0,
                    learningType: typeof p.learning_type === "boolean" ? p.learning_type : false
                }
            })
            setSelectedPaths(paths)
        } catch (e) {
            setSelectedPaths([])
            setStudentInfo(null)
        }
    }, [allPaths])

    // Initialize selected paths from API (edit mode)
    useEffect(() => {
        if (mode === "edit" && preSelectedStudentId) {
            loadStudentAssignedPaths(preSelectedStudentId)
        }
    }, [mode, preSelectedStudentId, loadStudentAssignedPaths])

    // Load assigned paths when student is selected (create mode only)
    useEffect(() => {
        if (mode === "create" && selectedStudentId && selectedStudentId !== preSelectedStudentId) {
            loadStudentAssignedPaths(selectedStudentId)
        }
    }, [selectedStudentId, mode, preSelectedStudentId, loadStudentAssignedPaths])

    // Fetch all students for dropdown (use full user list, not just assigned)
    useEffect(() => {
        async function fetchStudents() {
            try {
                const data = await getUsers();
                // Debug: log API response
                console.log('Fetched users:', data);
                const filtered = (data || [])
                    .filter((user: any) =>
                        user.role === 1 ||
                        user.role === "1" ||
                        user.role === "student" ||
                        user.user_role === 1 ||
                        user.user_role === "1" ||
                        user.user_role === "student"
                    )
                    .map((user: any) => ({
                        slug: user.user_slug,
                        user_picture: user.user_picture || "",
                        password: user.password || "",
                        user_fname: user.user_fname || user.fname || user.firstname || "",
                        user_lname: user.user_lname || user.lname || user.lastname || "",
                        user_email: user.user_email || user.email || "",
                        user_position: user.user_position || user.position || "",
                        user_login_date: user.user_login_date || "",
                        role: user.role ?? "student",
                        status: user.status ?? 1,
                    }));
                // Debug: log filtered students
                console.log('Filtered students:', filtered);
                setStudents(filtered);
            } catch (e) {
                setStudents([]);
            }
        }
        fetchStudents();
    }, []);

    // Fetch all learning paths from API
    useEffect(() => {
        async function fetchPaths() {
            try {
                const data = await getPath();
                setAllPaths(data || []);
            } catch (e) {
                setAllPaths([]);
            }
        }
        fetchPaths();
    }, []);

    // Get available students (searchable)
    const availableStudents = students.filter((student) =>
        `${student.user_fname} ${student.user_lname} ${student.user_email}`.toLowerCase().includes(studentSearchTerm.toLowerCase()),
    )

    // Get selected student info (from API if loaded)
    const selectedStudent = students.find((s) => s.slug === selectedStudentId) || studentInfo

    // Handle student selection
    const handleStudentSelection = (user_slug: string) => {
        setSelectedStudentId(user_slug)
        setShowStudentDropdown(false)
        setStudentSearchTerm("")
        // Always clear studentInfo when selecting a new student
        setStudentInfo(null)
        loadStudentAssignedPaths(user_slug)
    }

    // Add path to assignment (from API data)
    const addPath = (pathId: string) => {
        const path = allPaths.find((p) => String(p.id) === String(pathId))
        if (path) {
            setSelectedPaths((prev) => [
                ...prev,
                {
                    id: String(path.id),
                    name: path.name,
                    description: path.description,
                    courseCount: path.amount, // Use amount for course count
                    learningType: false, // Default to freestyle (false)
                },
            ])
            setShowPathDropdown(false)
            setPathSearchTerm("")
        }
    }

    // Remove path from assignment with SweetAlert confirmation
    const removePath = async (pathId: string) => {
        const path = selectedPaths.find((p) => p.id === pathId)
        if (path) {
            const result = await showDeleteConfirmDialog(`คุณต้องการลบเส้นทาง "${path.name}" ออกจากการมอบหมายหรือไม่?`)
            if (result.isConfirmed) {
                setSelectedPaths((prev) => prev.filter((p) => p.id !== pathId))
                await showSuccessAlert("ลบเส้นทางสำเร็จ", "ลบเส้นทางการเรียนรู้ออกจากการมอบหมายเรียบร้อยแล้ว")
            }
        }
    }

    // Update learning type for a specific path
    const updatePathLearningType = (pathId: string, learningType: boolean) => {
        setSelectedPaths((prev) => prev.map((path) => (path.id === pathId ? { ...path, learningType } : path)))
    }

    // Handle save confirmation and use SweetAlert for all dialogs
    const handleSaveClick = async () => {
        if (!selectedStudentId) {
            await showErrorAlert("ข้อมูลไม่ครบถ้วน", "กรุณาเลือกนักเรียน")
            return
        }
        if (selectedPaths.length === 0) {
            await showErrorAlert("ไม่มีเส้นทางการเรียนรู้", "กรุณาเพิ่มเส้นทางการเรียนรู้อย่างน้อย 1 เส้นทาง")
            return
        }
        const studentName = selectedStudent
            ? `${(selectedStudent as any).user_fname} ${(selectedStudent as any).user_lname}`
            : "นักเรียน"
        const result = await showConfirmDialog(
            mode === "create"
                ? `คุณต้องการมอบหมายเส้นทางการเรียนรู้ ${selectedPaths.length} เส้นทางให้กับ ${studentName} หรือไม่?`
                : `คุณต้องการบันทึกการแก้ไขการมอบหมายเส้นทางการเรียนรู้ให้กับ ${studentName} หรือไม่?`
        )
        if (!result.isConfirmed) return
        await handleSave()
    }

    // Handle actual save (call API)
    const handleSave = async () => {
        try {
            const payload = selectedPaths.map((path) => ({ path_id: Number(path.id), learning_type: path.learningType }))
            if (mode === "edit") {
                await updateAssignedStudentPaths(selectedStudentId, payload)
            } else {
                await createAssignedStudentPaths(selectedStudentId, payload)
            }
            await showSuccessAlert(
                mode === "create" ? "มอบหมายสำเร็จ" : "บันทึกสำเร็จ",
                mode === "create" ? "มอบหมายเส้นทางการเรียนรู้เรียบร้อยแล้ว" : "บันทึกการแก้ไขการมอบหมายเรียบร้อยแล้ว"
            )
            setTimeout(() => {
                router.push("/lecturer/assign-paths")
            }, 800)
        } catch (e) {
            await showErrorAlert("เกิดข้อผิดพลาด", "ไม่สามารถบันทึกข้อมูลได้ กรุณาลองใหม่อีกครั้ง")
        }
    }

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        handleSaveClick()
    }

    // Filter available paths for dropdown (exclude already selected)
    const availablePaths = allPaths.filter(
        (path) =>
            !selectedPaths.some((sp) => String(sp.id) === String(path.id)) &&
            `${path.name} ${path.description}`.toLowerCase().includes(pathSearchTerm.toLowerCase())
    )

    // CRUD functions for assignments
    // GET: fetchAllAssignedStudentPaths, fetchAssignedStudentPathBySlug (already used)
    // POST: createAssignedStudentPaths (already used)
    // PUT: updateAssignedStudentPaths (already used)
    // DELETE: deleteAssignedStudentPath (add usage below)

    // Example: Delete assignment for a student (API)
    const handleDeleteAssignment = async (studentSlug: string, pathId: string) => {
        try {
            const result = await showDeleteConfirmDialog("คุณต้องการลบการมอบหมายเส้นทางนี้หรือไม่?")
            if (!result.isConfirmed) return
            await removeAssignedStudentPath(studentSlug, pathId);
            loadStudentAssignedPaths(studentSlug);
            await showSuccessAlert("ลบการมอบหมายสำเร็จ", "ลบการมอบหมายเส้นทางการเรียนรู้เรียบร้อยแล้ว")
            setTimeout(() => {
                router.push("/lecturer/assign-paths")
            }, 800)
        } catch (e) {
            await showErrorAlert("เกิดข้อผิดพลาด", "ไม่สามารถลบข้อมูลได้ กรุณาลองใหม่อีกครั้ง")
        }
    };

    return (
        <>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                {/* Header with ChevronLeft back button and save button */}
                <div className="flex justify-between items-center p-4 border-b border-gray-200">
                    <div className="flex items-center gap-2">
                        <button
                            onClick={() => router.push('/lecturer/assign-paths')}
                            className="p-0 bg-transparent border-none focus:outline-none flex items-center"
                            aria-label="ย้อนกลับไปหน้าตารางการมอบหมาย"
                            type="button"
                        >
                            <ChevronLeft className="h-7 w-7" />
                        </button>
                        <h1 className="text-xl font-bold text-gray-800">
                            {mode === "edit" ? "แก้ไขเส้นทางการเรียนรู้" : "มอบหมายเส้นทางเรียนรู้"}
                        </h1>
                    </div>
                    <div className="flex space-x-3">
                        <button
                            onClick={handleSaveClick}
                            disabled={!selectedStudentId}
                            className={`px-4 py-2 rounded-md transition-colors font-semibold ${selectedStudentId
                                ? "bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white shadow-lg"
                                : "bg-gray-300 text-gray-500 cursor-not-allowed"
                                }`}
                        >
                            {mode === "create" ? "สร้างการมอบหมาย" : "บันทึกการแก้ไข"}
                        </button>
                    </div>
                </div>

                <form className="space-y-0">
                    {/* Student Selection Section */}
                    <div className="p-6 border-b border-gray-200">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">นักเรียน *</label>
                            <div className="relative max-w-md">
                                <button
                                    type="button"
                                    onClick={() => !preSelectedStudentId && setShowStudentDropdown(!showStudentDropdown)}
                                    disabled={!!preSelectedStudentId}
                                    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10 text-left ${preSelectedStudentId ? "cursor-not-allowed opacity-75" : "cursor-pointer"
                                        }`}
                                >
                                    {selectedStudent ? `${(selectedStudent as any).user_fname} ${(selectedStudent as any).user_lname}` : "เลือกนักเรียน"}
                                </button>
                                {!preSelectedStudentId && (
                                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 pointer-events-none" />
                                )}

                                {showStudentDropdown && !preSelectedStudentId && (
                                    <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg">
                                        <div className="p-2">
                                            <input
                                                type="text"
                                                value={studentSearchTerm}
                                                onChange={(e) => setStudentSearchTerm(e.target.value)}
                                                placeholder="ค้นหานักเรียน..."
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268]"
                                            />
                                        </div>
                                        <div className="max-h-60 overflow-y-auto">
                                            {availableStudents.length > 0 ? (
                                                availableStudents.map((student) => (
                                                    <button
                                                        key={student.slug}
                                                        type="button"
                                                        onClick={() => handleStudentSelection(student.slug)}
                                                        className="w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                                                    >
                                                        <div className="flex items-center space-x-3">
                                                            <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                                                <span className="text-gray-500 font-medium text-xs">
                                                                    {student.user_fname.charAt(0)}{student.user_lname.charAt(0)}
                                                                </span>
                                                            </div>
                                                            <div>
                                                                <div className="font-medium text-gray-900">
                                                                    {student.user_fname} {student.user_lname}
                                                                </div>
                                                                <div className="text-sm text-gray-500">{student.user_email}</div>
                                                            </div>
                                                        </div>
                                                    </button>
                                                ))
                                            ) : (
                                                <div className="px-4 py-3 text-gray-500 text-center">
                                                    {studentSearchTerm ? "ไม่พบนักเรียนที่ค้นหา" : "ไม่มีนักเรียนในระบบ"}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>


                        </div>
                    </div>

                    {/* Learning Paths Management Section */}
                    <div className="p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-lg font-semibold text-gray-800">เส้นทางการเรียนรู้</h2>
                            <span className="text-sm text-gray-500">{selectedPaths.length} เส้นทาง</span>
                        </div>

                        {/* Selected Paths */}
                        <div className="space-y-3">
                            {selectedPaths.map((path) => (
                                <div key={path.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                    <div className="flex items-center gap-4">
                                        <div className="flex-1">
                                            <h4 className="font-medium text-black">{path.name}</h4>
                                            <p className="text-sm text-gray-500 mt-1 line-clamp-2">{path.description}</p>
                                            <div className="flex items-center gap-2 mt-2">
                                                <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 border border-[#7586d3] text-[#293D97]">
                                                    {path.courseCount} คอร์ส
                                                </span>
                                            </div>
                                        </div>

                                        {/* Learning Mode Dropdown - positioned before delete button */}
                                        <div className="flex items-center gap-3">
                                            <div className="relative">
                                                <select
                                                    value={path.learningType ? "sequential" : "freestyle"}
                                                    onChange={(e) =>
                                                        updatePathLearningType(path.id, e.target.value === "sequential")
                                                    }
                                                    className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-white pr-8 min-w-[140px]"
                                                >
                                                    <option value="sequential">เรียนตามลำดับ</option>
                                                    <option value="freestyle">ฟรีสไตล์</option>
                                                </select>
                                                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 h-3 w-3 pointer-events-none" />
                                            </div>

                                            <button
                                                type="button"
                                                onClick={() => removePath(path.id)}
                                                className="text-red-500 hover:text-red-700 p-1"
                                            >
                                                <X className="h-4 w-4" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}

                            {/* Add Path Section */}
                            <div className="bg-gray-100 border-2 border-dashed border-gray-400 rounded-lg py-3 px-4">
                                <div className="flex justify-center">
                                    <button
                                        type="button"
                                        onClick={() => setShowPathDropdown(!showPathDropdown)}
                                        className="flex items-center gap-2 px-8 py-2 bg-white hover:bg-gray-50 rounded-lg text-gray-700 transition-colors font-medium border border-gray-300 min-w-[280px]"
                                    >
                                        <Plus className="h-4 w-4" />
                                        เพิ่มเส้นทางการเรียนรู้
                                    </button>
                                </div>

                                {/* Path Dropdown */}
                                {showPathDropdown && (
                                    <div className="relative mt-3">
                                        <div className="relative mb-2">
                                            <input
                                                type="text"
                                                value={pathSearchTerm}
                                                onChange={(e) => setPathSearchTerm(e.target.value)}
                                                placeholder="ค้นหาเส้นทางการเรียนรู้..."
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-white"
                                            />
                                        </div>

                                        <div className="bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                                            {availablePaths.length > 0 ? (
                                                availablePaths.map((path) => (
                                                    <button
                                                        key={path.id}
                                                        type="button"
                                                        onClick={() => addPath(String(path.id))}
                                                        className="w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                                                    >
                                                        <div className="font-medium text-black">{path.name}</div>
                                                        <div className="text-sm text-gray-500 mt-1 line-clamp-2">{path.description}</div>
                                                        <div className="flex items-center gap-2 mt-2">
                                                            <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                                {path.amount} คอร์ส
                                                            </span>
                                                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${path.status ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}`}>
                                                                {path.status ? "เผยแพร่แล้ว" : "ยังไม่เผยแพร่"}
                                                            </span>
                                                        </div>
                                                    </button>
                                                ))
                                            ) : (
                                                <div className="px-4 py-3 text-gray-500 text-center">
                                                    {pathSearchTerm ? "ไม่พบเส้นทางที่ค้นหา" : "ไม่มีเส้นทางที่สามารถเพิ่มได้"}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Empty state when no paths */}
                        {selectedPaths.length === 0 && selectedStudentId && (
                            <div className="text-center py-12 text-gray-500 mb-6">
                                <div className="text-lg mb-2">นักเรียนคนนี้ยังไม่มีเส้นทางการเรียนรู้</div>
                                <div className="text-sm">คลิกปุ่ม "เพิ่มเส้นทางการเรียนรู้" เพื่อเริ่มมอบหมาย</div>
                            </div>
                        )}

                        {!selectedStudentId && (
                            <div className="text-center py-12 text-gray-500 mb-6">
                                <div className="text-lg mb-2">กรุณาเลือกนักเรียนก่อน</div>
                                <div className="text-sm">เลือกนักเรียนเพื่อดูและจัดการเส้นทางการเรียนรู้</div>
                            </div>
                        )}
                    </div>
                </form>
            </div>
        </>
    )
}
