"use client"

import React, { useState } from "react"
import { useRouter } from "next/navigation"
import { Search, ChevronLeft, ChevronRight, Edit, Trash2, MoreHorizontal, Eye, Plus } from "lucide-react"
import { ChevronUp, ChevronDown } from "lucide-react"
import { quizService } from '@/hook/quizService'
import { showDeleteConfirmDialog, showSuccessAlert, showErrorAlert } from "@/lib/sweetAlert"

// เพิ่ม CSS สำหรับ dropdown menu
const dropdownStyles = {
  ".dropdown-menu": {
    position: "absolute",
    right: "0",
    zIndex: "50",
  },
  "@media (max-width: 640px)": {
    ".dropdown-menu": {
      right: "0",
      left: "auto",
    },
  },
}

export default function QuizTable() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedRows, setSelectedRows] = useState<string[]>([])
  const [sortField, setSortField] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [selectAll, setSelectAll] = useState(false)
  const [quizzes, setQuizzes] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const itemsPerPage = 10

  // Fetch quizzes from API
  React.useEffect(() => {
    setLoading(true)
    quizService.getAllQuizzes()
      .then((data) => {
        setQuizzes(data)
        setLoading(false)
      })
      .catch((err) => {
        setError(err.message || 'เกิดข้อผิดพลาดในการโหลดข้อมูลควิซ')
        setLoading(false)
      })
  }, [])

  // ฟังก์ชันค้นหา
  const filteredQuizzes = Array.isArray(quizzes) && quizzes.length > 0
    ? quizzes.filter(
        (quiz) =>
          (quiz.quiz_name || "").toLowerCase().includes(searchQuery.toLowerCase()) ||
          String(quiz.id).toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : [];

  // ฟังก์ชันเรียงลำดับ
  const sortedQuizzes = [...filteredQuizzes].sort((a, b) => {
    if (!sortField) return 0
    const fieldA = a[sortField]
    const fieldB = b[sortField]
    if (typeof fieldA === "string" && typeof fieldB === "string") {
      return sortDirection === "asc" ? fieldA.localeCompare(fieldB) : fieldB.localeCompare(fieldA)
    }
    if (typeof fieldA === "number" && typeof fieldB === "number") {
      return sortDirection === "asc" ? fieldA - fieldB : fieldB - fieldA
    }
    return 0
  })

  // ฟังก์ชันแบ่งหน้า
  const totalPages = Math.ceil(sortedQuizzes.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedQuizzes = sortedQuizzes.slice(startIndex, startIndex + itemsPerPage)

  // ฟังก์ชันเลือกแถว
  const toggleRowSelection = (id: string) => {
    if (selectedRows.includes(id)) {
      setSelectedRows(selectedRows.filter((rowId) => rowId !== id))
    } else {
      setSelectedRows([...selectedRows, id])
    }
  }

  // ฟังก์ชันเลือกทั้งหมด
  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedRows([])
    } else {
      setSelectedRows(paginatedQuizzes.map((quiz) => quiz.id))
    }
    setSelectAll(!selectAll)
  }

  // ฟังก์ชันเรียงลำดับ
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // ฟังก์ชันเปิด/ปิด dropdown
  const toggleDropdown = (id: string) => {
    if (activeDropdown === id) {
      setActiveDropdown(null)
    } else {
      setActiveDropdown(id)
    }
  }

  // แสดงไอคอนการเรียงลำดับ
  function renderSortIcon(field: string): React.ReactNode {
    if (sortField === field) {
      return sortDirection === "asc" ? (
        <ChevronUp className="ml-1 h-4 w-4 text-gray-700" />
      ) : (
        <ChevronDown className="ml-1 h-4 w-4 text-gray-700" />
      )
    }
    // แสดงไอคอนทั้งขึ้นและลงเมื่อยังไม่ได้เรียงลำดับ
    return (
      <span className="ml-1 inline-flex flex-col">
        <ChevronUp className="h-3 w-3 -mb-1 text-gray-400" />
        <ChevronDown className="h-3 w-3 text-gray-400" />
      </span>
    )
  }

  if (loading) {
    return <div className="text-center py-4">กำลังโหลดข้อมูลควิซ...</div>
  }

  if (error) {
    return <div className="text-center py-4 text-red-600">{error}</div>
  }

  // --- ลบควิซพร้อม SweetAlert ---
  const handleDeleteQuiz = async (quiz: any) => {
    const result = await showDeleteConfirmDialog(
      `คุณแน่ใจหรือไม่ว่าต้องการลบควิซ "${quiz.quiz_name || quiz.quiz_description || quiz.slug}"? การกระทำนี้ไม่สามารถย้อนกลับได้`
    );
    if (result.isConfirmed) {
      try {
        await quizService.deleteQuiz(quiz.slug)
        setQuizzes(quizzes.filter((q) => q.id !== quiz.id))
        setSelectedRows(selectedRows.filter((rowId) => rowId !== quiz.id.toString()))
        await showSuccessAlert('ลบสำเร็จ', 'ควิซถูกลบเรียบร้อยแล้ว')
      } catch (err: any) {
        await showErrorAlert('เกิดข้อผิดพลาด', err.message || 'ไม่สามารถลบควิซได้')
      }
    }
    setActiveDropdown(null)
  }

  return (
    <div className="w-full">
      {/* ส่วนค้นหาและปุ่มเพิ่มควิซ */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
        <div className="relative w-full sm:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="ค้นหาควิซ..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full bg-gray-50 sm:w-[300px] focus:outline-none focus:ring-2 focus:ring-[#008268]"
          />
        </div>

        <button
          className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-md w-full font-bold sm:w-auto flex items-center justify-center gap-2"
          onClick={() => router.push("/admin/quiz/new")}
        >
          เพิ่มควิซใหม่
          <Plus className="h-4 w-4" />
        </button>
      </div>

      {/* ตาราง */}
      <div className="overflow-x-auto border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-3 py-3 text-center">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={toggleSelectAll}
                  className="h-4 w-4 rounded border-gray-300 bg-gray-50 text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                />
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("course")}
              >
                <div className="flex items-center">
                  คอร์ส
                  {renderSortIcon("course")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("name")}
              >
                <div className="flex items-center">
                  ชื่อควิซ
                  {renderSortIcon("name")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("questionCount")}
              >
                <div className="flex items-center justify-center">
                  จำนวนข้อ
                  {renderSortIcon("questionCount")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("status")}
              >
                <div className="flex items-center">
                  สถานะ
                  {renderSortIcon("status")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                จัดการ
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredQuizzes.length === 0 ? (
              <tr>
                <td colSpan={6} className="text-center py-4 text-gray-500">
                  ไม่พบควิซ
                </td>
              </tr>
            ) : (
              paginatedQuizzes.map((quiz) => (
                <tr key={quiz.id} className="hover:bg-gray-50">
                  <td className="px-3 py-4 whitespace-nowrap text-center">
                    <input
                      type="checkbox"
                      checked={selectedRows.includes(quiz.id.toString())}
                      onChange={() => toggleRowSelection(quiz.id.toString())}
                      className="h-4 w-4 rounded border-gray-300 bg-white text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{quiz.course_name || "-"}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{quiz.quiz_name || "-"}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <div className="text-sm text-gray-900">{quiz.questions ? quiz.questions.length : 0}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
            ${quiz.quiz_status
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-700"
                      }`}
                  >
                    {quiz.quiz_status ? "เผยแพร่" : "ไม่เผยแพร่"}
                  </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium relative">
                    <button onClick={() => toggleDropdown(quiz.id.toString())} className="text-gray-500 hover:text-gray-700">
                      <MoreHorizontal className="h-5 w-5" />
                    </button>

                    {activeDropdown === quiz.id.toString() && (
                      <div className="fixed right-0 sm:right-6 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10 dropdown-menu">
                        <div className="py-1" role="menu" aria-orientation="vertical">
                          <button
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => {
                              console.log("View details:", quiz.id)
                              setActiveDropdown(null)
                            }}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            ดูรายละเอียด
                          </button>
                          <button
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => {
                              router.push(`/admin/quiz/edit/${quiz.slug}`)
                              setActiveDropdown(null)
                            }}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            แก้ไข
                          </button>
                          <button
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                            onClick={() => handleDeleteQuiz(quiz)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            ลบ
                          </button>
                        </div>
                      </div>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* ส่วนแสดงจำนวนที่เลือกและการแบ่งหน้า */}
      <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4">
        <div className="text-sm text-gray-500">
          เลือก {selectedRows.length} จาก {filteredQuizzes.length} รายการ
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700">
            หน้า {currentPage} จาก {totalPages}
          </span>
          <div className="flex gap-1">
            <button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
