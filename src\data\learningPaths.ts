import type { LearningPath } from "@/types/learning-paths"

export const learningPathsData: LearningPath[] = [
  {
    id: "lp001",
    name: "การดูแลผู้ป่วยฉุกเฉิน",
    description: "เส้นทางการเรียนรู้ที่ครอบคลุมทักษะการดูแลผู้ป่วยฉุกเฉินตั้งแต่พื้นฐานจนถึงขั้นสูง",
    courseIds: ["WnH5F0qZF938", "QlH3I647MCuN"], // ใช้ ID จริงของคอร์ส
    courseCount: 2,
    status: "published",
  },
  {
    id: "lp002",
    name: "การพยาบาลเบื้องต้น",
    description: "เส้นทางสำหรับผู้เริ่มต้นในการเรียนรู้ทักษะการพยาบาลพื้นฐาน",
    courseIds: ["19x4uNv4RgED"], // ใช้ ID จริงของคอร์ส
    courseCount: 1,
    status: "published",
  },
  {
    id: "lp003",
    name: "การดูแลผู้ป่วยโรคหัวใจ",
    description: "เส้นทางเฉพาะทางสำหรับการดูแลผู้ป่วยโรคหัวใจและหลอดเลือด",
    courseIds: ["WnH5F0qZF938", "QlH3I647MCuN", "19x4uNv4RgED"], // ใช้ ID จริงของคอร์ส
    courseCount: 3,
    status: "draft",
  },
  {
    id: "lp004",
    name: "การดูแลผู้สูงอายุ",
    description: "เส้นทางการเรียนรู้เฉพาะด้านการดูแลผู้สูงอายุและโรคเรื้อรัง",
    courseIds: ["QlH3I647MCuN", "19x4uNv4RgED"], // ใช้ ID จริงของคอร์ส
    courseCount: 2,
    status: "published",
  },
  {
    id: "lp005",
    name: "การดูแลแผลและการติดเชื้อ",
    description: "เส้นทางการเรียนรู้การดูแลแผลและป้องกันการติดเชื้อในสถานพยาบาล",
    courseIds: ["WnH5F0qZF938"], // ใช้ ID จริงของคอร์ส
    courseCount: 1,
    status: "archived",
  },
]
