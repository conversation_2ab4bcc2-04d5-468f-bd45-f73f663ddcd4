import { getCoursesData } from "@/data/allCourses";
import Link from "next/link";

const About = () => {
  return (
    <div className="relative overflow-hidden ipad-pro:h-[70vh] lg:h-screen bg-[#D9E7E4] ">
      <div className="px-10 w-full h-full bg-white py-20 xl:allign-items-center justify-center flex flex-col">
        <h1 className="text-center text-2xl ibm-plex-sans-thai-semibold items-center ">
          About
        </h1>
        <h1 className="text-center text-4xl lg:text-5xl ibm-plex-sans-thai-semibold text-transparent bg-clip-text bg-gradient-to-r from-[#35B6BD] to-[#008268] m-5">
          CareAcademy
        </h1>

        <p className="text-center text-2xl ibm-plex-sans-thai-medium mt-4">
          E-learning การแพทย์เพื่อสุขภาพ
          สำหรับผู้เรียนทุกระดับ{" "}
        </p>

        <div className="py-12 mt-8 ibm-plex-sans-thai-regular">
          <div className="max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 px-4">
            {/* Card 1 */}
            <div className="rounded-lg shadow-lg overflow-hidden bg-[#2CBCA0] hover:shadow-xl transform hover:scale-110 transition-all duration-300">
              <div className="flex justify-center items-center h-48">
                <img
                  src="/e-med/img/notebook.png"
                  alt="Course Image"
                  className="h-48 object-cover"
                />
              </div>
              <div className="p-4">
                <h3 className="mt-2 text-lg font-semibold text-white">
                  เรียนรู้การวินิจฉัยและการรักษาโรคผ่าน E-learning
                </h3>
                <p className="text-white mt-2">
                  ใช้ E-learning
                  เพื่อฝึกฝนทักษะในการวินิจฉัยและรักษาผู้ป่วยโดยการเข้าถึงข้อมูลและเครื่องมือทางการแพทย์ที่ทันสมัยได้อย่างสะดวกและมีประสิทธิภาพ
                </p>
              </div>
            </div>

            {/* Card 2 */}
            <div className="rounded-lg shadow-lg overflow-hidden bg-[#2CBCA0] hover:shadow-xl transform hover:scale-110 transition-all duration-300">
              <div className="flex justify-center items-center h-48">
                <img
                  src="/e-med/img/graph.png"
                  alt="Course Image"
                  className="h-48 object-cover"
                />
              </div>
              <div className="p-4">
                <h3 className="mt-2 text-lg font-semibold text-white">
                  พัฒนาทักษะทางการแพทย์และสุขภาพผ่าน E-learning
                </h3>
                <p className="text-white mt-2">
                  เรียนรู้และพัฒนาทักษะการดูแลผู้ป่วยและการใช้เครื่องมือการแพทย์ผ่าน
                  E-learning ที่สะดวกและยืดหยุ่น
                </p>
              </div>
            </div>

            {/* Card 3 */}
            <div className="rounded-lg shadow-lg overflow-hidden bg-[#2CBCA0] hover:shadow-xl transform hover:scale-110 transition-all duration-300">
              <div className="flex justify-center items-center h-48">
                <img
                  src="/e-med/img/graph2.png"
                  alt="Course Image"
                  className="object-cover"
                />
              </div>
              <div className="p-4 ">
                <h3 className="mt-2 text-lg font-semibold text-white">
                  เตรียมความพร้อมในเทคโนโลยีด้านการแพทย์และสุขภาพ
                </h3>
                <p className="text-white mt-2">
                  ฝึกใช้เทคโนโลยีทางการแพทย์ เช่น AI และเครื่องมือทางการแพทย์ผ่าน
                  E-learning เพื่อเพิ่มประสิทธิภาพในการรักษาและวินิจฉัย
                </p>
              </div>
            </div>

            {/* Card 4 */}
            <div className="rounded-lg shadow-lg overflow-hidden bg-[#2CBCA0] hover:shadow-xl transform hover:scale-110 transition-all duration-300">
              <div className="flex justify-center items-center h-48">
                <img
                  src="/e-med/img/character.png"
                  alt="Course Image"
                  className="h-48 object-contain"
                />
              </div>
              <div className="p-4">
                <h3 className="mt-2 text-lg font-semibold text-white">
                  พัฒนาแนวทางการศึกษาด้านการแพทย์ให้สอดคล้องกับเทคโนโลยีใหม่
                </h3>
                <p className="text-white mt-2">
                  พัฒนาหลักสูตรและการศึกษาที่สอดคล้องกับการเปลี่ยนแปลงของเทคโนโลยีการแพทย์ผ่าน
                  E-learning ที่มีความยืดหยุ่น
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
     

      {/* 4 card */}
    </div>
  );
};

export default About;