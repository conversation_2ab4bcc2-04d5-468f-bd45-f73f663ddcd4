"use client"

import { TrendingUp, TrendingDown, Users, Award, BookOpen } from "lucide-react"

export default function GrowthMetrics() {
  const metrics = [
    {
      title: "นักเรียนใหม่",
      current: 89,
      previous: 76,
      period: "สัปดาห์นี้",
      icon: Users,
      color: "blue",
    },
    {
      title: "คอร์สที่เสร็จสิ้น",
      current: 156,
      previous: 142,
      period: "สัปดาห์นี้",
      icon: BookOpen,
      color: "green",
    },
    {
      title: "ใบรับรองที่ออก",
      current: 67,
      previous: 54,
      period: "สัปดาห์นี้",
      icon: Award,
      color: "yellow",
    },
  ]

  const monthlyMetrics = [
    {
      title: "นักเรียนใหม่",
      current: 342,
      previous: 289,
      period: "เดือนนี้",
      icon: Users,
      color: "blue",
    },
    {
      title: "คอร์สที่เสร็จสิ้น",
      current: 623,
      previous: 567,
      period: "เดือนนี้",
      icon: BookOpen,
      color: "green",
    },
    {
      title: "ใบรับรองที่ออก",
      current: 278,
      previous: 234,
      period: "เดือนนี้",
      icon: Award,
      color: "yellow",
    },
  ]

  const calculateGrowth = (current: number, previous: number) => {
    const growth = ((current - previous) / previous) * 100
    return {
      percentage: Math.abs(growth).toFixed(1),
      isPositive: growth > 0,
    }
  }

  const getColorClasses = (color: string) => {
    switch (color) {
      case "blue":
        return {
          bg: "bg-blue-50",
          icon: "text-blue-500",
          border: "border-blue-200",
        }
      case "green":
        return {
          bg: "bg-green-50",
          icon: "text-green-500",
          border: "border-green-200",
        }
      case "yellow":
        return {
          bg: "bg-yellow-50",
          icon: "text-yellow-500",
          border: "border-yellow-200",
        }
      default:
        return {
          bg: "bg-gray-50",
          icon: "text-gray-500",
          border: "border-gray-200",
        }
    }
  }

  const MetricCard = ({ metric, isMonthly = false }: { metric: any, isMonthly?: boolean }) => {
    const growth = calculateGrowth(metric.current, metric.previous)
    const colors = getColorClasses(metric.color)
    const IconComponent = metric.icon

    return (
      <div className={`p-4 rounded-lg border ${colors.border} ${colors.bg}`}>
        <div className="flex items-center justify-between mb-2">
          <IconComponent size={20} className={colors.icon} />
          <div className="flex items-center">
            {growth.isPositive ? (
              <TrendingUp size={16} className="text-green-500 mr-1" />
            ) : (
              <TrendingDown size={16} className="text-red-500 mr-1" />
            )}
            <span className={`text-sm font-medium ${growth.isPositive ? "text-green-600" : "text-red-600"}`}>
              {growth.isPositive ? "+" : "-"}{growth.percentage}%
            </span>
          </div>
        </div>
        <div>
          <h3 className="text-lg font-bold text-gray-800">{metric.current}</h3>
          <p className="text-sm text-gray-600">{metric.title}</p>
          <p className="text-xs text-gray-500 mt-1">{metric.period}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full space-y-6">
      {/* Weekly Metrics */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3">การเติบโตรายสัปดาห์</h3>
        <div className="grid grid-cols-1 gap-3">
          {metrics.map((metric, index) => (
            <MetricCard key={`weekly-${index}`} metric={metric} />
          ))}
        </div>
      </div>

      {/* Monthly Metrics */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3">การเติบโตรายเดือน</h3>
        <div className="grid grid-cols-1 gap-3">
          {monthlyMetrics.map((metric, index) => (
            <MetricCard key={`monthly-${index}`} metric={metric} isMonthly />
          ))}
        </div>
      </div>

      {/* Growth Summary */}
      <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
        <h4 className="font-medium text-blue-800 mb-2">สรุปการเติบโต</h4>
        <div className="space-y-2 text-sm text-blue-700">
          <div className="flex justify-between">
            <span>อัตราการเติบโตเฉลี่ย (รายสัปดาห์):</span>
            <span className="font-semibold">+15.2%</span>
          </div>
          <div className="flex justify-between">
            <span>อัตราการเติบโตเฉลี่ย (รายเดือน):</span>
            <span className="font-semibold">+18.7%</span>
          </div>
          <div className="flex justify-between">
            <span>แนวโน้ม:</span>
            <span className="font-semibold text-green-600">เติบโตอย่างต่อเนื่อง</span>
          </div>
        </div>
      </div>
    </div>
  )
}
