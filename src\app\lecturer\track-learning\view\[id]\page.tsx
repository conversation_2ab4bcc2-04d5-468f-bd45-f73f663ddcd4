import React from "react"
import LecturerLayout from '@/components/lecturer/layout'
import LearningTrackTable from '@/components/lecturer/learning-trank'

interface Props {
  params: Promise<{ id: string }>
}

export default async function TrackLearningDetailPage({ params }: Props) {
  const { id } = await params

  return (
    <LecturerLayout>
      <div className="mx-auto">
        <div>
          <LearningTrackTable courseId={id} />
        </div>
      </div>
    </LecturerLayout>
  )
}
