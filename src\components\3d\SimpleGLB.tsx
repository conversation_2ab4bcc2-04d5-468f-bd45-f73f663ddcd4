'use client';

import React, { Suspense, useRef, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Environment, Html, useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface SimpleGLBProps {
  modelPath: string;
  scale?: number;
  autoRotate?: boolean;
}

const ModelLoader = () => (
  <Html center>
    <div className="animate-pulse text-white text-xs opacity-60">Loading...</div>
  </Html>
);

const ModelError = () => (
  <Html center>
    <div className="text-red-200 text-xs opacity-80">Model Error</div>
  </Html>
);

const SimpleModel: React.FC<{ modelPath: string; scale: number; autoRotate: boolean }> = ({ 
  modelPath, 
  scale, 
  autoRotate 
}) => {
  const groupRef = useRef<THREE.Group>(null);
  const [modelLoaded, setModelLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  const gltf = useGLTF(modelPath);

  useEffect(() => {
    try {
      if (gltf?.scene) {
        gltf.scene.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.castShadow = true;
            child.receiveShadow = true;
          }
        });
        setModelLoaded(true);
        setHasError(false);
      }
    } catch (error) {
      console.error('Model setup error:', error);
      setHasError(true);
    }
  }, [gltf]);

  useFrame(() => {
    if (groupRef.current && autoRotate && modelLoaded) {
      groupRef.current.rotation.y += 0.008;
    }
  });

  if (hasError) {
    return <ModelError />;
  }

  if (!gltf?.scene || !modelLoaded) {
    return <ModelLoader />;
  }

  return (
    <group ref={groupRef} scale={scale} position={[0, -0.2, 0]}>
      <primitive object={gltf.scene} />
    </group>
  );
};

const SimpleGLB: React.FC<SimpleGLBProps> = ({ 
  modelPath, 
  scale = 1, 
  autoRotate = true 
}) => {
  return (
    <div className="w-full h-full">
      <Canvas
        camera={{ position: [0, 0, 2], fov: 60 }}
        gl={{ antialias: true, alpha: true }}
      >
        <ambientLight intensity={0.6} />
        <directionalLight position={[2, 2, 2]} intensity={0.8} />
        <Environment preset="apartment" />
        
        <Suspense fallback={<ModelLoader />}>
          <SimpleModel 
            modelPath={modelPath}
            scale={scale}
            autoRotate={autoRotate}
          />
        </Suspense>
      </Canvas>
    </div>
  );
};

export default SimpleGLB;
