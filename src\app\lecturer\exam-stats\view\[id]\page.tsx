"use client"

import LecturerLayout from '@/components/lecturer/layout'
import ExamTrack from '@/components/lecturer/exam-track'
import { ArrowLeft, FileChartColumnIncreasing } from 'lucide-react'
import Link from 'next/link'
import { lecturerExamService } from '@/hook/lecturerService'
import React, { useEffect, useState } from 'react'

interface ExamDetailPageProps {
  params: {
    id: string
  }
}

export default function ExamDetailPage({ params }: ExamDetailPageProps) {
  // Unwrap params if it's a Promise (Next.js 14+)
  // @ts-ignore
  const actualParams: { id: string } = typeof params.then === 'function' ? React.use(params) : params
  const examId = actualParams.id

  const [exam, setExam] = useState<any>(null)
  const [examData, setExamData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let ignore = false
    setLoading(true)
    setError(null)
    Promise.all([
      lecturerExamService.getExamStatsDetails(examId),
      lecturerExamService.getExamTrackingDetails(examId)
    ])
      .then(([examDetail, examTrack]) => {
        if (!ignore) {
          setExam({
            exam_name: examDetail.exam_name,
            course_name: examDetail.course_name,
            status: examDetail.status
          })
          setExamData(examTrack)
        }
      })
      .catch(() => {
        if (!ignore) setError('ไม่พบข้อมูลข้อสอบหรือเกิดข้อผิดพลาดในการโหลดข้อมูล')
      })
      .finally(() => { if (!ignore) setLoading(false) })
    return () => { ignore = true }
  }, [examId])

  if (loading) {
    return (
      <LecturerLayout>
        <div className="min-h-screen flex items-center justify-center px-4 py-8">
          <div className="w-full max-w-4xl mx-auto flex flex-col gap-6">
            <div className="relative bg-gradient-to-r from-[#293D97] via-[#3949ab] to-[#5c6bc0] rounded-3xl p-6 sm:p-8 text-white overflow-hidden shadow-2xl">
              <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
              <div className="relative z-10 text-center">
                <div className="flex flex-col items-center gap-3">
                  <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm">
                    <FileChartColumnIncreasing className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10" strokeWidth={2.2} />
                  </div>
                  <div>
                    <h1 className="text-lg sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-1">กำลังโหลดข้อมูลข้อสอบ...</h1>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </LecturerLayout>
    )
  }

  if (error || !exam || !examData) {
    return (
      <LecturerLayout>
        <div className="min-h-screen flex items-center justify-center px-4 py-8">
          <div className="w-full max-w-4xl mx-auto flex flex-col gap-6">
            <div className="relative bg-gradient-to-r from-[#293D97] via-[#3949ab] to-[#5c6bc0] rounded-3xl p-6 sm:p-8 text-white overflow-hidden shadow-2xl">
              <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
              <div className="relative z-10 text-center">
                <div className="flex flex-col items-center gap-3">
                  <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm">
                    <FileChartColumnIncreasing className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10" strokeWidth={2.2} />
                  </div>
                  <div>
                    <h1 className="text-lg sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-1">ไม่พบข้อมูลข้อสอบ</h1>
                    <p className="text-sm sm:text-lg md:text-xl lg:text-xl opacity-90 font-medium">
                      ข้อสอบที่ระบุไม่มีอยู่ในระบบ
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-lg border p-8 text-center shadow-lg">
              <p className="text-gray-500 mb-4">ไม่พบข้อมูลข้อสอบที่ต้องการ</p>
              <Link
                href="/lecturer/exam-stats"
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                กลับไปยังรายการข้อสอบ
              </Link>
            </div>
          </div>
        </div>
      </LecturerLayout>
    )
  }

  return (
    <LecturerLayout>
      <div className="min-h-screen  justify-center px-4 py-8">
        <div className="w-full max-w-7xl mx-auto flex flex-col gap-6">
          {/* Header */}
          {/* ...existing code... */}
          {/* Content */}
          <div className="">
            <ExamTrack data={examData} exam={exam} examId={examId} />
          </div>
        </div>
      </div>
    </LecturerLayout>
  )
}
