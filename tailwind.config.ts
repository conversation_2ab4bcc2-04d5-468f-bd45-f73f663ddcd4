import type { Config } from "tailwindcss";
import daisyui from "daisyui";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['"Noto Sans Thai"', 'sans-serif'],
        noto: ['"Noto Sans Thai"', 'sans-serif'],
      },
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: {
          50: '#f0f9f7',
          100: '#dbf0eb',
          200: '#bae1d8',
          300: '#8ccbc0',
          400: '#5bb0a4',
          500: '#008067',
          600: '#007055',
          700: '#005d46',
          800: '#004a38',
          900: '#003d2e',
        },
        medical: {
          primary: '#008067',
          secondary: '#00a078',
          accent: '#2CBCA0',
          light: '#f8fffe',
          gray: '#f5f5f5',
        }
      },
      screens: { 
        'ipad': { min: '768px', max: '1199px'},
        'ipad-pro': { min: '1024px', max: '1199px'},
        "ipad-mini-landscape": { raw: "(min-width: 1024px) and (max-width: 1179px) and (orientation: landscape)" },
        "ipad-air-landscape": { raw: "(min-width: 1180px) and (max-width: 1366px) and (orientation: landscape)" },
        'ipad-pro-landscape': {'raw': '(min-width: 1366px) and (max-width: 1650px) and (orientation: landscape)'},
      },
      animation: {
        'fadeInUp': 'fadeInUp 0.6s ease-out forwards',
        'shimmer': 'shimmer 2s linear infinite',
        'slideDown': 'slideDown 0.3s ease-out',
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        fadeInUp: {
          '0%': {
            opacity: '0',
            transform: 'translateY(30px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        },
        shimmer: {
          '0%': {
            transform: 'translateX(-100%)'
          },
          '100%': {
            transform: 'translateX(200%)'
          }
        },
        slideDown: {
          '0%': {
            opacity: '0',
            transform: 'translateY(-10px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        },
        float: {
          '0%, 100%': {
            transform: 'translateY(0px)'
          },
          '50%': {
            transform: 'translateY(-10px)'
          }
        }
      }
    },
  },
  plugins: [
    daisyui
  ],
} satisfies Config;
