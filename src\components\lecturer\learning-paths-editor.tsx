"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core"
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from "@dnd-kit/sortable"
import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { Plus, GripVertical, X, ChevronDown, ArrowLeft, ChevronLeft } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { courseService } from "@/hook/courseService"
import { postPath, putPath, deletePath, getPathById } from "@/hook/pathService"
import { showSuccessAlert, showErrorAlert, showDeleteConfirmDialog, showConfirmDialog } from "@/lib/sweetAlert"

interface LearningPathEditorProps {
  mode: "create" | "edit"
  pathId?: string
}

interface SelectedCourse {
  id: string
  slug: string
  name: string
  description: string
}

interface ModalState {
  isOpen: boolean
  type: "save" | "delete" | "success" | "error"
  title: string
  message: string
  onConfirm?: () => void
}

// Sortable Course Item Component
function SortableCourseItem({
  course,
  onRemove,
}: {
  course: SelectedCourse
  onRemove: (id: string) => void
}) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: course.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const handleRemoveClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    console.log("Remove button clicked for course:", course.id, course.name)
    onRemove(course.id)
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center gap-4 ${
        isDragging ? "opacity-50" : ""
      }`}
    >
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab hover:cursor-grabbing text-gray-400 hover:text-gray-600"
      >
        <GripVertical className="h-5 w-5" />
      </div>

      <div className="flex-1">
        <h4 className="font-medium text-black">{course.name}</h4>
        <p className="text-sm text-gray-500 mt-1 line-clamp-2">{course.description}</p>
      </div>

      <button
        onClick={handleRemoveClick}
        className="text-red-500 hover:text-red-700 p-1 hover:bg-red-50 rounded transition-colors"
        type="button"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  )
}

export default function LearningPathEditor({ mode, pathId }: LearningPathEditorProps) {
  const router = useRouter()
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    status: true, // boolean: true = active (ใช้งาน), false = inactive (ไม่ใช้งาน)
  })
  const [courses, setCourses] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [apiLoading, setApiLoading] = useState(false)
  const [selectedCourses, setSelectedCourses] = useState<SelectedCourse[]>([])
  const [showCourseDropdown, setShowCourseDropdown] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [modal, setModal] = useState<ModalState>({
    isOpen: false,
    type: "save",
    title: "",
    message: "",
  })

  // Fetch courses and path data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        
        // Fetch courses
        const coursesData = await courseService.getCourses()
        console.log("All courses loaded:", coursesData)
        console.log("Course slugs:", coursesData.map(c => ({ 
          id: c.id, 
          slug: c.slug
        })))
        setCourses(coursesData)

        // If edit mode and pathId is provided, fetch path data
        if (mode === "edit" && pathId) {
          try {
            const pathData = await getPathById(pathId)
            
            setFormData({
              name: pathData.name || "",
              description: pathData.description || "",
              status: pathData.status, // Keep boolean as is
            })

            // Convert path courses to selected courses format
            if (pathData.path && Array.isArray(pathData.path)) {
              const selectedCoursesData = pathData.path.map(course => {
                // course_id from API contains the slug
                const courseSlug = course.course_id
                const courseDetails = coursesData.find(c => c.slug === courseSlug)
                
                return {
                  id: courseSlug,
                  slug: courseSlug,
                  name: course.course_name || (courseDetails ? courseDetails.course_name : "Unknown Course"),
                  description: courseDetails ? courseDetails.course_description || "" : ""
                }
              })
              setSelectedCourses(selectedCoursesData)
              console.log("Selected courses loaded from API:", selectedCoursesData)
            }
          } catch (pathError) {
            console.error("Failed to fetch path data:", pathError)
            showErrorAlert("เกิดข้อผิดพลาด", "ไม่สามารถโหลดข้อมูลเส้นทางการเรียนรู้ได้")
            setError("ไม่สามารถโหลดข้อมูลเส้นทางการเรียนรู้ได้")
          }
        }
      } catch (err) {
        console.error("Failed to fetch courses:", err)
        setError("ไม่สามารถโหลดคอร์สได้")
        showErrorAlert("เกิดข้อผิดพลาด", "ไม่สามารถโหลดข้อมูลคอร์สได้")
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [mode, pathId])

  // DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  // Handle drag end
  function handleDragEnd(event: any) {
    const { active, over } = event

    if (active.id !== over.id) {
      setSelectedCourses((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id)
        const newIndex = items.findIndex((item) => item.id === over.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  // Get available courses (not already selected)
  const availableCourses = (courses || []).filter(
    (course) => {
      const courseSlug = course.slug
      // Check only by slug
      const isNotSelected = !selectedCourses.some((selected) => selected.slug === courseSlug)
      const courseName = course.course_name || ""
      const matchesSearch = courseName.toLowerCase().includes(searchTerm.toLowerCase())
      
      return isNotSelected && matchesSearch
    }
  )

  // Add course to path
  const addCourse = (courseSlug: string) => {
    const course = courses.find((c) => c.slug === courseSlug)
    if (!course) {
      showErrorAlert("เกิดข้อผิดพลาด", "ไม่พบคอร์สที่เลือก")
      return
    }

    // Check for duplicates by slug only
    const alreadySelected = selectedCourses.some((selected) => selected.slug === courseSlug)
    
    if (alreadySelected) {
      showErrorAlert("คอร์สซ้ำ", "คอร์สนี้ถูกเพิ่มในเส้นทางการเรียนรู้แล้ว")
      return
    }
    
    const newCourse = {
      id: courseSlug,
      slug: courseSlug,
      name: course.course_name || "ไม่มีชื่อคอร์ส",
      description: course.course_description || "",
    }
    
    setSelectedCourses((prev) => [...prev, newCourse])
    setShowCourseDropdown(false)
    setSearchTerm("")
    showSuccessAlert("เพิ่มคอร์สสำเร็จ", `เพิ่มคอร์ส "${newCourse.name}" ในเส้นทางการเรียนรู้แล้ว`)
  }

  // Remove course from path with confirmation
  const removeCourse = (courseId: string) => {
    const course = selectedCourses.find((c) => c.id === courseId)

    if (course) {
      // Use SweetAlert for confirmation
      showDeleteConfirmDialog(`คอร์ส "${course.name}"`).then((result) => {
        if (result.isConfirmed) {
          setSelectedCourses((prev) => {
            const updatedCourses = prev.filter((c) => c.id !== courseId)
            console.log("Selected courses after removing:", updatedCourses)
            return updatedCourses
          })
          showSuccessAlert("ลบคอร์สสำเร็จ", `ลบคอร์ส "${course.name}" ออกจากเส้นทางการเรียนรู้เรียบร้อยแล้ว`)
        }
      }).catch((error) => {
        console.error("Error in delete confirmation:", error)
      })
    } else {
      showErrorAlert("เกิดข้อผิดพลาด", "ไม่พบคอร์สที่ต้องการลบ")
    }
  }

  // Handle save confirmation
  const handleSaveClick = async () => {
    if (!formData.name.trim()) {
      showErrorAlert("ข้อมูลไม่ครบถ้วน", "กรุณากรอกชื่อเส้นทางการเรียนรู้")
      return
    }

    if (!formData.description.trim()) {
      showErrorAlert("ข้อมูลไม่ครบถ้วน", "กรุณากรอกคำอธิบายเส้นทางการเรียนรู้")
      return
    }

    if (selectedCourses.length === 0) {
      showErrorAlert("ไม่มีคอร์สในเส้นทาง", "กรุณาเพิ่มคอร์สอย่างน้อย 1 คอร์สในเส้นทางการเรียนรู้")
      return
    }

    // Use SweetAlert confirmation dialog
    const confirmTitle = mode === "create" ? "สร้างเส้นทางการเรียนรู้" : "บันทึกการแก้ไข"
    const confirmMessage = mode === "create"
      ? `คุณต้องการสร้างเส้นทางการเรียนรู้ \"${formData.name}\" หรือไม่?`
      : `คุณต้องการบันทึกการแก้ไขเส้นทางการเรียนรู้ \"${formData.name}\" หรือไม่?`
    const result = await showConfirmDialog(confirmTitle, confirmMessage)
    if (result.isConfirmed) {
      handleSave()
    }
  }

  // Handle actual save with API
  const handleSave = async () => {
    try {
      setApiLoading(true)
      
      // Prepare data for API
      const pathData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        status: formData.status, // Already boolean
        path: selectedCourses.map(course => ({
          course_id: course.slug, // Use slug as course_id
          path_order: selectedCourses.findIndex(c => c.id === course.id) + 1, // 1-based index
        }))
      }

      if (mode === "create") {
        await postPath(pathData)
        showSuccessAlert("สร้างเส้นทางสำเร็จ", "สร้างเส้นทางการเรียนรู้ใหม่เรียบร้อยแล้ว")
      } else if (mode === "edit" && pathId) {
        await putPath(pathId, pathData)
        showSuccessAlert("บันทึกสำเร็จ", "บันทึกการแก้ไขเส้นทางการเรียนรู้เรียบร้อยแล้ว")
      }

      // Redirect back to paths list after success
      setTimeout(() => {
        router.push("/lecturer/learning-paths")
      }, 800)

    } catch (error) {
      console.error("Error saving path:", error)
      
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred"
      showErrorAlert(
        "เกิดข้อผิดพลาด", 
        mode === "create" 
          ? `ไม่สามารถสร้างเส้นทางการเรียนรู้ได้: ${errorMessage}` 
          : `ไม่สามารถบันทึกการแก้ไขได้: ${errorMessage}`
      )
    } finally {
      setApiLoading(false)
    }
  }

  // Handle delete path (only for edit mode)
  const handleDeletePath = async () => {
    if (mode !== "edit" || !pathId) return

    try {
      const result = await showDeleteConfirmDialog(formData.name)
      if (result.isConfirmed) {
        setApiLoading(true)
        await deletePath(pathId)
        showSuccessAlert("ลบสำเร็จ", `ลบเส้นทางการเรียนรู้ "${formData.name}" เรียบร้อยแล้ว`)
        
        setTimeout(() => {
          router.push("/lecturer/learning-paths")
        }, 800)
      }
    } catch (error) {
      console.error("Error deleting path:", error)
      showErrorAlert("เกิดข้อผิดพลาด", "ไม่สามารถลบเส้นทางการเรียนรู้ได้")
    } finally {
      setApiLoading(false)
    }
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSaveClick()
  }

  return (
    <>
      {loading ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center">
            <div className="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-gray-600 rounded-full" role="status" aria-label="loading">
              <span className="sr-only">Loading...</span>
            </div>
            <p className="mt-2 text-gray-600">กำลังโหลดข้อมูล...</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center text-red-600">
            <p className="text-lg font-medium">เกิดข้อผิดพลาด</p>
            <p className="mt-2">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              โหลดใหม่
            </button>
          </div>
        </div>
      ) : (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Back button and header */}
        <div className="flex items-center p-4 border-b border-gray-200">
          <button
            type="button"
            className="flex items-center text-gray-700 hover:text-black font-medium text-base mr-4"
            onClick={() => router.push("/lecturer/learning-paths")}
          >
            <ChevronLeft className="h-7 w-7" />
            
          </button>
          <h1 className="text-xl font-bold text-gray-800 ">
            {mode === "create" ? "สร้างเส้นทางการเรียนรู้ใหม่" : "แก้ไขเส้นทางการเรียนรู้"}
          </h1>
          <div className="flex space-x-3 ml-auto">
            {mode === "edit" && (
              <button
                type="button"
                onClick={handleDeletePath}
                disabled={apiLoading}
                className="px-4 py-2 border border-red-300 text-red-700 rounded-md hover:bg-red-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                ลบเส้นทางการเรียนรู้
              </button>
            )}
            {mode === "create" && (
              <Link href="/lecturer/learning-paths">
                
              </Link>
            )}
            <button
              type="button"
              onClick={handleSaveClick}
              disabled={apiLoading}
              className="px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {apiLoading 
                ? (mode === "create" ? "กำลังสร้าง..." : "กำลังบันทึก...") 
                : (mode === "create" ? "สร้างเส้นทางการเรียนรู้" : "บันทึกการแก้ไข")
              }
            </button>
          </div>
        </div>

        <form id="learning-path-form" onSubmit={handleSubmit} className="space-y-0">
          {/* Basic Information Section */}
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">ข้อมูลพื้นฐาน</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">ชื่อเส้นทางการเรียนรู้ *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                  placeholder="กรอกชื่อเส้นทางการเรียนรู้"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">สถานะ *</label>
                <div className="relative">
                  <select
                    value={formData.status ? "true" : "false"}
                    onChange={(e) => setFormData((prev) => ({ ...prev, status: e.target.value === "true" }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                  >
                    <option value="true">ใช้งาน</option>
                    <option value="false">ไม่ใช้งาน</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 pointer-events-none" />
                </div>
              </div>
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">คำอธิบาย *</label>
              <input
                type="text"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                placeholder="กรอกคำอธิบายเส้นทางการเรียนรู้"
                required
              />
            </div>
          </div>

          {/* Course Management Section */}
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-800">คอร์สในเส้นทางการเรียนรู้</h2>
              <span className="text-sm text-gray-500">{selectedCourses.length} คอร์ส</span>
            </div>

            {/* Selected Courses with Drag & Drop and Add Course integrated */}
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext items={selectedCourses.map((c) => c.id)} strategy={verticalListSortingStrategy}>
                <div className="space-y-3">
                  {selectedCourses.map((course) => (
                    <SortableCourseItem key={course.slug} course={course} onRemove={removeCourse} />
                  ))}

                  {/* Add Course Section - Smaller dashed area */}
                  <div className="bg-gray-100 border-2 border-dashed border-gray-400 rounded-lg py-3 px-4">
                    <div className="flex justify-center">
                      <button
                        type="button"
                        onClick={() => setShowCourseDropdown(!showCourseDropdown)}
                        className="flex items-center gap-2 px-8 py-2 bg-white hover:bg-gray-50 rounded-lg text-gray-700 transition-colors font-medium border border-gray-300 min-w-[280px]"
                      >
                        <Plus className="h-4 w-4" />
                        เพิ่มคอร์สเรียนในเส้นทาง
                      </button>
                    </div>

                    {/* Course Dropdown */}
                    {showCourseDropdown && (
                      <div className="relative mt-3">
                        <div className="relative mb-2">
                          <input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            placeholder="ค้นหาคอร์ส..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-white"
                          />
                        </div>

                        <div className="bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                          {availableCourses.length > 0 ? (
                            availableCourses.map((course) => (
                              <button
                                key={course.slug}
                                type="button"
                                onClick={() => addCourse(course.slug)}
                                className="w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                              >
                                <div className="font-medium text-black">{course.course_name || "ไม่มีชื่อคอร์ส"}</div>
                                <div className="text-sm text-gray-500 mt-1 line-clamp-2">{course.course_description || ""}</div>
                              </button>
                            ))
                          ) : (
                            <div className="px-4 py-3 text-gray-500 text-center">
                              {searchTerm ? "ไม่พบคอร์สที่ค้นหา" : "ไม่มีคอร์สที่สามารถเพิ่มได้"}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </SortableContext>
            </DndContext>

            {/* Empty state when no courses */}
            {selectedCourses.length === 0 && (
              <div className="text-center py-12 text-gray-500 mb-6">
                <div className="text-lg mb-2">ยังไม่มีคอร์สในเส้นทางการเรียนรู้</div>
                <div className="text-sm">คลิกปุ่ม "เพิ่มคอร์สเรียนในเส้นทางการเรียน" เพื่อเริ่มสร้างเส้นทางการเรียนรู้</div>
              </div>
            )}
          </div>
        </form>
      </div>
      )}
    </>
  )
}
