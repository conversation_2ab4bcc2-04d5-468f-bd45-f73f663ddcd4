"use client"

import React, { useState, useEffect } from "react"
import { lecturerService, TrackLearningDetailResponse, StudentDetail } from "@/hook/lecturerService"
import {
  ChevronLeft,
  ChevronRight,
  Search,
  BookOpen,
  GraduationCap,
  ChevronDown,
  Filter,
  CheckCircle,
  Clock,
  RotateCcw,
} from "lucide-react"
import clsx from "clsx"

interface LearningTrankTableProps {
  courseId?: string
}

const PAGE_SIZE = 9

export default function LearningTrackTable({ courseId }: LearningTrankTableProps) {
  const [trackingData, setTrackingData] = useState<TrackLearningDetailResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [search, setSearch] = useState("")
  const [page, setPage] = useState(1)
  const [statusFilter, setStatusFilter] = useState("")
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  // Fetch data from API
  useEffect(() => {
    const fetchTrackingData = async () => {
      if (!courseId) return

      try {
        setLoading(true)
        setError(null)
        const data = await lecturerService.getTrackLearningDetail(
          courseId,
          page,
          PAGE_SIZE,
          search,
          statusFilter
        )
        setTrackingData(data)
      } catch (err) {
        console.error('Error fetching tracking data:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch data')
      } finally {
        setLoading(false)
      }
    }

    fetchTrackingData()
  }, [courseId, page, search, statusFilter])

  // Reset page when search or status filter changes
  useEffect(() => {
    if (page !== 1) {
      setPage(1)
    }
  }, [search, statusFilter])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest(".dropdown-container")) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#008268]"></div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">เกิดข้อผิดพลาด: {error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-[#008268] text-white rounded-lg hover:bg-[#006854] transition-colors"
          >
            ลองใหม่
          </button>
        </div>
      </div>
    )
  }

  // Show not found state
  if (!trackingData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-gray-600">ไม่พบข้อมูลคอร์สนี้</p>
        </div>
      </div>
    )
  }

  const { course_overview: courseOverview, students, pagination } = trackingData

  return (
    <div className="">
      <div className="max-w-7xl mx-auto p-4 sm:p-6 w-full">
        <div className="relative bg-gradient-to-r from-[#008268] via-[#00a685] to-[#00b894] rounded-3xl p-6 sm:p-8 mb-8 text-white overflow-hidden shadow-2xl">
          <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

          <div className="relative z-10">
            <div className="flex flex-col sm:flex-row md:flex-row md:items-center gap-3 mb-4">
              <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm">
                <GraduationCap className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10" />
              </div>
              <div>
                <h1 className="text-lg sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-1">ติดตามผลรายบุคคล</h1>
                <p className="text-sm sm:text-lg md:text-xl lg:text-xl opacity-90 font-medium">
                  {courseOverview?.course_name || "-"}
                </p>
              </div>
            </div>

            {/* Progress Card - White card inside green card */}
            <div className="bg-white/15 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-4 md:p-6 mt-6">
              <div className="flex flex-col sm:flex-row md:flex-row md:items-start justify-between mb-4 gap-4">
                <div>
                  <div className="flex items-baseline gap-4">
                    <span className="text-4xl sm:text-5xl md:text-6xl font-bold text-white">{courseOverview?.total_students || 0}</span>
                    <span className="text-base sm:text-lg md:text-xl text-white/80 font-medium">ผู้เรียน</span>
                  </div>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="space-y-4 mb-4">
                <div className="flex gap-1 h-4 rounded-full overflow-hidden">
                  <div
                    className="bg-green-500 border-white/50 border-2 h-full"
                    style={{
                      width: courseOverview?.total_students ? `${(courseOverview.completed_students / courseOverview.total_students) * 100}%` : "0%",
                    }}
                  ></div>
                  <div
                    className="bg-yellow-500 border-2 border-white/50 h-full"
                    style={{
                      width: courseOverview?.total_students ? `${(courseOverview.in_progress_students / courseOverview.total_students) * 100}%` : "0%",
                    }}
                  ></div>
                  <div
                    className="bg-gray-300 border-2 border-white/50 h-full"
                    style={{
                      width: courseOverview?.total_students ? `${(courseOverview.not_started_students / courseOverview.total_students) * 100}%` : "0%",
                    }}
                  ></div>
                </div>
              </div>

              {/* Status Labels - Inline with numbers below */}
              <div className="flex flex-wrap items-start gap-4 md:gap-8">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                    <span className="text-white font-medium text-sm md:text-base">เรียนจบแล้ว</span>
                  </div>
                  <div className="text-xl sm:text-2xl md:text-3xl font-bold text-white">
                    {courseOverview?.completed_students || 0}
                  </div>
                </div>

                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                    <span className="text-white font-medium text-sm md:text-base">กำลังเรียน</span>
                  </div>
                  <div className="text-xl sm:text-2xl md:text-3xl font-bold text-white">
                    {courseOverview?.in_progress_students || 0}
                  </div>
                </div>

                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
                    <span className="text-white font-medium text-sm md:text-base">ยังไม่เริ่ม</span>
                  </div>
                  <div className="text-xl sm:text-2xl md:text-3xl font-bold text-white">
                    {courseOverview?.not_started_students || 0}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search, Filter, Content and Pagination - Wrapped in white background */}
        <div className="bg-white/50 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-6 sm:p-8">
          {/* Search and Filter Section */}
          <div className="flex flex-col sm:flex-row md:flex-row lg:flex-row justify-between items-start lg:items-center gap-4 sm:gap-6 mb-8">
            <div className="relative w-full sm:w-auto lg:w-auto">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                <Search className="h-5 w-5" />
              </div>
              <input
                type="text"
                placeholder="ค้นหาชื่อนักเรียน..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-12 pr-6 py-3 sm:py-4 border-2 border-gray-200 rounded-2xl w-full sm:w-[400px] md:w-[350px] lg:w-[600px] bg-white/80 backdrop-blur-sm shadow-lg focus:outline-none focus:ring-4 focus:ring-[#008268]/20 focus:border-[#008268] transition-all duration-300 text-gray-700 placeholder-gray-400 text-sm sm:text-base"
              />
            </div>

            <div className="flex items-center gap-2 sm:gap-4 w-full sm:w-auto lg:w-auto">
              <div className="relative dropdown-container w-full sm:w-auto">
                <button
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="flex items-center justify-between gap-2 bg-white/80 backdrop-blur-sm border-2 border-gray-200 rounded-2xl px-3 sm:px-4 py-3 font-semibold text-gray-700 shadow-lg focus:outline-none focus:ring-4 focus:ring-[#008268]/20 focus:border-[#008268] transition-all duration-300 w-full sm:w-auto min-w-[120px] sm:min-w-[160px] md:min-w-[140px] lg:min-w-[200px]"
                >
                  <div className="flex items-center gap-2">
                    {statusFilter === "" && (
                      <>
                        <Filter className="h-4 w-4 sm:h-5 sm:w-5" />
                        <span className="hidden sm:inline">ทุกสถานะ</span>
                      </>
                    )}
                    {statusFilter === "completed" && (
                      <>
                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                        <span className="hidden sm:inline">เรียนจบแล้ว</span>
                      </>
                    )}
                    {statusFilter === "not-completed" && (
                      <>
                        <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                        <span className="hidden sm:inline">ยังไม่สำเร็จ</span>
                      </>
                    )}
                  </div>
                  <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                </button>

                {isDropdownOpen && (
                  <div className="absolute top-full left-0 mt-2 w-full sm:w-48 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl z-10">
                    <button
                      onClick={() => {
                        setStatusFilter("")
                        setIsDropdownOpen(false)
                      }}
                      className="flex items-center gap-2 w-full px-3 sm:px-4 py-3 text-left hover:bg-gray-50 rounded-t-2xl transition-colors text-sm sm:text-base"
                    >
                      <Filter className="h-4 w-4 sm:h-5 sm:w-5" />
                      <span>ทุกสถานะ</span>
                    </button>
                    <button
                      onClick={() => {
                        setStatusFilter("completed")
                        setIsDropdownOpen(false)
                      }}
                      className="flex items-center gap-2 w-full px-3 sm:px-4 py-3 text-left hover:bg-gray-50 transition-colors text-sm sm:text-base"
                    >
                      <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                      <span>เรียนจบแล้ว</span>
                    </button>
                    <button
                      onClick={() => {
                        setStatusFilter("not-completed")
                        setIsDropdownOpen(false)
                      }}
                      className="flex items-center gap-2 w-full px-3 sm:px-4 py-3 text-left hover:bg-gray-50 rounded-b-2xl transition-colors text-sm sm:text-base"
                    >
                      <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                      <span>ยังไม่สำเร็จ</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Students List */}
          {students.length === 0 ? (
            <div className="text-center py-20">
              <div className="bg-gray-50 rounded-3xl p-12 shadow-xl border border-gray-100">
                <BookOpen className="h-20 w-20 text-gray-300 mx-auto mb-6" />
                <p className="text-2xl text-gray-400 font-medium">ไม่พบนักเรียนในคอร์สนี้</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {students.map((student, idx) => {
                let status = "ยังไม่เริ่ม"
                let statusColor = "bg-gray-100 text-gray-600 border-gray-200"
                let progressColor = "#d1d5db"

                if (student.status === "completed") {
                  status = "เรียนจบแล้ว"
                  statusColor = "bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border-green-200"
                  progressColor = "#22c55e"
                } else if (student.progress > 0) {
                  status = "กำลังเรียน"
                  statusColor = "bg-gradient-to-r from-blue-100 to-sky-100 text-blue-700 border-blue-200"
                  progressColor = "#3b82f6"
                } else {
                  status = "ยังไม่เริ่ม"
                  statusColor = "bg-gray-100 text-gray-600 border-gray-200"
                  progressColor = "#d1d5db"
                }

                return (
                  <div
                    key={student.user_slug}
                    className="bg-white rounded-2xl shadow-lg border border-gray-200 p-4 sm:p-6 hover:shadow-xl transition-shadow duration-300 relative"
                  >
                    {/* Last Login - Mobile top right */}
                    <div className="absolute top-4 right-4 sm:hidden">
                      <div className="flex items-center gap-1">
                        <RotateCcw className="h-3 w-3 text-[#c92828]" />
                        <span className="text-xs text-gray-600">
                          {new Date(student.last_login || "2024-12-28 12:40:00").toLocaleDateString("th-TH", {
                            day: "numeric",
                            month: "short",
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </span>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row lg:flex-row items-start sm:items-center lg:items-center gap-4 sm:gap-6 lg:gap-8 md:grid md:grid-cols-2 md:gap-6">
                      {/* Left Column - Avatar, Name, Status */}
                      <div className="flex flex-col sm:flex-row lg:flex-row items-start sm:items-center lg:items-center gap-4 sm:gap-6 lg:gap-8 md:flex md:flex-row md:items-center md:gap-4">
                        {/* Avatar */}
                        <div className="flex-shrink-0">
                          <img
                            src={student.avatar || "/images/progress-card.png"}
                            alt={student.name}
                            className="w-16 h-16 md:w-16 md:h-16 lg:w-16 lg:h-16 rounded-full object-cover border-2 border-gray-200"
                          />
                        </div>

                        {/* Student Info */}
                        <div className="flex-1 min-w-0">
                          <h3 className="text-lg md:text-lg lg:text-lg font-bold text-gray-800 mb-1">
                            {student.name}
                          </h3>
                          <p className="text-sm text-gray-600 mb-2">
                            {student.position}
                          </p>
                          <div>
                            <span
                              className={clsx(
                                "px-3 py-1 rounded-lg text-sm md:text-sm lg:text-sm font-semibold border",
                                statusColor,
                              )}
                            >
                              {status}
                            </span>
                          </div>
                        </div>

                        {/* Current Lesson - Show on lg only, in left column */}
                        <div className="hidden lg:block flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <BookOpen className="h-4 w-4 text-[#008268]" />
                            <span className="text-sm font-medium text-gray-600">บทเรียนล่าสุด</span>
                          </div>
                          <p className="text-gray-800 font-medium text-sm">{student.last_finish_content_lesson || "ยังไม่เริ่ม"}</p>
                        </div>
                      </div>

                      {/* Right Column - Details */}
                      <div className="flex flex-col sm:flex-row lg:flex-row gap-4 sm:gap-6 lg:gap-8 flex-1 md:flex md:flex-col md:gap-4">
                        {/* Current Lesson - Show on non-lg */}
                        <div className="flex-1 min-w-0 w-full sm:w-auto md:w-full lg:hidden">
                          <div className="flex items-center gap-2 mb-1">
                            <BookOpen className="h-4 w-4 text-[#008268]" />
                            <span className="text-sm font-medium text-gray-600">บทเรียนล่าสุด</span>
                          </div>
                          <p className="text-gray-800 font-medium truncate">{student.last_finish_content_lesson || "ยังไม่เริ่ม"}</p>
                        </div>

                        {/* Last Login - Hidden on mobile, shown on tablet+ */}
                        <div className="hidden sm:flex flex-shrink-0 w-full sm:w-40 md:w-full lg:w-40">
                          <div className="w-full">
                            <div className="flex items-center gap-2 mb-1">
                              <RotateCcw className="h-4 w-4 text-[#cc2a2a]" />
                              <span className="text-sm font-medium text-gray-600">ล็อกอินล่าสุด</span>
                            </div>
                            <p className="text-gray-800 font-medium text-sm">
                              {new Date(student.last_login || "2024-12-28 12:40:00").toLocaleDateString("th-TH", {
                                year: "numeric",
                                month: "short",
                                day: "numeric",
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </p>
                          </div>
                        </div>

                        {/* Progress - Hidden on mobile, rightmost on lg */}
                        <div className="hidden sm:flex flex-shrink-0 w-full sm:w-48 md:w-full lg:w-48">
                          <div className="w-full">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-gray-600">ความคืบหน้า</span>
                              <span className="text-lg font-bold text-gray-800">{student.progress}%</span>
                            </div>
                            <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="h-full rounded-full"
                                style={{
                                  width: `${student.progress}%`,
                                  backgroundColor: progressColor,
                                }}
                              />
                            </div>
                          </div>
                        </div>

                        {/* Mobile Progress Bar */}
                        <div className="sm:hidden w-full">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-600">ความคืบหน้า</span>
                            <span className="text-lg font-bold text-gray-800">{student.progress}%</span>
                          </div>
                          <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className="h-full rounded-full"
                              style={{
                                width: `${student.progress}%`,
                                backgroundColor: progressColor,
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}

          {/* Pagination */}
          {pagination.total_pages > 1 && (
            <div className="flex flex-col sm:flex-row md:flex-row lg:flex-row items-center justify-between mt-8 gap-4 sm:gap-6 md:gap-8 lg:gap-8">
              <div className="flex items-center gap-2 text-sm text-gray-600 order-2 sm:order-1 md:order-1 lg:order-1">
                <span className="hidden sm:block md:block lg:block">
                  แสดง {(pagination.page - 1) * pagination.limit + 1} - {Math.min(pagination.page * pagination.limit, pagination.total_items)} จาก{" "}
                  {pagination.total_items} รายการ
                </span>
                <span className="lg:hidden">{pagination.total_items} รายการ</span>
              </div>

              <div className="flex items-center gap-2 order-1 sm:order-2 md:order-2 lg:order-2">
                <button
                  onClick={() => setPage(1)}
                  disabled={page === 1}
                  className="px-3 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>

                <div className="flex items-center gap-1">
                  <span className="text-sm text-gray-600">
                    หน้า {pagination.page} จาก {pagination.total_pages}
                  </span>
                </div>

                <button
                  onClick={() => setPage(page + 1)}
                  disabled={page === pagination.total_pages}
                  className={clsx(
                    "px-3 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",
                    {
                      "opacity-50 cursor-not-allowed": page === pagination.total_pages,
                    },
                  )}
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
