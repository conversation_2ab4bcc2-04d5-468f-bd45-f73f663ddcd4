"use client"

import { useState, useEffect } from "react"
import { getCoursesData } from "@/data/allCourses"
import type { CourseType } from "@/types/courses"
import { useParams } from "next/navigation"
import CourseTitle from "@/components/coursecompo/CourseTitle"
import CourseDetails from "../coursecompo/CourseDetail"
import RecommendedCourses from "../coursecompo/randomCourse"
import Link from "next/link"
import Footer from "../footer"
import CourseNav from "../coursecompo/Navtab"
import CourseDetailsCard from "../coursecompo/CourseDetailBar"
import { getCourseBySlug, type CourseSlugResponse } from "@/hook/displaycourseService"

interface AllCoursesPageProps {
    slug?: string;
}

export default function AllCoursesPage({ slug }: AllCoursesPageProps) {
    const [coursesData, setCoursesData] = useState<CourseType | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState("");

    const params = useParams();

    // Function to map API response to CourseType
    const mapToCourseType = (apiResponse: CourseSlugResponse): CourseType => {
        return {
            id: apiResponse.slug,
            name: apiResponse.course_name,
            description: apiResponse.course_description,
            instruction: apiResponse.course_instruction,
            level: apiResponse.course_difficulty,
            difficulty: apiResponse.course_difficulty,
            time: parseInt(apiResponse.course_duration) || 0,
            coverImage: apiResponse.course_picture,
            certify: apiResponse.course_certificate === "1",
            status: "active",
            title: apiResponse.course_name,
            flexibility: {
                duration: apiResponse.course_duration + " ชั่วโมง" || "ไม่ระบุ",
                description: "เรียนผ่านวิดีโอคอร์ส สามารถเรียนได้ตามสะดวก"
            },
            teacher: {
                name: apiResponse.lecturer_name,
                description: apiResponse.lecturer_position,
                avatar: apiResponse.lecturer_picture
            },
            lesson: apiResponse.lesson_details.map((lesson, index) => ({
                id: `lesson-${index}`,
                name: lesson.lesson_name,
                description: lesson.lesson_description,
                time: 0,
                content: lesson.content.map((content, contentIndex) => ({
                    id: `content-${index}-${contentIndex}`,
                    name: content.content_lesson_name,
                    typecontent: content.content_lesson_type,
                    details: content.content_lesson_name,
                    time: 0
                }))

            })),
            summary: {
                lesson_count: apiResponse.summary.lesson_count || 0,
                exam_count: apiResponse.summary.exam_count || 0,
                video_count: apiResponse.summary.video_count || 0
            }
        };
    };

    useEffect(() => {
        const fetchCourse = async () => {
            const courseId = slug || params?.id;

            if (!courseId) {
                setError("No course slug provided");
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                setError(null);
                const apiResponse = await getCourseBySlug(courseId as string);
                const mappedCourse = mapToCourseType(apiResponse);
                setCoursesData(mappedCourse);
            } catch (err) {
                setError(err instanceof Error ? err.message : "Failed to fetch course");
            } finally {
                setLoading(false);
            }
        };

        fetchCourse();
    }, [slug, params?.id]);

    useEffect(() => {
        console.log("Course data updated:", coursesData);
    }, [coursesData]);

    const course = coursesData;
    const level = course?.level || "เบื้องต้น";

    if (loading) {
        return (
            <main className="min-h-screen bg-[#F0FCFF] flex items-center justify-center">
                <p className="text-lg text-gray-800">Loading course details...</p>
            </main>
        )
    }

    if (error) {
        return (
            <main className="min-h-screen bg-[#F0FCFF] flex items-center justify-center">
                <p className="text-lg text-red-600">Error: {error}</p>
            </main>
        )
    }

    if (!course) {
        return (
            <main className="min-h-screen bg-[#F0FCFF] flex items-center justify-center">
                <p className="text-lg text-gray-800">Loading course details...</p>
            </main>
        )
    }

    return (
        <main className="min-h-screen bg-white">
            <CourseTitle
                name={course.name || "Untitled Course"}
                teacher={course.teacher}
                courseId={course.id || "unknown-id"}
                coverImage={course.coverImage || "/default-cover.jpg"}
            />
            {/* Course Details */}
            <CourseDetailsCard course={course} level={level} />

            <div className="max-w-5xl mx-auto px-4 py-8">
                <CourseNav
                    sections={[
                        { id: 'about', label: 'เกี่ยวกับ' },
                        { id: 'instruction', label: 'คำแนะนำในการเรียน' },
                    ]}
                />
            </div>
            <div id="about">
                <CourseDetails
                    courseId={course.id}
                    description={course.description}
                    instruction={course.instruction}
                    modules={course.lesson.map((lesson: { name: any; description: any }) => ({
                        title: lesson.name,
                        description: lesson.description
                    }))}
                    lessons={course.lesson}
                    certify={course.certify}
                    summary={course.summary || { lesson_count: 0, exam_count: 0, video_count: 0 }}
                />
            </div>
        </main>
    )
}