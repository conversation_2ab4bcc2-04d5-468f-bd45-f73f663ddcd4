import { api } from "@/lib/api";

export interface CourseSlugResponse {
  slug: string;
  course_name: string;
  course_picture: string;
  course_description: string;
  course_instruction: string;
  course_difficulty: string;
  course_duration: string;
  course_certificate: string;
  lecturer_id: number;
  lecturer_name: string;
  lecturer_position: string;
  lecturer_picture: string;
  summary: {
    lesson_count: number;
    exam_count: number;
    video_count: number;
  };
  lesson_details: {
    lesson_description: string;
    lesson_name: string;
    content: {
      content_lesson_name: string;
      content_lesson_type: string;
    }[];
  }[];
}

export interface GetUserDisplayCourseResponse {
  course_slug: string;
  course_name: string;
  course_description: string;
  course_lecturer: {
    lecturer_name: string;
    lecturer_position: string;
  };
  course_lessons: {
    lesson_name: string;
    lesson_duration: string;
    lesson_content: {
      content_slug: string;
      content_lesson_name: string;
      content_lesson_duration: string;
      url: string;
      is_locked: boolean;
      // Add optional fields for content type and description
      content_type?: string;
      content_description?: string;
    }[];
    is_locked: boolean;
  }[];
  course_exam: {
    has_exam: boolean;
    exam_duration: string;
    exam_slug: string;
    is_locked: boolean;
  };
}

export interface ContentQuizResponse {
  data: {
    content_slug: string;
    content_type: string;
    content_name: string;
    content_description: string;
    quiz_questions: {
      quiz_slug: string;
      quiz_title: string;
      quiz_details: string;
      quiz_image: string;
      quiz_time: string;
      quiz_type: string;
      quiz_choices: {
        choice_slug: string;
        choice: string;
        choice_image: string;
        choice_type: number;
      }[];
    }[];
  };
  success: boolean;
}

export interface submitRequest {
  question_slug: string;
  choice_slug: string[];
}

export interface submitResponse {
  question_slug: string;
  correct_slugs: string[];
  is_correct: boolean;
}


export interface completeRequest {
  content_lesson_slug: string;
}

export const getCourseBySlug = async (slug: string): Promise<CourseSlugResponse> => {
  try {
    const response = await api.get<CourseSlugResponse>(`/user/user-course/${slug}`);
    return response;
  } catch (error) {
    throw new Error(`Failed to fetch course with slug ${slug}: ${error}`);
  }
};

export const getUserDisplayCourse = async (slug: string): Promise<GetUserDisplayCourseResponse> => {
  try {
    const response = await api.get<GetUserDisplayCourseResponse>(`/user/display-course/${slug}`);
    return response;
  } catch (error) {
    throw new Error(`Failed to fetch user display course with slug ${slug}: ${error}`);
  }
};

export const getQuizContentBySlug = async (contentSlug: string): Promise<ContentQuizResponse> => {
  try {
    const response = await api.get<ContentQuizResponse>(`/user/quiz-display/content/${contentSlug}`);
    return response;
  } catch (error) {
    throw new Error(`Failed to fetch quiz content with slug ${contentSlug}: ${error}`);
  }
};

export const submitQuiz = async (submitData: submitRequest): Promise<submitResponse> => {
  try {
    const response = await api.post<submitResponse>('/user/quiz-display/submit', submitData);
    return response;
  } catch (error) {
    throw new Error(`Failed to submit quiz: ${error}`);
  }
};

export const completeContentProgress = async (completeData: completeRequest): Promise<any> => {
  try {
    const response = await api.post('/content-progress/complete', completeData);
    return response;
  } catch (error) {
    throw new Error(`Failed to complete content progress: ${error}`);
  }
};