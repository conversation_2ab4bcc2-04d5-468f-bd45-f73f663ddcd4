export interface AssignmentFormData {
  studentId: string
  pathAssignments: PathAssignment[]
}

export interface PathAssignment {
  pathId: string
  learningMode: "sequential" | "freestyle"
}

export interface SelectedPath {
  id: string
  name: string
  description: string
  courseCount: number
  learningMode: "sequential" | "freestyle"
}

export interface AssignEditorProps {
  initialData?: {
    studentId: string
    pathAssignments: PathAssignment[]
  }
  mode: "create" | "edit"
}
