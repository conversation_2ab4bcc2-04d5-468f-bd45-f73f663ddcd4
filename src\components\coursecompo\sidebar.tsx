import { BookOpen, Video, FileQuestion } from "lucide-react";
import { getCoursesData } from "@/data/allCourses"; // เปลี่ยน path ตามโครงสร้างโปรเจคของคุณ
import { CourseType } from "@/types/courses";

interface SidebarProps {
  lessonsCount: number;
  videosCount: number;
  questionsCount: number;
}

export default function Sidebar(
  {
    lessonsCount,
    videosCount,
    questionsCount
  }: SidebarProps) {

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm border-gray-200 border md:mb-3 lg:mb-0">
      {/* Default Vertical Layout (mobile and lg+) */}
      <div className="flex flex-col space-y-6 md:hidden lg:flex lg:space-y-6">
        <div className="flex items-center gap-3">
          <BookOpen className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">บทเรียน</h3>
            <p className="text-sm text-gray-500">{lessonsCount} บท</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Video className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">วิดีโอ</h3>
            <p className="text-sm text-gray-500">{videosCount} คลิป</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <FileQuestion className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">ท้ายบทเรียน</h3>
            <p className="text-sm text-gray-500">{questionsCount} ข้อ</p>
          </div>
        </div>
      </div>

      {/* Tablet Only: Horizontal Layout */}
      <div className="hidden md:grid md:grid-cols-3 md:gap-8 lg:hidden">
        <div className="flex items-center gap-3">
          <BookOpen className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">บทเรียน</h3>
            <p className="text-sm text-gray-500">{lessonsCount} บท</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Video className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">วิดีโอ</h3>
            <p className="text-sm text-gray-500">{videosCount} คลิป</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <FileQuestion className="h-6 w-6 text-gray-400" />
          <div>
            <h3 className="font-medium">ท้ายบทเรียน</h3>
            <p className="text-sm text-gray-500">{questionsCount} ข้อ</p>
          </div>
        </div>
      </div>
    </div>
  );
}