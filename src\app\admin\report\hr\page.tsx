"use client"

import { useState } from "react"
import { Search, Download, Filter, Users, TrendingUp, Clock, Award, Calendar, X } from "lucide-react"
import AdminLayout from "@/components/admin/layout"
import { useRouter } from "next/navigation"
import html2canvas from "html2canvas"
import jsPDF from "jspdf"

// Mock data for HR report
const mockHRData = {
  totalStudents: 150,
  activeStudents: 142,
  completedTraining: 98,
  inProgress: 44,
  avgCompletionTime: 45,
  certificationRate: 85
}

const mockStudentData = [
  {
    id: 1,
    studentId: "STU001",
    name: "นางสาวสุภา ใจดี",
    department: "แผนกการพยาบาล",
    position: "พยาบาลประจำการ",
    hireDate: "2023-01-15",
    completedCourses: 8,
    totalCourses: 10,
    completionRate: 80,
    lastActivity: "2025-01-10",
    status: "กำลังเรียน"
  },
  {
    id: 2,
    studentId: "STU002",
    name: "นายสมชาย รักดี",
    department: "แผนกเวชกรรม",
    position: "แพทย์ประจำการ",
    hireDate: "2022-03-20",
    completedCourses: 12,
    totalCourses: 12,
    completionRate: 100,
    lastActivity: "2024-01-08",
    status: "เสร็จสิ้น"
  },
  {
    id: 3,
    studentId: "STU003",
    name: "นางสาวมานี ใจงาม",
    department: "แผนกเภสัชกรรม",
    position: "เภสัชกรประจำการ",
    hireDate: "2023-06-10",
    completedCourses: 5,
    totalCourses: 8,
    completionRate: 62.5,
    lastActivity: "2024-01-05",
    status: "กำลังเรียน"
  },
  {
    id: 4,
    studentId: "STU004",
    name: "นายธนาคาร เรียนดี",
    department: "แผนกเทคนิคการแพทย์",
    position: "นักเทคนิคการแพทย์",
    hireDate: "2023-09-15",
    completedCourses: 6,
    totalCourses: 9,
    completionRate: 66.7,
    lastActivity: "2024-01-12",
    status: "กำลังเรียน"
  },
  {
    id: 5,
    studentId: "STU005",
    name: "นางสาวปรียา ขยันเรียน",
    department: "แผนกรังสีวิทยา",
    position: "นักรังสีการแพทย์",
    hireDate: "2022-11-01",
    completedCourses: 11,
    totalCourses: 11,
    completionRate: 100,
    lastActivity: "2024-01-09",
    status: "เสร็จสิ้น"
  }
]

const mockDepartmentStats = [
  {
    department: "แผนกการพยาบาล",
    totalStudents: 45,
    completedTraining: 38,
    inProgress: 7,
    completionRate: 84.4
  },
  {
    department: "แผนกเวชกรรม",
    totalStudents: 32,
    completedTraining: 28,
    inProgress: 4,
    completionRate: 87.5
  },
  {
    department: "แผนกเภสัชกรรม",
    totalStudents: 18,
    completedTraining: 15,
    inProgress: 3,
    completionRate: 83.3
  },
  {
    department: "แผนกเทคนิคการแพทย์",
    totalStudents: 25,
    completedTraining: 20,
    inProgress: 5,
    completionRate: 80.0
  }
]

export default function HRReportPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDepartment, setSelectedDepartment] = useState("ทั้งหมด")
  const [activeTab, setActiveTab] = useState("departments")
  const [showFilters, setShowFilters] = useState(false)
  const [dateRange, setDateRange] = useState({ start: "", end: "" })
  const [selectedTimeFilter, setSelectedTimeFilter] = useState("all")
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [selectedStudentId, setSelectedStudentId] = useState<string | null>(null)
  const router = useRouter()

  const handleStudentClick = (studentId: string, studentName: string) => {
    router.push(`/admin/report/hr/detail?id=${studentId}&name=${encodeURIComponent(studentName)}`)
  }

  const handleDownloadPDF = async (type: 'overall' | 'selected' = 'overall') => {
    setIsGeneratingPDF(true)
    
    try {
      // Create filtered data within the function
      const filteredData = mockStudentData.filter(emp => {
        const matchesSearch = emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             emp.studentId.toLowerCase().includes(searchTerm.toLowerCase())
        const matchesDepartment = selectedDepartment === "ทั้งหมด" || emp.department === selectedDepartment
        let matchesDate = true
        if (dateRange.start && dateRange.end) {
          const activityDate = new Date(emp.lastActivity)
          const startDate = new Date(dateRange.start)
          const endDate = new Date(dateRange.end)
          endDate.setHours(23, 59, 59, 999)
          matchesDate = activityDate >= startDate && activityDate <= endDate
        }
        return matchesSearch && matchesDepartment && matchesDate
      })

      // Create an isolated iframe for PDF generation
      const iframe = document.createElement('iframe')
      iframe.style.position = 'absolute'
      iframe.style.top = '-10000px'
      iframe.style.left = '-10000px'
      iframe.style.width = '1200px'
      iframe.style.height = '1600px'
      document.body.appendChild(iframe)

      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      if (!iframeDoc) throw new Error('Unable to access iframe document')

      const selectedStudent = selectedStudentId ? mockStudentData.find(s => s.studentId === selectedStudentId) : null
      const dataToShow = type === 'selected' && selectedStudent ? [selectedStudent] : filteredData

      // Create PDF HTML content with isolated styles
      const pdfContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>รายงาน HR - ระบบการเรียนรู้ออนไลน์ E-Med</title>
          <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&display=swap" rel="stylesheet">
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }
            
            body {
              font-family: 'Noto Sans Thai', sans-serif;
              line-height: 1.6;
              color: #1f2937;
              background: #ffffff;
            }
            
            .container {
              max-width: 1200px;
              margin: 0 auto;
              padding: 40px;
            }
            
            .header {
              text-align: center;
              margin-bottom: 40px;
              border-bottom: 3px solid #008067;
              padding-bottom: 30px;
            }
            
            .logo {
              font-size: 28px;
              font-weight: 700;
              color: #008067;
              margin-bottom: 10px;
            }
            
            .title {
              font-size: 24px;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 8px;
            }
            
            .subtitle {
              font-size: 16px;
              color: #6b7280;
            }
            
            .summary-stats {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 20px;
              margin-bottom: 40px;
            }
            
            .stat-card {
              background: #f8fafc;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              padding: 20px;
              text-align: center;
            }
            
            .stat-number {
              font-size: 24px;
              font-weight: 700;
              color: #008067;
              margin-bottom: 4px;
            }
            
            .stat-label {
              font-size: 14px;
              color: #6b7280;
            }
            
            .section {
              margin-bottom: 40px;
            }
            
            .section-header {
              background: #f8fafc;
              border-left: 4px solid #008067;
              padding: 20px;
              margin-bottom: 20px;
              border-radius: 0 8px 8px 0;
            }
            
            .section-title {
              font-size: 20px;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 4px;
            }
            
            .section-description {
              font-size: 14px;
              color: #6b7280;
            }
            
            .table {
              width: 100%;
              border-collapse: collapse;
              background: white;
              border-radius: 8px;
              overflow: hidden;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              page-break-inside: auto;
            }

            .table th {
              background: #f9fafb;
              padding: 16px;
              text-align: left;
              font-weight: 600;
              color: #374151;
              font-size: 14px;
              border-bottom: 1px solid #e5e7eb;
              page-break-inside: avoid;
              page-break-after: avoid;
            }

            .table td {
              padding: 16px;
              border-bottom: 1px solid #f3f4f6;
              font-size: 14px;
              page-break-inside: avoid;
            }

            .table thead {
              display: table-header-group;
            }

            .table tbody tr {
              page-break-inside: avoid;
              page-break-after: auto;
            }
            
            .table tr:last-child td {
              border-bottom: none;
            }
            
            .status-badge {
              display: inline-block;
              padding: 6px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 600;
            }
            
            .status-complete {
              background: #dcfce7;
              color: #166534;
            }
            
            .status-learning {
              background: #fef3c7;
              color: #92400e;
            }
            
            .status-notstarted {
              background: #f3f4f6;
              color: #374151;
            }
            
            .progress-bar {
              width: 80px;
              height: 8px;
              background: #e5e7eb;
              border-radius: 4px;
              overflow: hidden;
              display: inline-block;
            }
            
            .progress-fill {
              height: 100%;
              border-radius: 4px;
            }
            
            .progress-high {
              background: #10b981;
            }
            
            .progress-medium {
              background: #f59e0b;
            }
            
            .progress-low {
              background: #ef4444;
            }
            
            .footer {
              margin-top: 60px;
              padding-top: 30px;
              border-top: 2px solid #e5e7eb;
              text-align: center;
              color: #6b7280;
              font-size: 12px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">ระบบการเรียนรู้ออนไลน์ E-Med</div>
              <div class="title">รายงาน HR</div>
              <div class="subtitle">${type === 'selected' && selectedStudent ? `รายงานของ ${selectedStudent.name}` : 'รายงานภาพรวมนักเรียนทั้งหมด'}</div>
            </div>

            <div class="summary-stats">
              <div class="stat-card">
                <div class="stat-number">${type === 'selected' ? '1' : mockHRData.totalStudents}</div>
                <div class="stat-label">นักเรียนทั้งหมด</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">${type === 'selected' ? (selectedStudent?.status === 'กำลังเรียน' ? '1' : '0') : mockHRData.inProgress}</div>
                <div class="stat-label">กำลังเรียน</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">${type === 'selected' ? (selectedStudent?.status === 'เสร็จสิ้น' ? '1' : '0') : mockHRData.completedTraining}</div>
                <div class="stat-label">เสร็จสิ้น</div>
              </div>
            </div>

            ${activeTab === 'departments' && type === 'overall' ? `
            <div class="section">
              <div class="section-header">
                <div class="section-title">สถิติตามแผนก</div>
                <div class="section-description">ข้อมูลสรุปการเรียนรู้แยกตามแผนก</div>
              </div>
              <table class="table">
                <thead>
                  <tr>
                    <th>แผนก</th>
                    <th>นักเรียนทั้งหมด</th>
                    <th>เสร็จสิ้น</th>
                    <th>กำลังเรียน</th>
                    <th>อัตราเสร็จสิ้น</th>
                  </tr>
                </thead>
                <tbody>
                  ${filteredDepartmentStats.map(dept => `
                    <tr>
                      <td>${dept.department}</td>
                      <td>${dept.totalStudents}</td>
                      <td>${dept.completedTraining}</td>
                      <td>${dept.inProgress}</td>
                      <td>
                        <div style="display: flex; align-items: center; gap: 8px;">
                          <div class="progress-bar">
                            <div class="progress-fill ${dept.completionRate >= 90 ? 'progress-high' : dept.completionRate >= 70 ? 'progress-medium' : 'progress-low'}" 
                                 style="width: ${dept.completionRate}%"></div>
                          </div>
                          <span>${dept.completionRate.toFixed(1)}%</span>
                        </div>
                      </td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
            ` : ''}

            <div class="section">
              <div class="section-header">
                <div class="section-title">รายละเอียดนักเรียน</div>
                <div class="section-description">${type === 'selected' ? 'ข้อมูลนักเรียนที่เลือก' : 'ข้อมูลนักเรียนทั้งหมด'}</div>
              </div>
              <table class="table">
                <thead>
                  <tr>
                    <th>ลำดับ</th>
                    <th>ชื่อ-นามสกุล</th>
                    <th>แผนก</th>
                    <th>ตำแหน่ง</th>
                    <th>ความคืบหน้า</th>
                    <th>สถานะ</th>
                    <th>กิจกรรมล่าสุด</th>
                  </tr>
                </thead>
                <tbody>
                  ${dataToShow.map((student, index) => `
                    <tr>
                      <td>${index + 1}</td>
                      <td>${student.name}</td>
                      <td>${student.department}</td>
                      <td>${student.position}</td>
                      <td>
                        <div style="display: flex; align-items: center; gap: 8px;">
                          <div class="progress-bar">
                            <div class="progress-fill ${student.completionRate >= 90 ? 'progress-high' : student.completionRate >= 70 ? 'progress-medium' : 'progress-low'}" 
                                 style="width: ${student.completionRate}%"></div>
                          </div>
                          <span>${student.completionRate.toFixed(1)}%</span>
                        </div>
                      </td>
                      <td>
                        <span class="status-badge ${student.status === 'เสร็จสิ้น' ? 'status-complete' : student.status === 'กำลังเรียน' ? 'status-learning' : 'status-notstarted'}">
                          ${student.status}
                        </span>
                      </td>
                      <td>${student.lastActivity}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>

            <div class="footer">
              <div>รายงานนี้สร้างโดยระบบการเรียนรู้ออนไลน์ E-Med</div>
              <div>วันที่สร้าง: ${new Date().toLocaleDateString('th-TH')} เวลา: ${new Date().toLocaleTimeString('th-TH')}</div>
            </div>
          </div>
        </body>
        </html>
      `

      // Write content to iframe
      iframeDoc.open()
      iframeDoc.write(pdfContent)
      iframeDoc.close()

      // Wait for fonts to load
      await new Promise(resolve => {
        const checkFontLoad = () => {
          if (iframeDoc.fonts.ready) {
            iframeDoc.fonts.ready.then(resolve)
          } else {
            // Fallback timeout
            setTimeout(resolve, 2000)
          }
        }
        
        if (iframe.contentWindow) {
          iframe.onload = checkFontLoad
        } else {
          checkFontLoad()
        }
      })

      // Additional wait to ensure everything is rendered
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Generate PDF
      const canvas = await html2canvas(iframeDoc.body, {
        width: 1200,
        height: 1600,
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })

      const imgData = canvas.toDataURL('image/png')
      const pdf = new jsPDF('p', 'mm', 'a4')
      
      const imgWidth = 210
      const pageHeight = 297
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight
      let position = 0

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      // Clean up iframe
      document.body.removeChild(iframe)

      // Download PDF
      const fileName = type === 'selected' && selectedStudent 
        ? `รายงาน_HR_${selectedStudent.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`
        : `รายงาน_HR_ภาพรวม_${new Date().toISOString().split('T')[0]}.pdf`
      pdf.save(fileName)

    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('เกิดข้อผิดพลาดในการสร้าง PDF กรุณาลองอีกครั้ง')
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "เสร็จสิ้น":
        return "bg-green-100 text-green-800"
      case "กำลังเรียน":
        return "bg-yellow-100 text-yellow-800"
      case "ยังไม่เริ่ม":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getCompletionColor = (rate: number) => {
    if (rate >= 90) return "text-green-600"
    if (rate >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  const filteredStudents = mockStudentData.filter(emp => {
    // Search filter
    const matchesSearch = emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         emp.studentId.toLowerCase().includes(searchTerm.toLowerCase())
    
    // Department filter
    const matchesDepartment = selectedDepartment === "ทั้งหมด" || emp.department === selectedDepartment
    
    // Date filter - only use custom date range
    let matchesDate = true
    if (dateRange.start && dateRange.end) {
      const activityDate = new Date(emp.lastActivity)
      const startDate = new Date(dateRange.start)
      const endDate = new Date(dateRange.end)
      // Include the end date by setting time to end of day
      endDate.setHours(23, 59, 59, 999)
      matchesDate = activityDate >= startDate && activityDate <= endDate
    }
    
    return matchesSearch && matchesDepartment && matchesDate
  })

  const filteredDepartmentStats = mockDepartmentStats.filter(dept => 
    selectedDepartment === "ทั้งหมด" || dept.department === selectedDepartment
  )

  const clearFilters = () => {
    setSearchTerm("")
    setSelectedDepartment("ทั้งหมด")
    setDateRange({ start: "", end: "" })
    setShowFilters(false)
  }

  const hasActiveFilters = searchTerm || selectedDepartment !== "ทั้งหมด" || dateRange.start || dateRange.end

  return (
    <AdminLayout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900">รายงาน HR</h1>
            <div className="flex space-x-2">
              <button 
                onClick={() => handleDownloadPDF('overall')}
                disabled={isGeneratingPDF}
                className={`flex items-center px-4 py-2 rounded-md transition-colors ${
                  isGeneratingPDF
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-[#008067] text-white hover:bg-[#006b57]'
                }`}
              >
                <Download size={16} className="mr-2" />
                {isGeneratingPDF ? 'กำลังสร้าง PDF...' : 'ดาวน์โหลด PDF'}
              </button>
              <button 
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center px-4 py-2 border rounded-md transition-colors ${
                  showFilters || hasActiveFilters 
                    ? 'border-[#008067] bg-[#008067] text-white' 
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                <Filter size={16} className="mr-2" />
                ตัวกรอง
                {hasActiveFilters && (
                  <span className="ml-2 bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full">
                    {[searchTerm, selectedDepartment !== "ทั้งหมด", dateRange.start].filter(Boolean).length}
                  </span>
                )}
              </button>
            </div>
          </div>
          
          {/* Enhanced Search and Filter */}
          <div className="space-y-4">
            {/* Primary Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="ค้นหานักเรียน (ชื่อ หรือ รหัสนักเรียน)..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#008067] focus:border-transparent"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X size={16} />
                </button>
              )}
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium text-gray-700">ตัวกรองขั้นสูง</h3>
                  {hasActiveFilters && (
                    <button
                      onClick={clearFilters}
                      className="text-sm text-[#008067] hover:text-[#006b57] underline"
                    >
                      ล้างตัวกรองทั้งหมด
                    </button>
                  )}
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Department Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">แผนก</label>
                    <select
                      value={selectedDepartment}
                      onChange={(e) => setSelectedDepartment(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008067] focus:border-transparent"
                    >
                      <option value="ทั้งหมด">ทุกแผนก</option>
                      <option value="แผนกการพยาบาล">แผนกการพยาบาล</option>
                      <option value="แผนกเวชกรรม">แผนกเวชกรรม</option>
                      <option value="แผนกเภสัชกรรม">แผนกเภสัชกรรม</option>
                      <option value="แผนกเทคนิคการแพทย์">แผนกเทคนิคการแพทย์</option>
                      <option value="แผนกรังสีวิทยา">แผนกรังสีวิทยา</option>
                    </select>
                  </div>

                  {/* Custom Date Range */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <Calendar size={14} className="inline mr-1" />
                      ช่วงวันที่
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="date"
                        value={dateRange.start}
                        onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008067] focus:border-transparent"
                        placeholder="วันเริ่มต้น"
                      />
                      <span className="text-gray-500 self-center">ถึง</span>
                      <input
                        type="date"
                        value={dateRange.end}
                        onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008067] focus:border-transparent"
                        placeholder="วันสิ้นสุด"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Active Filters Display */}
            {hasActiveFilters && (
              <div className="flex flex-wrap gap-2">
                <span className="text-sm text-gray-600">ตัวกรองที่ใช้:</span>
                {searchTerm && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    ค้นหา: {searchTerm}
                    <button onClick={() => setSearchTerm("")} className="ml-1 text-blue-600 hover:text-blue-800">
                      <X size={12} />
                    </button>
                  </span>
                )}
                {selectedDepartment !== "ทั้งหมด" && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    แผนก: {selectedDepartment}
                    <button onClick={() => setSelectedDepartment("ทั้งหมด")} className="ml-1 text-green-600 hover:text-green-800">
                      <X size={12} />
                    </button>
                  </span>
                )}
                {dateRange.start && dateRange.end && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    วันที่: {dateRange.start} - {dateRange.end}
                    <button onClick={() => setDateRange({ start: "", end: "" })} className="ml-1 text-purple-600 hover:text-purple-800">
                      <X size={12} />
                    </button>
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

      {/* Enhanced Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">นักเรียนทั้งหมด</p>
              <p className="text-2xl font-bold text-gray-900">{mockHRData.totalStudents}</p>
              <p className="text-xs text-gray-500 mt-1">จำนวนนักเรียนในระบบ</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-full">
              <Users className="w-6 h-6 text-blue-500" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">กำลังเรียน</p>
              <p className="text-2xl font-bold text-yellow-600">{mockHRData.inProgress}</p>
              <p className="text-xs text-gray-500 mt-1">
                {((mockHRData.inProgress / mockHRData.totalStudents) * 100).toFixed(1)}% ของนักเรียนทั้งหมด
              </p>
            </div>
            <div className="p-3 bg-yellow-50 rounded-full">
              <Clock className="w-6 h-6 text-yellow-500" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">เสร็จสิ้น</p>
              <p className="text-2xl font-bold text-green-600">{mockHRData.completedTraining}</p>
              <p className="text-xs text-gray-500 mt-1">
                {((mockHRData.completedTraining / mockHRData.totalStudents) * 100).toFixed(1)}% ของนักเรียนทั้งหมด
              </p>
            </div>
            <div className="p-3 bg-green-50 rounded-full">
              <Award className="w-6 h-6 text-green-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab("departments")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "departments"
                  ? "border-[#008067] text-[#008067]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              สถิติตามแผนก
            </button>
            <button
              onClick={() => setActiveTab("employees")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "employees"
                  ? "border-[#008067] text-[#008067]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              รายละเอียดพนักงาน
            </button>
          </nav>
        </div>

        {/* Department Statistics Tab */}
        {activeTab === "departments" && (
          <div>
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">สถิติตามแผนก</h2>
                <span className="text-sm text-gray-500">
                  {filteredDepartmentStats.length} จาก {mockDepartmentStats.length} แผนก
                </span>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">แผนก</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">นักเรียนทั้งหมด</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">เสร็จสิ้น</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">กำลังเรียน</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">อัตราเสร็จสิ้น</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredDepartmentStats.length > 0 ? (
                    filteredDepartmentStats.map((dept, index) => (
                      <tr key={index} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{dept.department}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{dept.totalStudents}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{dept.completedTraining}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{dept.inProgress}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                              <div 
                                className={`h-2 rounded-full ${getCompletionColor(dept.completionRate).includes('green') ? 'bg-green-500' : 
                                  getCompletionColor(dept.completionRate).includes('yellow') ? 'bg-yellow-500' : 'bg-red-500'}`}
                                style={{ width: `${dept.completionRate}%` }}
                              ></div>
                            </div>
                            <span className={`text-sm font-medium ${getCompletionColor(dept.completionRate)}`}>
                              {dept.completionRate.toFixed(1)}%
                            </span>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                        ไม่พบข้อมูลแผนกที่ตรงกับเงื่อนไขการค้นหา
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Employee Details Tab */}
        {activeTab === "employees" && (
          <div>
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">รายละเอียดนักเรียน</h2>
                <span className="text-sm text-gray-500">
                  {filteredStudents.length} จาก {mockStudentData.length} นักเรียน
                </span>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ชื่อ-นามสกุล</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">แผนก</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ตำแหน่ง</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ความคืบหน้า</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">สถานะ</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">กิจกรรมล่าสุด</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredStudents.length > 0 ? (
                    filteredStudents.map((student) => (
                      <tr key={student.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <button
                              onClick={() => {
                                setSelectedStudentId(student.studentId === selectedStudentId ? null : student.studentId)
                                handleStudentClick(student.studentId, student.name)
                              }}
                              className="text-sm text-[#008067] hover:text-[#006b57] hover:underline cursor-pointer font-medium"
                            >
                              {student.name}
                            </button>
                            {selectedStudentId === student.studentId && (
                              <button
                                onClick={() => handleDownloadPDF('selected')}
                                disabled={isGeneratingPDF}
                                className="ml-3 text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200 disabled:opacity-50"
                                title="ดาวน์โหลด PDF ของนักเรียนคนนี้"
                              >
                                PDF
                              </button>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{student.department}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{student.position}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-20 bg-gray-200 rounded-full h-2 mr-3">
                              <div 
                                className={`h-2 rounded-full ${student.completionRate >= 90 ? 'bg-green-500' : student.completionRate >= 70 ? 'bg-yellow-500' : 'bg-red-500'}`}
                                style={{ width: `${student.completionRate}%` }}
                              ></div>
                            </div>
                            <div>
                              <span className="text-sm font-medium text-gray-900">{student.completionRate.toFixed(1)}%</span>
                              <div className="text-xs text-gray-500">
                                {student.completedCourses}/{student.totalCourses} คอร์ส
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(student.status)}`}>
                            {student.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{student.lastActivity}</div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                        <div className="flex flex-col items-center">
                          <Users className="w-12 h-12 text-gray-300 mb-2" />
                          <p>ไม่พบนักเรียนที่ตรงกับเงื่อนไขการค้นหา</p>
                          <p className="text-xs text-gray-400 mt-1">ลองปรับเปลี่ยนตัวกรองหรือคำค้นหา</p>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
      </div>
    </AdminLayout>
  )
}
