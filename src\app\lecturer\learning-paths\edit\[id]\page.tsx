import LecturerLayout from '@/components/lecturer/layout'
import LearningPathsEditor from '@/components/lecturer/learning-paths-editor'

interface EditLearningPathPageProps {
  params: { id: string }
}

export default function EditLearningPathPage({ params }: EditLearningPathPageProps) {
  return (
    <LecturerLayout>
      <div className="mx-auto py-4">
        <LearningPathsEditor mode="edit" pathId={params.id} />
      </div>
    </LecturerLayout>
  )
}
