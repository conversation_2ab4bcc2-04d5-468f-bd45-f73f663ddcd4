"use client"

import React, { useState, useMemo } from "react"
import type { ExamTrackData, StudentExamStats, ExamAttempt } from "@/hook/lecturerService"
import ExamResult from "./exam-result"
import Link from "next/link"
import {
  ChevronDown,
  ChevronUp,
  ChevronsUpDown,
  Award,
  Eye,
  GraduationCap,
  Search,
  Filter,
  Calendar,
  Target,
  ArrowLeft,
  FileChartColumnIncreasing,
  Clock,
} from "lucide-react"
import clsx from "clsx"

interface ExamTrackProps {
  data: ExamTrackData
  exam?: {
    exam_name: string
    course_name: string
    status: string
  }
  examId?: string // Add examId prop to connect to the right exam data
}

// Helper function to get initials from name
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

export default function ExamTrack({ data, exam, examId }: ExamTrackProps) {
  const [expandedStudents, setExpandedStudents] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "ผ่าน" | "ไม่ผ่าน">("all")
  const [positionFilter, setPositionFilter] = useState("all")
  const [sortBy, setSortBy] = useState<"name" | "score" | "status">("name")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")
  const [selectedExamResult, setSelectedExamResult] = useState<{
    studentId: string
    attemptNumber: number
  } | null>(null)

  // Get unique positions for dropdown
  const uniquePositions = useMemo(() => {
    if (!data?.students) return []
    return [...new Set(data.students.map(student => student.position))]
  }, [data?.students])

  // Filter and sort students
  const filteredAndSortedStudents = useMemo(() => {
    if (!data?.students) return []
    
    const filtered = data.students.filter(student => {
      const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = statusFilter === "all" || student.status === statusFilter
      const matchesPosition = positionFilter === "all" || student.position === positionFilter
      
      return matchesSearch && matchesStatus && matchesPosition
    })

    // Sort students
    filtered.sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name, 'th')
          break
        case "score":
          const aLatestScore = a.attempts[a.attempts.length - 1]?.score || 0
          const bLatestScore = b.attempts[b.attempts.length - 1]?.score || 0
          comparison = aLatestScore - bLatestScore
          break
        case "status":
          comparison = a.status.localeCompare(b.status, 'th')
          break
      }
      
      return sortOrder === "asc" ? comparison : -comparison
    })

    return filtered
  }, [data?.students, searchTerm, statusFilter, positionFilter, sortBy, sortOrder])

  const toggleExpanded = (studentId: string) => {
    const newExpanded = new Set(expandedStudents)
    if (newExpanded.has(studentId)) {
      newExpanded.delete(studentId)
    } else {
      newExpanded.add(studentId)
    }
    setExpandedStudents(newExpanded)
  }

  if (!data || !data.students || data.students.length === 0 || data.students.some(student => !student.attempts)) {
    return (
      <div className="flex items-center justify-center min-h-[600px]">
        <div className="text-center">
          <div className="bg-gray-50 rounded-3xl p-16 sm:p-20 shadow-xl border border-gray-100 max-w-lg mx-auto">
            <GraduationCap className="h-32 w-32 text-gray-300 mx-auto mb-8" />
            <p className="text-3xl sm:text-4xl text-gray-400 font-medium">ไม่พบข้อมูลการสอบ</p>
          </div>
        </div>
      </div>
    )
  }

  // Check if any student has null attempts
  const hasStudentsWithNullAttempts = data.students.some(student => !student.attempts || student.attempts === null)
  
  if (hasStudentsWithNullAttempts) {
    return (
      <div className="flex items-center justify-center min-h-[600px]">
        <div className="text-center">
          <div className="bg-gray-50 rounded-3xl p-16 sm:p-20 shadow-xl border border-gray-100 max-w-lg mx-auto">
            <GraduationCap className="h-32 w-32 text-gray-300 mx-auto mb-8" />
            <p className="text-3xl sm:text-4xl text-gray-400 font-medium">ไม่พบข้อมูลการสอบ</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-7xl mx-auto   ">
      {/* Header with Exam Info and Stats */}
      <div className="relative bg-gradient-to-r mb-6 from-[#293D97] via-[#3949ab] to-[#5c6bc0] rounded-3xl p-6 sm:p-8 text-white overflow-hidden shadow-2xl">
        <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>
        <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

        <div className="relative z-10">
          <div className="flex flex-col gap-6">
            {/* Mobile Layout - Average Score Card at bottom */}
            <div className="hidden sm:block absolute -top-2 -right-2 z-30">
              <div className="relative group">
                {/* Animated glowing background effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/40 via-orange-400/40 to-red-400/40 rounded-2xl blur-lg animate-pulse"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                {/* Main card with hover effect */}
                <div className="relative bg-white/25 backdrop-blur-lg border border-white/40 rounded-2xl p-4 shadow-2xl transform transition-all duration-300 hover:scale-105 hover:bg-white/30">
                  {/* Icon and label */}
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl shadow-lg transform transition-transform duration-300 group-hover:rotate-12">
                      <Award className="h-4 w-4 text-white" strokeWidth={2.5} />
                    </div>
                    <span className="text-white font-semibold text-sm tracking-wide">คะแนนเฉลี่ย</span>
                  </div>
                  
                  {/* Score display */}
                  <div className="text-center">
                    <div className="relative">
                      <span className="text-4xl font-black text-white drop-shadow-lg bg-gradient-to-r from-white to-yellow-100 bg-clip-text">
                        {filteredAndSortedStudents.length > 0 
                          ? (filteredAndSortedStudents.reduce((sum, s) => sum + s.best_score, 0) / filteredAndSortedStudents.length).toFixed(1)
                          : "0.0"
                        }
                      </span>
                      <span className="text-white/80 text-lg font-medium ml-1">คะแนน</span>
                    </div>
                    
                    {/* Enhanced score indicator bar */}
                    <div className="mt-2 w-full bg-white/30 rounded-full h-2 overflow-hidden shadow-inner">
                      <div 
                        className="h-full bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 rounded-full transition-all duration-1000 ease-out shadow-sm relative overflow-hidden"
                        style={{ 
                          width: `${filteredAndSortedStudents.length > 0 
                            ? ((filteredAndSortedStudents.reduce((sum, s) => sum + s.best_score, 0) / filteredAndSortedStudents.length) / 100) * 100
                            : 0}%` 
                        }}
                      >
                        {/* Shimmer effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 animate-shimmer"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Back button and Exam Info - Mobile First Layout */}
            <div className="flex flex-col gap-4 sm:pr-40 md:pr-44 lg:pr-48">
              <div className="flex justify-center sm:justify-start">
                <Link 
                  href="/lecturer/exam-stats"
                  className="inline-flex items-center gap-2 text-white/80 hover:text-white transition-colors"
                >
                  <ArrowLeft className="h-4 w-4" />
                  กลับไปยังรายการข้อสอบ
                </Link>
              </div>
              
              {/* Mobile: Title and Icon at top */}
              <div className="flex flex-col sm:flex-row md:flex-row items-center sm:items-start md:items-start gap-3 text-center sm:text-left mt-2">
                <div className="p-3 sm:p-4 bg-white/20 rounded-2xl backdrop-blur-sm">
                  <FileChartColumnIncreasing className="h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12" strokeWidth={2.2} />
                </div>
                <div className="flex-1">
                  <div>
                    <h1 className="text-lg sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-1">สถิติการสอบ</h1>
                    <p className="text-sm sm:text-lg md:text-xl lg:text-xl opacity-90 font-medium mb-3">
                      {exam?.exam_name || 'ข้อสอบ'} - {exam?.course_name || 'คอร์สเรียน'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Stats Section - Updated Style */}
            <div className="flex flex-col gap-4">
              {/* Main Stats Container */}
              <div className="bg-white/20 backdrop-blur-sm border border-white/50 rounded-2xl p-6">
                {/* Top Section: Number + Label */}
                <div className="flex items-baseline gap-2 mb-4">
                  <div className="text-5xl font-bold text-white">{filteredAndSortedStudents.length}</div>
                  <p className="text-white/80 text-base">ผู้สอบ</p>
                </div>
                
                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="w-full bg-gradient-to-r from-green-700 via-white to-red-700 rounded-full h-4 overflow-hidden relative border-2 border-white/50">
                    {/* Actual progress overlay with stronger colors - clearly separated bars */}
                    <div className="h-full flex gap-0 relative z-10">
                      <div 
                        className="bg-gradient-to-r from-green-700 to-green-500 rounded-l-full transition-all duration-500 ease-out  border-white/20"
                        style={{ 
                          width: `${filteredAndSortedStudents.length > 0 
                            ? (filteredAndSortedStudents.filter(s => s.status === "ผ่าน").length / filteredAndSortedStudents.length) * 100 
                            : 0}%` 
                        }}
                      />
                      <div 
                        className="bg-gradient-to-r from-red-500 to-red-700 rounded-r-full transition-all duration-500 ease-out  border-white/20"
                        style={{ 
                          width: `${filteredAndSortedStudents.length > 0 
                            ? (filteredAndSortedStudents.filter(s => s.status === "ไม่ผ่าน").length / filteredAndSortedStudents.length) * 100 
                            : 0}%` 
                        }}
                      />
                    </div>
                    
                    {/* Single shimmer effect flowing across entire bar */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 animate-shimmer opacity-70 z-20"></div>
                  </div>
                </div>
                
                {/* Bottom Section: Legend and Numbers aligned */}
                <div className="flex justify-between">
                  <div className="flex flex-col items-start">
                    <div className="flex items-center gap-2 mb-1">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <span className="text-white/90 text-sm font-semibold">สอบผ่าน</span>
                    </div>
                    <span className="text-white text-2xl font-bold">
                      {filteredAndSortedStudents.filter(s => s.status === "ผ่าน").length}
                    </span>
                  </div>
                  <div className="flex flex-col items-end">
                    <div className="flex items-center gap-2 mb-1">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <span className="text-white/90 text-sm font-semibold">สอบไม่ผ่าน</span>
                    </div>
                    <span className="text-white text-2xl font-bold">
                      {filteredAndSortedStudents.filter(s => s.status === "ไม่ผ่าน").length}
                    </span>
                  </div>
                </div>
              </div>
              
              {/* Mobile Average Score Card - Bottom of Stats Container */}
              <div className="block sm:hidden">
                <div className="relative group">
                  {/* Animated glowing background effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/30 via-orange-400/30 to-red-400/30 rounded-2xl blur-lg animate-pulse"></div>
                  
                  {/* Main card with hover effect */}
                  <div className="relative bg-white/25 backdrop-blur-lg border border-white/40 rounded-2xl p-4 shadow-2xl">
                    {/* Icon and label */}
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl shadow-lg">
                        <Award className="h-4 w-4 text-white" strokeWidth={2.5} />
                      </div>
                      <span className="text-white font-semibold text-sm tracking-wide">คะแนนเฉลี่ย</span>
                    </div>
                    
                    {/* Score display */}
                    <div className="text-center">
                      <div className="relative">
                        <span className="text-3xl font-black text-white drop-shadow-lg bg-gradient-to-r from-white to-yellow-100 bg-clip-text">
                          {filteredAndSortedStudents.length > 0 
                            ? (filteredAndSortedStudents.reduce((sum, s) => sum + s.best_score, 0) / filteredAndSortedStudents.length).toFixed(1)
                            : "0.0"
                          }
                        </span>
                        <span className="text-white/80 text-lg font-medium ml-1">คะแนน</span>
                      </div>
                      
                      {/* Enhanced score indicator bar */}
                      <div className="mt-2 w-full bg-white/30 rounded-full h-2 overflow-hidden shadow-inner">
                        <div 
                          className="h-full bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 rounded-full transition-all duration-1000 ease-out shadow-sm relative overflow-hidden"
                          style={{ 
                            width: `${filteredAndSortedStudents.length > 0 
                              ? ((filteredAndSortedStudents.reduce((sum, s) => sum + s.best_score, 0) / filteredAndSortedStudents.length) / 100) * 100
                              : 0}%` 
                          }}
                        >
                          {/* Shimmer effect */}
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 animate-shimmer"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search/Filter Section - Normal White Style */}
      <div className="bg-white rounded-3xl shadow-xl border border-gray-200 overflow-hidden">
        {/* Search and Filter Controls Header */}
        <div className="bg-white border-b border-gray-200 p-6">
          <div className="grid grid-cols-1 lg:grid-cols-8 gap-4 items-end">
            {/* Search Bar - Takes 3 columns (larger) */}
            <div className="lg:col-span-3">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Search className="inline h-4 w-4 mr-1" />
                ค้นหา
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="ชื่อผู้เข้าสอบ..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-400 text-gray-900 focus:outline-none focus:placeholder-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Status Filter - Smaller */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Filter className="inline h-4 w-4 mr-1" />
                สถานะ
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as "all" | "ผ่าน" | "ไม่ผ่าน")}
                className="block w-full px-2 py-2 text-sm border border-gray-300 rounded-lg bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">ทั้งหมด</option>
                <option value="ผ่าน">ผ่าน</option>
                <option value="ไม่ผ่าน">ไม่ผ่าน</option>
              </select>
            </div>

            {/* Position Filter - Takes 2 columns */}
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Target className="inline h-4 w-4 mr-1" />
                ตำแหน่ง
              </label>
              <select
                value={positionFilter}
                onChange={(e) => setPositionFilter(e.target.value)}
                className="block w-full px-2 py-2 text-sm border border-gray-300 rounded-lg bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">ทั้งหมด</option>
                {uniquePositions.map(position => (
                  <option key={position} value={position}>{position}</option>
                ))}
              </select>
            </div>

            {/* Sort Field - Smaller */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                เรียงตาม
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as "name" | "score" | "status")}
                className="block w-full px-2 py-2 text-sm border border-gray-300 rounded-lg bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="name">ชื่อ</option>
                <option value="score">คะแนน</option>
                <option value="status">สถานะ</option>
              </select>
            </div>

            {/* Sort Order - Smaller */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <ChevronsUpDown className="inline h-4 w-4 mr-1" />
                ลำดับ
              </label>
              <button
                onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
                className="flex items-center justify-center w-full px-2 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-sm text-gray-900"
              >
                {sortOrder === "asc" ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>

   
        </div>
        
        {/* Student List Content */}
        <div className="bg-white p-6">
          {filteredAndSortedStudents.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-gray-50 rounded-2xl p-8 max-w-md mx-auto">
                <Search className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <p className="text-lg text-gray-400 font-medium">ไม่พบผู้เข้าสอบที่ตรงกับการค้นหา</p>
                <p className="text-sm text-gray-500 mt-2">ลองเปลี่ยนคำค้นหาหรือตัวกรองที่ใช้</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4 max-w-6xl mx-auto">
              {filteredAndSortedStudents.map((student) => {
                const isExpanded = expandedStudents.has(student.id)
                
                return (
                  <div key={student.id} className="bg-white rounded-2xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 overflow-hidden">
                    {/* Student Header - Clickable */}
                    <button
                      onClick={() => toggleExpanded(student.id)}
                      className="w-full p-4 sm:p-6 text-left hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex flex-col sm:flex-row lg:flex-row items-center sm:items-center lg:items-center gap-4 sm:gap-6 lg:gap-8">
                        {/* Avatar and Basic Info */}
                        <div className="flex items-center gap-4 flex-1 min-w-0 justify-center sm:justify-start">
                          {/* Avatar with fallback to initials */}
                          <div className="relative flex-shrink-0">
                            <div className="w-16 h-16 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-full bg-gradient-to-br from-[#008268] to-[#00a685] text-white flex items-center justify-center font-bold text-lg sm:text-base lg:text-lg border-2 border-gray-200 shadow-lg">
                              {getInitials(student.name)}
                            </div>
                          </div>
                          
                          <div className="flex-1 min-w-0 text-center sm:text-left">
                            <h3 className="text-lg sm:text-lg lg:text-lg font-bold text-gray-800 mb-1 truncate">
                              {student.name}
                            </h3>
                            <p className="text-sm text-gray-600 mb-2">{student.position}</p>
                          </div>
                        </div>
                        
                        {/* Status and Score */}
                        <div className="flex flex-col sm:flex-row items-center sm:items-center gap-4 w-full sm:w-auto">
                          <div className="flex-1 sm:flex-none">
                            <span className={clsx(
                              "px-3 py-1 rounded-lg text-sm font-medium border shadow-sm",
                              student.status === "ผ่าน" 
                                ? "bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border-green-200"
                                : "bg-gradient-to-r from-red-100 to-rose-100 text-red-700 border-red-200"
                            )}>
                              {student.status}
                            </span>
                          </div>
                          
                          <div className="text-center sm:text-center lg:text-center">
                            <p className="text-lg font-bold text-gray-800">{student.attempts[student.attempts.length - 1]?.score || 0}/100</p>
                            <p className="text-sm text-gray-600">คะแนนล่าสุด</p>
                          </div>
                          
                          <div className="flex items-center text-gray-400 ml-auto sm:ml-4">
                            {isExpanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
                          </div>
                        </div>
                      </div>
                    </button>
                    
                    {/* Expanded Content - Back to Normal Style */}
                    {isExpanded && (
                      <div className="border-t border-gray-200 bg-gradient-to-br from-gray-50/80 to-blue-50/30">
                        <div className="p-6">
                          <div className="space-y-4 max-w-6xl mx-auto">
                            {student.attempts.map((attempt, index) => (
                              <div 
                                key={index}
                                className="bg-white rounded-2xl border border-gray-200 shadow-lg p-6 hover:shadow-xl transition-all duration-300"
                              >
                                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-4 mb-4">
                                      <div className={`w-12 h-12 rounded-2xl flex items-center justify-center font-bold text-lg ${
                                        attempt.status === 'ผ่าน' 
                                          ? 'bg-green-100 text-green-700' 
                                          : 'bg-red-100 text-red-700'
                                      }`}>
                                        {attempt.attempt_number}
                                      </div>
                                      <div>
                                        <h3 className="text-lg font-bold text-gray-800">ครั้งที่ {attempt.attempt_number}</h3>
                                        <div className="flex items-center gap-4 text-sm text-gray-600">
                                          <div className="flex items-center gap-1">
                                            <Calendar className="w-4 h-4" />
                                            <span>{attempt.submitted_at}</span>
                                          </div>
                                          <div className="flex items-center gap-1">
                                            <Clock className="w-4 h-4" />
                                            <span>{attempt.time_spent}</span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                      <div className="bg-gray-50 rounded-xl p-4">
                                        <div className="text-2xl font-bold text-gray-800">{attempt.score}/100</div>
                                        <div className="text-sm text-gray-600">คะแนนที่ได้</div>
                                      </div>
                                      <div className="bg-gray-50 rounded-xl p-4">
                                        <div className="text-base font-bold text-gray-800">{attempt.submitted_at}</div>
                                        <div className="text-sm text-gray-600">วันที่ทำการสอบ</div>
                                      </div>
                                      <div className="bg-gray-50 rounded-xl p-4">
                                        <div className={`text-2xl font-bold ${
                                          attempt.status === 'ผ่าน' ? 'text-green-600' : 'text-red-600'
                                        }`}>
                                          {attempt.status}
                                        </div>
                                        <div className="text-sm text-gray-600">ผลการสอบ</div>
                                      </div>
                                    </div>
                                  </div>

                                  <div className="flex flex-col sm:flex-row gap-3">
                                    <button
                                      onClick={() => {
                                        setSelectedExamResult({
                                          studentId: student.id,
                                          attemptNumber: attempt.attempt_number
                                        })
                                      }}
                                      className="group flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 text-sm font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                                    >
                                      <Eye className="h-4 w-4 group-hover:scale-110 transition-transform" />
                                      <span>ดูรายละเอียด</span>
                                    </button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>

      {/* Modal for Exam Result */}
      {selectedExamResult && examId && (
        <div 
          className="fixed inset-0  bg-black bg-opacity-50 z-[9999] flex items-center justify-center p-4"
          onClick={(e) => {
            // Close modal if clicking on backdrop
            if (e.target === e.currentTarget) {
              setSelectedExamResult(null)
            }
          }}
        >
          <div 
            className="bg-white rounded-2xl shadow-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900">ผลการสอบรายละเอียด</h2>
              <button
                onClick={() => setSelectedExamResult(null)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                title="ปิด"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(90vh-5rem)]">
              <ExamResult 
                examId={examId}
                studentId={selectedExamResult.studentId}
                attemptNumber={selectedExamResult.attemptNumber}
                onBack={() => setSelectedExamResult(null)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
