// Types for API responses
export interface User {
    id: number
    user_picture: string
    user_fname: string
    user_lname: string
    user_email: string
    user_position: string
    user_login_date: string
    user_status: string
    user_role: number
    user_slug: string
}

export interface UserTable {
    slug: string;
    first_name: string;
    last_name: string;
    picture: string;
    email: string;
    role: string;
    position: string;
    status: string;
}

export interface UserSlugResponse {
    slug: string;
    user_picture: string;
    password: string;
    user_fname: string;
    user_lname: string;
    user_email: string;
    user_position: string;
    user_login_date: string;
    role: number;
    status: number;
}