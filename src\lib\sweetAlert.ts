import Swal from 'sweetalert2'

// SweetAlert2 configuration with Thai language support
const swalConfig = {
  confirmButtonColor: '#008268',
  cancelButtonColor: '#6b7280',
  confirmButtonText: 'ยืนยัน',
  cancelButtonText: 'ยกเลิก',
  showClass: {
    popup: 'animate__animated animate__fadeInDown'
  },
  hideClass: {
    popup: 'animate__animated animate__fadeOutUp'
  }
}

// Success notification
export const showSuccessAlert = (title: string, text?: string) => {
  return Swal.fire({
    icon: 'success',
    title,
    text,
    confirmButtonColor: swalConfig.confirmButtonColor,
    confirmButtonText: 'ตกลง',
    timer: 3000,
    timerProgressBar: true,
    showConfirmButton: true,
    allowOutsideClick: true,
  })
}

// Error notification
export const showErrorAlert = (title: string, text?: string) => {
  return Swal.fire({
    icon: 'error',
    title,
    text,
    confirmButtonColor: swalConfig.confirmButtonColor,
    confirmButtonText: 'ตกลง',
    allowOutsideClick: true,
  })
}

// Warning notification
export const showWarningAlert = (title: string, text?: string) => {
  return Swal.fire({
    icon: 'warning',
    title,
    text,
    confirmButtonColor: swalConfig.confirmButtonColor,
    confirmButtonText: 'ตกลง',
    allowOutsideClick: true,
  })
}

// Confirmation dialog
export const showConfirmDialog = (
  title: string,
  text?: string,
  confirmText: string = 'ยืนยัน',
  cancelText: string = 'ยกเลิก'
) => {
  return Swal.fire({
    icon: 'question',
    title,
    text,
    showCancelButton: true,
    confirmButtonColor: swalConfig.confirmButtonColor,
    cancelButtonColor: swalConfig.cancelButtonColor,
    confirmButtonText: confirmText,
    cancelButtonText: cancelText,
    reverseButtons: true,
    allowOutsideClick: false,
  })
}

// Delete confirmation dialog
export const showDeleteConfirmDialog = (itemName: string) => {
  return Swal.fire({
    icon: 'warning',
    title: 'ยืนยันการลบ',
    html: `คุณต้องการลบ <strong>${itemName}</strong> หรือไม่?<br><small class="text-gray-500">การดำเนินการนี้ไม่สามารถยกเลิกได้</small>`,
    showCancelButton: true,
    confirmButtonColor: '#dc2626',
    cancelButtonColor: swalConfig.cancelButtonColor,
    confirmButtonText: 'ลบ',
    cancelButtonText: 'ยกเลิก',
    reverseButtons: true,
    allowOutsideClick: false,
  })
}

// Loading dialog
export const showLoadingAlert = (title: string = 'กำลังดำเนินการ...') => {
  return Swal.fire({
    title,
    allowOutsideClick: false,
    allowEscapeKey: false,
    showConfirmButton: false,
    didOpen: () => {
      Swal.showLoading()
    }
  })
}

// Close loading dialog
export const closeLoadingAlert = () => {
  Swal.close()
}

// Save confirmation dialog for course editor
export const showSaveConfirmDialog = (isEditMode: boolean) => {
  const action = isEditMode ? 'อัปเดต' : 'บันทึก'
  const title = isEditMode ? 'ยืนยันการอัปเดตคอร์ส' : 'ยืนยันการบันทึกคอร์ส'
  const text = isEditMode 
    ? 'คุณต้องการอัปเดตข้อมูลคอร์สนี้หรือไม่?' 
    : 'คุณต้องการบันทึกคอร์สใหม่นี้หรือไม่?'
  
  return showConfirmDialog(title, text, action, 'ยกเลิก')
}

// Course save success
export const showCourseSaveSuccess = (isEditMode: boolean) => {
  const title = isEditMode ? 'อัปเดตคอร์สสำเร็จ!' : 'บันทึกคอร์สสำเร็จ!'
  const text = isEditMode 
    ? 'ข้อมูลคอร์สได้รับการอัปเดตเรียบร้อยแล้ว' 
    : 'คอร์สใหม่ได้รับการบันทึกเรียบร้อยแล้ว'
  
  return showSuccessAlert(title, text)
}

// Course save error
export const showCourseSaveError = (isEditMode: boolean, errorMessage?: string) => {
  const title = isEditMode ? 'เกิดข้อผิดพลาดในการอัปเดตคอร์ส' : 'เกิดข้อผิดพลาดในการบันทึกคอร์ส'
  const text = errorMessage || 'กรุณาลองใหม่อีกครั้ง หรือติดต่อผู้ดูแลระบบ'
  
  return showErrorAlert(title, text)
}

// Lesson delete confirmation
export const showLessonDeleteConfirm = (lessonName: string) => {
  return showDeleteConfirmDialog(`บทเรียน "${lessonName}"`)
}

// Content delete confirmation
export const showContentDeleteConfirm = (contentName: string) => {
  return showDeleteConfirmDialog(`เนื้อหา "${contentName}"`)
}

// Form validation error
export const showValidationError = (message: string) => {
  return showErrorAlert('ข้อมูลไม่ถูกต้อง', message)
}

// Network error
export const showNetworkError = () => {
  return showErrorAlert(
    'เกิดข้อผิดพลาดในการเชื่อมต่อ',
    'กรุณาตรวจสอบการเชื่อมต่ออินเทอร์เน็ตและลองใหม่อีกครั้ง'
  )
}
