import UserEditor from "@/components/admin/user-editor"
import AdminLayout from "@/components/admin/layout"

export default async function EditUserPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  
  return (
    <AdminLayout>
      <div className="flex flex-col gap-4">
        <div className="rounded-lg">
          <UserEditor userId={id} />
        </div>
      </div>
    </AdminLayout>
  )
}

