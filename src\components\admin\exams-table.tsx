"use client"

import { useEffect, useState } from "react"
import examService, { ExamTable } from "@/hook/examService"
import type React from "react"
import { showDeleteConfirmDialog, showSuccessAlert, showErrorAlert } from "@/lib/sweetAlert"

import { Search, ChevronLeft, ChevronRight, Edit, Trash2, MoreHorizontal, Eye } from "lucide-react"
import { FaPlus } from "react-icons/fa"
import { ChevronUp, ChevronDown } from "lucide-react"
import Link from "next/link"

export default function ExamsTable() {
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedRows, setSelectedRows] = useState<number[]>([])
  const [sortField, setSortField] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null)
  const [selectAll, setSelectAll] = useState(false)
  const [exams, setExams] = useState<ExamTable[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const itemsPerPage = 10

  useEffect(() => {
    setLoading(true)
    examService.getExamsTable()
      .then((data) => {
        setExams(Array.isArray(data) ? data : [data])
        setLoading(false)
      })
      .catch((err) => {
        setError(err.message || 'เกิดข้อผิดพลาดในการโหลดข้อมูลข้อสอบ')
        setLoading(false)
      })
  }, [])

  // ฟังก์ชันค้นหา
  const filteredExams = Array.isArray(exams) && exams.length > 0
    ? exams.filter(
      (exam) =>
        (exam.exam_name || "").toLowerCase().includes(searchQuery.toLowerCase()) ||
        String(exam.slug).toLowerCase().includes(searchQuery.toLowerCase()),
    )
    : [];

  // ฟังก์ชันเรียงลำดับ
  const sortedExams = [...filteredExams].sort((a, b) => {
    if (!sortField) return 0
    const fieldA = a[sortField as keyof typeof a]
    const fieldB = b[sortField as keyof typeof b]
    if (typeof fieldA === "string" && typeof fieldB === "string") {
      return sortDirection === "asc" ? fieldA.localeCompare(fieldB) : fieldB.localeCompare(fieldA)
    }
    if (typeof fieldA === "number" && typeof fieldB === "number") {
      return sortDirection === "asc" ? fieldA - fieldB : fieldB - fieldA
    }
    return 0
  })

  // ฟังก์ชันแบ่งหน้า
  const totalPages = Math.ceil(sortedExams.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedExams = sortedExams.slice(startIndex, startIndex + itemsPerPage)

  // ฟังก์ชันเลือกแถว
  const toggleRowSelection = (id: number) => {
    if (selectedRows.includes(id)) {
      setSelectedRows(selectedRows.filter((rowId) => rowId !== id))
    } else {
      setSelectedRows([...selectedRows, id])
    }
  }

  // ฟังก์ชันเลือกทั้งหมด
  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedRows([])
    } else {
      setSelectedRows(paginatedExams.map((exam) => exam.slug))
    }
    setSelectAll(!selectAll)
  }

  // ฟังก์ชันเรียงลำดับ
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // ฟังก์ชันเปิด/ปิด dropdown
  const toggleDropdown = (id: number) => {
    if (activeDropdown === id) {
      setActiveDropdown(null)
    } else {
      setActiveDropdown(id)
    }
  }

  // ฟังก์ชันหาชื่อคอร์สจาก course_id
  const getCourseName = (courseId: number) => {
    return courseId ? `Course #${courseId}` : "ไม่พบคอร์ส"
  }

  // แก้ไขฟังก์ชัน renderSortIcon ให้แสดงไอคอนตลอดเวลา
  function renderSortIcon(field: string): React.ReactNode {
    if (sortField === field) {
      return sortDirection === "asc" ? (
        <ChevronUp className="ml-1 h-4 w-4 text-gray-700" />
      ) : (
        <ChevronDown className="ml-1 h-4 w-4 text-gray-700" />
      )
    }
    // แสดงไอคอนทั้งขึ้นและลงเมื่อยังไม่ได้เรียงลำดับ
    return (
      <span className="ml-1 inline-flex flex-col">
        <ChevronUp className="h-3 w-3 -mb-1 text-gray-400" />
        <ChevronDown className="h-3 w-3 text-gray-400" />
      </span>
    )
  }

  // --- ลบข้อสอบพร้อม SweetAlert ---
  const handleDeleteExam = async (exam: ExamTable) => {
    const result = await showDeleteConfirmDialog(
      `คุณแน่ใจหรือไม่ว่าต้องการลบข้อสอบ "${exam.exam_name || exam.slug}"? การกระทำนี้ไม่สามารถย้อนกลับได้`
    );
    if (result.isConfirmed) {
      try {
        await examService.deleteExam(exam.slug)
        setExams(exams.filter((e) => e.slug !== exam.slug))
        setSelectedRows(selectedRows.filter((id) => id !== exam.slug))
        await showSuccessAlert('ลบสำเร็จ', 'ข้อสอบถูกลบเรียบร้อยแล้ว')
      } catch (err: any) {
        await showErrorAlert('เกิดข้อผิดพลาด', err.message || 'ไม่สามารถลบข้อสอบได้')
      }
    }
    setActiveDropdown(null)
  }

  return (
    <div className="w-full">
      {/* ส่วนค้นหาและปุ่มเพิ่มข้อสอบ */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
        <div className="relative w-full sm:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="ค้นหาข้อสอบ..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full bg-gray-50 sm:w-[300px] focus:outline-none focus:ring-2 focus:ring-[#008268]"
          />
        </div>

        <Link
          href="/admin/exams/new"
          className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-md w-full font-bold sm:w-auto flex items-center justify-center gap-2"
        >
          เพิ่มข้อสอบใหม่
          <FaPlus className="text-white text-sm" />
        </Link>
      </div>

      {/* ตาราง */}
      <div className="overflow-x-auto border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-3 py-3 text-center">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={toggleSelectAll}
                  className="h-4 w-4 rounded border-gray-300 bg-gray-50 text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                />
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("exam_name")}
              >
                <div className="flex items-center">
                  ชื่อข้อสอบ
                  {renderSortIcon("exam_name")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("courseId")}
              >
                <div className="flex items-center">
                  คอร์ส
                  {renderSortIcon("courseId")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("questions")}
              >
                <div className="flex items-center justify-center">
                  จำนวนคำถาม
                  {renderSortIcon("questions")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("passingScore")}
              >
                <div className="flex items-center justify-center">
                  เกณฑ์ผ่าน (%)
                  {renderSortIcon("passingScore")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("timeLimit")}
              >
                <div className="flex items-center justify-center">
                  เวลา (นาที)
                  {renderSortIcon("timeLimit")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                จัดการ
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr><td colSpan={7} className="text-center py-8 text-gray-500">กำลังโหลด...</td></tr>
            ) : paginatedExams.length === 0 ? (
              <tr><td colSpan={7} className="text-center py-8 text-gray-500">ไม่พบข้อสอบ</td></tr>
            ) : (
              paginatedExams.map((exam) => (
                <tr key={exam.slug} className="hover:bg-gray-50">
                  <td className="px-3 py-4 whitespace-nowrap text-center">
                    <input
                      type="checkbox"
                      checked={selectedRows.includes(exam.slug)}
                      onChange={() => toggleRowSelection(exam.slug)}
                      className="h-4 w-4 rounded border-gray-300 bg-white text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{exam.exam_name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{exam.course_name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <div className="text-sm text-gray-900">{exam.exam_amount}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <div className="text-sm text-gray-900">{exam.passing_score}%</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <div className="text-sm text-gray-900">{exam.exam_time}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium relative">
                    <button onClick={() => toggleDropdown(exam.slug)} className="text-gray-500 hover:text-gray-700">
                      <MoreHorizontal className="h-5 w-5" />
                    </button>

                    {activeDropdown === exam.slug && (
                      <div className="right-6 mt-2 w-48 fixed rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                        <div className="py-1" role="menu" aria-orientation="vertical">
                          <button
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => {
                              console.log("View details:", exam.slug)
                              setActiveDropdown(null)
                            }}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            ดูรายละเอียด
                          </button>
                          <Link
                            href={`/admin/exams/edit/${exam.slug}`}
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => setActiveDropdown(null)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            แก้ไข
                          </Link>
                          <button
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                            onClick={() => handleDeleteExam(exam)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            ลบ
                          </button>
                        </div>
                      </div>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* ส่วนแสดงจำนวนที่เลือกและการแบ่งหน้า */}
      <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4">
        <div className="text-sm text-gray-500">
          เลือก {selectedRows.length} จาก {filteredExams.length} รายการ
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700">
            หน้า {currentPage} จาก {totalPages || 1}
          </span>
          <div className="flex gap-1">
            <button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages || totalPages === 0}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
