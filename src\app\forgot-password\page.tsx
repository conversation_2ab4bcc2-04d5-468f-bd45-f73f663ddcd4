"use client"
import React, { useState, useRef, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { LockKeyhole, ArrowLeft } from "lucide-react"
import { authAPI } from "@/hook/auth"

export default function CombinedPasswordResetPage() {
  const router = useRouter()
  
  // Main flow state
  const [currentStep, setCurrentStep] = useState<'request' | 'otp' | 'reset'>('request')
  const [email, setEmail] = useState("")
  const maskedEmail = email ? email.replace(/(.{2})(.*)(@.*)/, "$1*****$3") : ""

  // Step 1: Request OTP states
  const [requestError, setRequestError] = useState("")
  const [isRequestingOTP, setIsRequestingOTP] = useState(false)

  // Step 2: OTP verification states
  const [otp, setOtp] = useState(["", "", "", "", "", ""])
  const [otpError, setOtpError] = useState("")
  const [isVerifyingOTP, setIsVerifyingOTP] = useState(false)
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])
  const [resendCountdown, setResendCountdown] = useState(0)
  const [canResend, setCanResend] = useState(true)

  // Step 3: Password reset states
  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: "",
  })
  const [resetErrors, setResetErrors] = useState({
    password: "",
    confirmPassword: "",
  })
  const [isResettingPassword, setIsResettingPassword] = useState(false)

  // Countdown effect for resend OTP
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (resendCountdown > 0) {
      interval = setInterval(() => {
        setResendCountdown((prev) => {
          if (prev <= 1) {
            setCanResend(true)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [resendCountdown])

  // Step 1: Request OTP handlers
  const handleRequestSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email) {
      setRequestError("กรุณากรอกอีเมล")
      return
    }

    setIsRequestingOTP(true)
    setRequestError("")

    try {
      const response = await authAPI.requestOTP({ email })
      
      if (response.success) {
        console.log("OTP sent successfully")
        setCurrentStep('otp')
        // Start countdown when moving to OTP step
        setResendCountdown(60)
        setCanResend(false)
      } else {
        setRequestError(response.message || "เกิดข้อผิดพลาดในการส่ง OTP")
      }
    } catch (err) {
      console.error("Error sending OTP:", err)
      setRequestError("เกิดข้อผิดพลาดในการส่ง OTP")
    } finally {
      setIsRequestingOTP(false)
    }
  }

  // Step 2: OTP handling functions
  const handleOTPChange = (index: number, value: string) => {
    if (value && !/^\d+$/.test(value)) return

    const newOtp = [...otp]
    newOtp[index] = value.slice(0, 1)
    setOtp(newOtp)

    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault()
    const pastedData = e.clipboardData.getData('text')
    
    // Check if pasted data is exactly 6 digits
    if (/^\d{6}$/.test(pastedData)) {
      const newOtp = pastedData.split('')
      setOtp(newOtp)
      setOtpError("")
      // Focus on the last input after pasting
      inputRefs.current[5]?.focus()
    } else if (/^\d+$/.test(pastedData) && pastedData.length <= 6) {
      // Handle partial paste (less than 6 digits)
      const digits = pastedData.split('')
      const newOtp = [...otp]
      
      // Find the current focused input index
      const currentIndex = inputRefs.current.findIndex(ref => ref === document.activeElement)
      const startIndex = currentIndex >= 0 ? currentIndex : 0
      
      // Fill from current position
      for (let i = 0; i < digits.length && (startIndex + i) < 6; i++) {
        newOtp[startIndex + i] = digits[i]
      }
      
      setOtp(newOtp)
      setOtpError("")
      
      // Focus on the next empty input or last filled input
      const nextFocusIndex = Math.min(startIndex + digits.length, 5)
      inputRefs.current[nextFocusIndex]?.focus()
    }
  }

  const handleOTPSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (otp.some((digit) => !digit)) {
      setOtpError("กรุณากรอกรหัส OTP ให้ครบ")
      return
    }

    setIsVerifyingOTP(true)
    console.log("Verifying OTP:", otp.join(""))

    try {
      const response = await authAPI.verifyOTP({
        email,
        otp: otp.join(""),
      })

      if (response.success) {
        console.log("OTP verified successfully")
        setCurrentStep('reset')
        setOtpError("")
      } else {
        setOtpError(response.message || "เกิดข้อผิดพลาดในการยืนยัน OTP")
      }
    } catch (err) {
      console.error("Error verifying OTP:", err)
      setOtpError("เกิดข้อผิดพลาดในการยืนยัน OTP")
    } finally {
      setIsVerifyingOTP(false)
    }
  }

  const handleResendOTP = async () => {
    if (!canResend) return
    
    console.log("Resending OTP to:", email)
    setOtp(["", "", "", "", "", ""])
    setOtpError("")
    setCanResend(false)
    setResendCountdown(60)
    
    try {
      const response = await authAPI.requestOTP({ email })
      if (response.success) {
        // Show success message or toast
        console.log("OTP resent successfully")
      }
    } catch (err) {
      console.error("Error resending OTP:", err)
      setOtpError("เกิดข้อผิดพลาดในการส่งรหัส OTP")
      // Reset countdown on error
      setCanResend(true)
      setResendCountdown(0)
    }
  }

  // Step 3: Password reset handling functions
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
    
    if (resetErrors[name as keyof typeof resetErrors]) {
      setResetErrors((prev) => ({
        ...prev,
        [name]: "",
      }))
    }
  }

  const validatePassword = () => {
    const newErrors: any = {}
    
    if (!formData.password) {
      newErrors.password = "กรุณากรอกรหัสผ่าน"
    } else if (formData.password.length < 8) {
      newErrors.password = "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "กรุณายืนยันรหัสผ่าน"
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "รหัสผ่านไม่ตรงกัน"
    }
    
    return newErrors
  }

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const newErrors = validatePassword()
    setResetErrors(newErrors)

    if (Object.keys(newErrors).length === 0) {
      setIsResettingPassword(true)
      console.log("Resetting password for:", email)

      try {
        const response = await authAPI.resetPassword({
          email,
          otp: otp.join(""),
          password: formData.password,
        })
        
        if (response.success) {
          router.push("/login?message=password-reset-success")
        } else {
          // Handle error
          console.error("Password reset failed:", response.message)
        }
        
      } catch (err) {
        console.error("Error resetting password:", err)
      } finally {
        setIsResettingPassword(false)
      }
    }
  }

  // Navigation functions
  const handleBackToRequest = () => {
    setCurrentStep('request')
    setOtp(["", "", "", "", "", ""])
    setOtpError("")
    // Reset countdown when going back
    setResendCountdown(0)
    setCanResend(true)
  }

  const handleBackToOTP = () => {
    setCurrentStep('otp')
    setFormData({ password: "", confirmPassword: "" })
    setResetErrors({ password: "", confirmPassword: "" })
  }

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-50 to-indigo-100">
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes fade-in-blur {
            0% {
              opacity: 0;
              filter: blur(0px) brightness(100%);
            }
            50% {
              opacity: 0.7;
              filter: blur(2px) brightness(85%);
            }
            100% {
              opacity: 1;
              filter: blur(4px) brightness(70%);
            }
          }
          
          @keyframes fade-in-popup {
            0% {
              opacity: 0;
              transform: scale(0.8) translateY(30px);
            }
            60% {
              opacity: 0.8;
              transform: scale(1.02) translateY(-5px);
            }
            100% {
              opacity: 1;
              transform: scale(1) translateY(0);
            }
          }
          
          .animate-fade-in-blur {
            animation: fade-in-blur 1.5s ease-in-out forwards;
            opacity: 0;
          }
          
          .animate-fade-in-popup {
            animation: fade-in-popup 0.8s ease-out forwards 0.5s;
            opacity: 0;
            transform: scale(0.8) translateY(30px);
          }
        `
      }} />
      {/* Background Pattern */}
      {/* <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10" /> */}
      <img
        src="/e-med/img/SynphaetBuilding.jpg"
        alt="Background"
        className="absolute inset-0 w-full h-full object-cover animate-fade-in-blur"
        // style={{ filter: "blur(6px)" }}
      />
      
      {/* Content */}
      <div className="relative z-10 min-h-screen flex justify-center items-center">
        <div className="w-full max-w-4xl animate-fade-in-popup">
          <div className="bg-white/80 backdrop-blur-xl border border-white/20 rounded-3xl shadow-2xl overflow-hidden">
            <div className="flex flex-col md:flex-row min-h-[600px]">
              
              {/* Left Side - Branding */}
              <div className="relative hidden md:block w-1/2 bg-gradient-to-br from-[#53af9dcc] to-[#185f51]">
                <div className="absolute inset-0 bg-black/15" />
                <div className="relative z-10 p-12 flex flex-col justify-center h-full text-white">
                  <div className="mb-8">
                    <LockKeyhole size={64} className="mb-4 text-white/90" />
                    <h1 className="text-4xl font-bold mb-4">E-MED LEARNING</h1>
                    <p className="text-lg text-white/80">
                      ระบบจัดการรหัสผ่านที่ปลอดภัย
                    </p>
                  </div>
                  
                  {/* Progress Indicator */}
                  <div className="flex items-center space-x-4">
                    <div className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      currentStep === 'request' ? 'bg-white' : 'bg-white/40'
                    }`} />
                    <div className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      currentStep === 'otp' ? 'bg-white' : 'bg-white/40'
                    }`} />
                    <div className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      currentStep === 'reset' ? 'bg-white' : 'bg-white/40'
                    }`} />
                  </div>
                </div>
              </div>

              {/* Right Side - Dynamic Content */}
              <div className="w-full md:w-1/2 p-8 relative overflow-hidden">
                
                {/* Step 1: Request OTP */}
                <div className={`absolute inset-0 p-8 flex flex-col justify-center transition-all duration-500 ease-in-out ${
                  currentStep === 'request' ? 'transform translate-x-0 opacity-100' : 'transform -translate-x-full opacity-0 pointer-events-none'
                }`}>
                  <div className="text-center mb-8">
                    <div className="bg-gray-400/25 p-4 rounded-full inline-block mb-4">
                      <LockKeyhole size={48} className="text-[#293D97]" />
                    </div>
                    <h2 className="text-3xl font-bold text-gray-800 mb-2">รีเซ็ทรหัสผ่าน</h2>
                    <p className="text-gray-600">กรอกอีเมลเพื่อรับรหัสยืนยัน</p>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        อีเมล
                      </label>
                      <input
                        id="email"
                        name="email"
                        type="email"
                        required
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={isRequestingOTP}
                        className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-200 disabled:cursor-not-allowed transition-all duration-300"
                        placeholder="กรอกอีเมลของคุณ"
                      />
                      {requestError && <p className="text-red-600 text-sm mt-2">{requestError}</p>}
                    </div>

                    <button
                      onClick={handleRequestSubmit}
                      disabled={isRequestingOTP}
                      className="w-full bg-[#008268] hover:bg-[#00695c] disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-lg shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
                    >
                      {isRequestingOTP ? "กำลังส่ง..." : "ส่งรหัสยืนยัน"}
                    </button>
                  </div>

                  <div className="text-center mt-6">
                    <button 
                      onClick={() => router.push("/login")}
                      className="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-300"
                    >
                      กลับไปหน้าเข้าสู่ระบบ
                    </button>
                  </div>
                </div>

                {/* Step 2: OTP Verification */}
                <div className={`absolute inset-0 p-8 flex flex-col justify-center transition-all duration-500 ease-in-out ${
                  currentStep === 'otp' ? 'transform translate-x-0 opacity-100' : 'transform translate-x-full opacity-0 pointer-events-none'
                }`}>
                  <div className="mb-6">
                    {/* <button
                      onClick={handleBackToRequest}
                      disabled={isVerifyingOTP}
                      className="flex items-center text-blue-600 hover:text-blue-700 disabled:text-gray-400 disabled:cursor-not-allowed mb-4 transition-colors duration-300"
                    >
                      <ArrowLeft size={20} className="mr-2" />
                      กลับ
                    </button> */}
                    <h2 className="text-3xl font-bold text-gray-800 mb-2">กรอกรหัสยืนยัน</h2>
                    <p className="text-gray-600 text-sm">
                      กรอกรหัส OTP ที่ส่งไปยังอีเมล<br />
                      <span className="font-medium">{maskedEmail}</span>
                    </p>
                  </div>

                  <div className="space-y-6">
                    <div className="flex justify-center space-x-3">
                      {otp.map((digit, index) => (
                        <input
                          key={index}
                          ref={(el) => {
                            inputRefs.current[index] = el
                          }}
                          type="text"
                          value={digit}
                          onChange={(e) => handleOTPChange(index, e.target.value)}
                          onKeyDown={(e) => handleKeyDown(index, e)}
                          onPaste={handlePaste}
                          className="w-12 h-14 text-center text-xl bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 hover:shadow-md"
                          maxLength={1}
                          disabled={isVerifyingOTP}
                        />
                      ))}
                    </div>

                    {otpError && <p className="text-red-600 text-sm text-center">{otpError}</p>}

                    <button
                      onClick={handleOTPSubmit}
                      disabled={isVerifyingOTP}
                      className="w-full bg-[#008268] hover:bg-[#00695c] disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-lg shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
                    >
                      {isVerifyingOTP ? "กำลังยืนยัน..." : "ยืนยันรหัส"}
                    </button>
                  </div>

                  <div className="text-center mt-4">
                    <p className="text-sm text-gray-600">
                      ยังไม่ได้รับรหัส?{" "}
                      <button 
                        onClick={handleResendOTP} 
                        disabled={isVerifyingOTP || !canResend}
                        className="text-blue-600 hover:text-blue-700 disabled:text-gray-500/70 disabled:cursor-not-allowed font-medium transition-colors duration-300"
                      >
                        {!canResend ? `ส่งรหัสอีกครั้งใน ${resendCountdown} วินาที` : "ส่งรหัสอีกครั้ง"}
                      </button>
                    </p>
                  </div>
                </div>

                {/* Step 3: Password Reset */}
                <div className={`absolute inset-0 p-8 flex flex-col justify-between transition-all duration-500 ease-in-out ${
                  currentStep === 'reset' ? 'transform translate-x-0 opacity-100' : 'transform translate-x-full opacity-0 pointer-events-none'
                }`}>
                  <div>
                    <div className="mb-6">
                      <button
                        onClick={handleBackToOTP}
                        disabled={isResettingPassword}
                        className="flex items-center text-blue-600 hover:text-blue-700 disabled:text-gray-400 disabled:cursor-not-allowed mb-4 transition-colors duration-300"
                      >
                        <ArrowLeft size={20} className="mr-2" />
                        กลับ
                      </button>
                      <h2 className="text-3xl font-bold text-gray-800 mb-2">ตั้งรหัสผ่านใหม่</h2>
                      <p className="text-gray-600 text-sm">กรอกรหัสผ่านใหม่เพื่อเปลี่ยนรหัสผ่านของคุณ</p>
                    </div>

                    <div className="space-y-6">
                      <div>
                        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                          รหัสผ่านใหม่
                        </label>
                        <input
                          id="password"
                          name="password"
                          type="password"
                          required
                          value={formData.password}
                          onChange={handlePasswordChange}
                          disabled={isResettingPassword}
                          className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-200 disabled:cursor-not-allowed transition-all duration-300"
                          placeholder="กรอกรหัสผ่านใหม่"
                        />
                        {resetErrors.password && <p className="text-red-600 text-sm mt-1">{resetErrors.password}</p>}
                      </div>

                      <div>
                        <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                          ยืนยันรหัสผ่าน
                        </label>
                        <input
                          id="confirmPassword"
                          name="confirmPassword"
                          type="password"
                          required
                          value={formData.confirmPassword}
                          onChange={handlePasswordChange}
                          disabled={isResettingPassword}
                          className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-200 disabled:cursor-not-allowed transition-all duration-300"
                          placeholder="ยืนยันรหัสผ่านใหม่"
                        />
                        {resetErrors.confirmPassword && <p className="text-red-600 text-sm mt-1">{resetErrors.confirmPassword}</p>}
                      </div>
                    </div>
                  </div>

                  <button
                    type="submit"
                    onClick={handlePasswordSubmit}
                    disabled={isResettingPassword}
                    className="w-full bg-[#008268] hover:bg-[#00695c] disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-lg shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
                  >
                    {isResettingPassword ? "กำลังบันทึก..." : "บันทึกรหัสผ่านใหม่"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}