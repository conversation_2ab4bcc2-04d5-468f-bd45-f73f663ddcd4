export interface AssignPathType {
  path_id: number;
  learning_type: boolean;
}

export interface AssignPathRequest {
  path_id: number;
  user_slug: string;
  assign: AssignPathType[];
}

export interface AssignPathDeleteRequest {
  user_slug: string;
  path_ids: number[];
}

export interface AssignedPath {
  path_id: number;
  path_name: string;
  learning_type: boolean;
}

export interface AssignStudent {
  UserRole: number;
  user_slug: string;
  fname: string;
  lname: string;
  picture: string;
  email: string;
  position: string;
  status: string; // "1" for active, "0" for inactive, etc.
  assigned_paths: AssignedPath[];
}

export interface AssignStudentResponse {
  data: AssignStudent[];
}