@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Thai:wght@100;200;300;400;500;600;700&display=swap");
@import url("https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css");
@import url("https://cdn.jsdelivr.net/npm/highlight.js@11.11.1/styles/github.min.css");

/* Tiptap Editor Styles */
@import "../components/admin/tiptap-styles.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* สำหรับ iPad Mini ในแนวนอน */
@media (min-width: 1024px) and (max-width: 1199px) and (orientation: landscape) {
  .ipad-mini\:w-\[48vh\] {
    width: 48vh; /* ปรับขนาดตามที่ต้องการ */
  }
}

/* สำหรับ iPad Air ในแนวนอน */
@media (min-width: 1180px) and (max-width: 1280px) and (orientation: landscape) {
  .ipad-air\:w-\[50vh\] {
    width: 50vh; /* ปรับขนาดตามที่ต้องการ */
  }
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* Dark mode (ถ้าต้องการเปิดใช้) */
/*
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
*/

@font-face {
  font-family: 'Noto Sans Thai';
  src: url('../assets/fonts/NotoSansThai-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom Animations for Hero Section */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes gradientX {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes pulseSlow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
  }
}

/* New Gradient and Animation Effects for Hero Section */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes textShimmer {
  0% {
    transform: translateY(0px) scale(1);
    filter: brightness(1) contrast(1);
  }
  33% {
    transform: translateY(-2px) scale(1.02);
    filter: brightness(1.2) contrast(1.1);
  }
  66% {
    transform: translateY(1px) scale(0.98);
    filter: brightness(0.9) contrast(1.05);
  }
  100% {
    transform: translateY(0px) scale(1);
    filter: brightness(1) contrast(1);
  }
}

@keyframes rainbowShift {
  0% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg) brightness(1.2);
  }
  25% {
    background-position: 25% 75%;
    filter: hue-rotate(90deg) brightness(1.4);
  }
  50% {
    background-position: 100% 50%;
    filter: hue-rotate(180deg) brightness(1.6);
  }
  75% {
    background-position: 75% 25%;
    filter: hue-rotate(270deg) brightness(1.4);
  }
  100% {
    background-position: 0% 50%;
    filter: hue-rotate(360deg) brightness(1.2);
  }
}

@keyframes slowShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(2deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(-2deg);
  }
  66% {
    transform: translateY(-8px) rotate(1deg);
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(3deg);
  }
}

@keyframes float-reverse {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(15px) rotate(-2deg);
  }
}

@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes ping-slow {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75%, 100% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes animate-gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

/* New Hero animations */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

/* Animation Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-scale-in {
  animation: scaleIn 1s ease-out forwards;
}

.animate-gradient-x {
  animation: gradientX 3s ease infinite;
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulseSlow 4s ease-in-out infinite;
}

/* New Utility Classes for Animations */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 10s ease-in-out infinite;
}

.animate-float-reverse {
  animation: float-reverse 7s ease-in-out infinite;
}

.animate-bounce-slow {
  animation: bounce-slow 4s ease-in-out infinite;
}

.animate-ping-slow {
  animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animate-gradient-x {
  animation: animate-gradient-x 3s ease infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.bg-size-200 {
  background-size: 200% 200%;
}

/* Medical Learning Platform Animations */
@keyframes shimmer {
  0% { transform: translateX(-100%) skewX(-12deg); }
  100% { transform: translateX(200%) skewX(-12deg); }
}

@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes spin-slow {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 6s ease infinite;
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

/* Medical-themed Animation Keyframes */
@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(1.05);
  }
  75% {
    transform: scale(1.15);
  }
}

@keyframes blood-flow {
  0% {
    transform: translateX(-20px) translateY(0) rotate(0deg);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    transform: translateX(100px) translateY(-10px) rotate(180deg);
    opacity: 0;
  }
}

@keyframes dna-twist {
  0% {
    transform: rotateZ(0deg) rotateY(0deg);
  }
  50% {
    transform: rotateZ(180deg) rotateY(90deg);
  }
  100% {
    transform: rotateZ(360deg) rotateY(180deg);
  }
}

/* Medical Animation Classes */
.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

.animate-blood-flow {
  animation: blood-flow 4s linear infinite;
}

.animate-dna-twist {
  animation: dna-twist 6s linear infinite;
}

/* Blood Cell Inspired Animation Keyframes */
@keyframes blood-cell-flow {
  0% {
    transform: translateX(-50px) translateY(0px) scale(1);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    transform: translateX(200px) translateY(-30px) scale(0.8);
    opacity: 0;
  }
}

@keyframes cell-pulsation {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 20px 10px rgba(255, 107, 107, 0.1);
  }
}

@keyframes plasma-flow {
  0% {
    transform: rotate(0deg) translateX(30px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(30px) rotate(-360deg);
  }
}

@keyframes nutrient-drift {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-15px) translateX(10px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) translateX(0px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-15px) translateX(-10px) rotate(270deg);
    opacity: 1;
  }
}

@keyframes circulation-ring {
  0% {
    transform: rotate(0deg);
    border-color: rgba(255, 107, 107, 0.3);
  }
  33% {
    border-color: rgba(78, 205, 196, 0.4);
  }
  66% {
    border-color: rgba(255, 230, 109, 0.3);
  }
  100% {
    transform: rotate(360deg);
    border-color: rgba(255, 107, 107, 0.3);
  }
}

/* Blood Cell Animation Classes */
.animate-blood-cell-flow {
  animation: blood-cell-flow 6s linear infinite;
}

.animate-cell-pulsation {
  animation: cell-pulsation 2s ease-in-out infinite;
}

.animate-plasma-flow {
  animation: plasma-flow 8s linear infinite;
}

.animate-nutrient-drift {
  animation: nutrient-drift 5s ease-in-out infinite;
}

.animate-circulation-ring {
  animation: circulation-ring 10s linear infinite;
}

/* Delay Classes */
.delay-100 { animation-delay: 100ms; }
.delay-200 { animation-delay: 200ms; }
.delay-300 { animation-delay: 300ms; }
.delay-400 { animation-delay: 400ms; }
.delay-500 { animation-delay: 500ms; }
.delay-600 { animation-delay: 600ms; }
.delay-700 { animation-delay: 700ms; }
.delay-800 { animation-delay: 800ms; }
.delay-900 { animation-delay: 900ms; }
.delay-1000 { animation-delay: 1000ms; }

/* Gradient utilities */
.bg-gradient-radial {
  background: radial-gradient(var(--tw-gradient-stops));
}

::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-thumb {
  background-color: #8a8a8a;
  border-radius: 10px;
}

.ibm-plex-sans-thai-thin {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 100;
}
.ibm-plex-sans-thai-extralight { font-family: "IBM Plex Sans Thai"; font-weight: 200; }
.ibm-plex-sans-thai-light      { font-family: "IBM Plex Sans Thai"; font-weight: 300; }
.ibm-plex-sans-thai-regular    { font-family: "IBM Plex Sans Thai"; font-weight: 400; }
.ibm-plex-sans-thai-medium     { font-family: "IBM Plex Sans Thai"; font-weight: 500; }
.ibm-plex-sans-thai-semibold   { font-family: "IBM Plex Sans Thai"; font-weight: 600; }
.ibm-plex-sans-thai-bold       { font-family: "IBM Plex Sans Thai"; font-weight: 700; }

@layer components {
  input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    position: relative;
  }

  input[type="checkbox"]:checked {
    background-color: #008268;
    border-color: #008268;
  }

  input[type="checkbox"]:checked::after {
    content: "";
    position: absolute;
    top: 2px;
    left: 5px;
    width: 5px;
    height: 9px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  input[type="checkbox"]:focus {
    outline: none;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .hide-scrollbar::-webkit-scrollbar { display: none; }
  .hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }

  /* Hide scrollbar in iframe */
  .pdf-iframe { scrollbar-width: none; -ms-overflow-style: none; }
  .pdf-iframe::-webkit-scrollbar { display: none; }

  /* Also hide scrollbar in modal container */
  .hide-scrollbar::-webkit-scrollbar { display: none; }
  .hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
}

/* Tiptap Editor ProseMirror styles */
.ProseMirror {
  outline: none;
  padding: 12px;
  min-height: 200px;
  font-family: inherit;
  line-height: 1.6;
}
/* ... (other ProseMirror styles unchanged) ... */

/* Force Tiptap heading override */
.tiptap-editor h1, .tiptap-editor .ProseMirror h1 {
  font-size: 2.25rem !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin: 1.5rem 0 1rem 0 !important;
  color: #1f2937 !important;
}
.tiptap-editor h2, .tiptap-editor .ProseMirror h2 {
  font-size: 1.875rem !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
  margin: 1.25rem 0 0.875rem 0 !important;
  color: #374151 !important;
}
.tiptap-editor h3, .tiptap-editor .ProseMirror h3 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin: 1rem 0 0.75rem 0 !important;
  color: #4b5563 !important;
}
.tiptap-editor h4, .tiptap-editor .ProseMirror h4 {
  font-size: 1.25rem !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
  margin: 0.875rem 0 0.625rem 0 !important;
  color: #6b7280 !important;
}
.tiptap-editor h5, .tiptap-editor .ProseMirror h5 {
  font-size: 1.125rem !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
  margin: 0.75rem 0 0.5rem 0 !important;
  color: #6b7280 !important;
}
.tiptap-editor h6, .tiptap-editor .ProseMirror h6 {
  font-size: 1rem !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
  margin: 0.625rem 0 0.5rem 0 !important;
  color: #9ca3af !important;
}

/* Custom animations for exam statistics */
@keyframes shimmer {
  0% { transform: translateX(-100%) skewX(-12deg); }
  100% { transform: translateX(200%) skewX(-12deg); }
}

@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes spin-slow {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 6s ease infinite;
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

/* Glassmorphism enhancements */
.glass-card { backdrop-filter: blur(16px); -webkit-backdrop-filter: blur(16px); }
.glass-hover:hover { backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); }

/* Gentle animation for simplified text effect */
@keyframes gentleShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Heartbeat pulse graph animations */
@keyframes heartbeat-move {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  5% {
    opacity: 1;
  }
  95% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw);
    opacity: 0;
  }
}

@keyframes heartbeat-dot {
  0% {
    transform: translateX(-20px);
    opacity: 0;
    box-shadow: 0 0 15px #dc2626, 0 0 30px #dc262650;
  }
  5% {
    opacity: 1;
  }
  20% {
    transform: translateX(20vw);
    box-shadow: 0 0 15px #dc2626, 0 0 30px #dc262650;
  }
  22% {
    transform: translateX(21vw) scale(1.8);
    box-shadow: 0 0 25px #dc2626, 0 0 40px #dc2626;
  }
  24% {
    transform: translateX(22vw) scale(1);
    box-shadow: 0 0 15px #dc2626, 0 0 30px #dc262650;
  }
  26% {
    transform: translateX(23vw) scale(2);
    box-shadow: 0 0 30px #dc2626, 0 0 50px #dc2626;
  }
  28% {
    transform: translateX(24vw) scale(1);
    box-shadow: 0 0 15px #dc2626, 0 0 30px #dc262650;
  }
  30% {
    transform: translateX(25vw);
  }
  32% {
    transform: translateX(26vw) scale(1.5);
    box-shadow: 0 0 20px #dc2626, 0 0 35px #dc2626;
  }
  34% {
    transform: translateX(27vw) scale(1.8);
    box-shadow: 0 0 25px #dc2626, 0 0 40px #dc2626;
  }
  36% {
    transform: translateX(28vw) scale(1);
    box-shadow: 0 0 15px #dc2626, 0 0 30px #dc262650;
  }
  95% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw);
    opacity: 0;
  }
}

@keyframes heartbeat-dot-bottom {
  0% {
    transform: translateX(-20px);
    opacity: 0;
    box-shadow: 0 0 20px #dc2626, 0 0 40px #dc2626, 0 0 60px #dc262650;
  }
  5% {
    opacity: 1;
  }
  12% {
    transform: translateX(10vw);
    box-shadow: 0 0 20px #dc2626, 0 0 40px #dc2626, 0 0 60px #dc262650;
  }
  14% {
    transform: translateX(12vw) scale(2.5);
    box-shadow: 0 0 35px #dc2626, 0 0 60px #dc2626, 0 0 80px #dc2626;
  }
  16% {
    transform: translateX(14vw) scale(1);
    box-shadow: 0 0 20px #dc2626, 0 0 40px #dc2626, 0 0 60px #dc262650;
  }
  18% {
    transform: translateX(16vw) scale(3);
    box-shadow: 0 0 40px #dc2626, 0 0 70px #dc2626, 0 0 100px #dc2626;
  }
  20% {
    transform: translateX(18vw) scale(1);
    box-shadow: 0 0 20px #dc2626, 0 0 40px #dc2626, 0 0 60px #dc262650;
  }
  22% {
    transform: translateX(20vw);
  }
  24% {
    transform: translateX(22vw) scale(2);
    box-shadow: 0 0 30px #dc2626, 0 0 50px #dc2626, 0 0 70px #dc2626;
  }
  26% {
    transform: translateX(24vw) scale(2.8);
    box-shadow: 0 0 35px #dc2626, 0 0 60px #dc2626, 0 0 85px #dc2626;
  }
  28% {
    transform: translateX(26vw) scale(1);
    box-shadow: 0 0 20px #dc2626, 0 0 40px #dc2626, 0 0 60px #dc262650;
  }
  95% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw);
    opacity: 0;
  }
}

.animate-heartbeat-move {
  animation: heartbeat-move 4s linear infinite;
}

.animate-heartbeat-dot {
  animation: heartbeat-dot 4s linear infinite;
}

.animate-heartbeat-dot-bottom {
  animation: heartbeat-dot-bottom 4s linear infinite;
}
