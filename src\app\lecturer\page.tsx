import React from "react"
import LecturerLayout from '@/components/lecturer/layout'

export default function LecturerDashboardPage() {
  return (
    <LecturerLayout>
      <div className="max-w-4xl mx-auto py-10 px-4">
        <h1 className="text-3xl font-bold text-[#008268] mb-4">แดชบอร์ดผู้สอน (Mockup)</h1>
        <div className="rounded-xl bg-gradient-to-r from-[#e6eafd] via-[#f1f2fa] to-[#f6f7fc] p-6 shadow flex items-center gap-4 border border-[#b2b8df] mb-8">
          <div className="flex-shrink-0 flex items-center justify-center h-14 w-14 rounded-full bg-[#b2b8df]">
            <span className="text-2xl text-[#293D97] font-bold">👩‍🏫</span>
          </div>
          <div>
            <h2 className="text-2xl font-bold tracking-tight text-[#293D97] mb-1">ยินดีต้อนรับสู่ระบบผู้สอน</h2>
            <p className="text-[#3a4a7a] text-lg">จัดการคอร์ส ข้อสอบ และติดตามผลนักเรียนได้ที่นี่</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="rounded-lg border bg-white p-6 shadow-sm flex flex-col items-center">
            <span className="text-4xl mb-2">📚</span>
            <div className="text-lg font-semibold text-[#008268]">คอร์สที่ดูแล</div>
            <div className="text-2xl font-bold mt-1">10</div>
          </div>
          <div className="rounded-lg border bg-white p-6 shadow-sm flex flex-col items-center">
            <span className="text-4xl mb-2">📝</span>
            <div className="text-lg font-semibold text-[#008268]">ชุดข้อสอบ</div>
            <div className="text-2xl font-bold mt-1">4</div>
          </div>
        </div>
      </div>
    </LecturerLayout>
  )
}
