"use client"

import { type <PERSON>actNode, useState } from "react"
import SidebarLTR from "./sidebarLTR"
import NavbarLTR from "./navbarLTR"

interface LecturerLayoutProps {
  children: ReactNode
}

export default function LecturerLayout({ children }: LecturerLayoutProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)

  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <SidebarLTR 
        isCollapsed={isCollapsed} 
        setIsCollapsed={setIsCollapsed}
        isMobileOpen={isMobileOpen}
        setIsMobileOpen={setIsMobileOpen}
      />
      <NavbarLTR 
        isCollapsed={isCollapsed}
        toggleMobileSidebar={toggleMobileSidebar}
        isMobileOpen={isMobileOpen}
      />
      <main className={`pt-16 sm:pt-16 transition-all duration-300 ${isCollapsed ? "md:pl-20" : "md:pl-64"}`}>
        <div className="p-4 md:p-6">{children}</div>
      </main>
    </div>
  )
}
