"use client"

import type React from "react"

import { useState, useRef, useEffect, useCallback, useMemo } from "react"
import Image from "next/image"
import { Upload, ChevronDown, Plus, Trash2, Save, AlertTriangle, X, GripVertical, Check, Clock, ChevronLeft } from "lucide-react"
import { getCoursesData } from "@/data/allCourses"
import { quizService, QuizRequest } from '@/hook/quizService'
import { courseService } from '@/hook/courseService'
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core"
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { showSuccessAlert, showErrorAlert, showDeleteConfirmDialog, showConfirmDialog } from "@/lib/sweetAlert"
import { useRouter } from "next/navigation"

// Reusable Select Component
interface SelectOption {
  value: string;
  label: string;
}

interface OptimizedSelectProps {
  id: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  options: SelectOption[];
  placeholder: string;
  disabled?: boolean;
  className?: string;
}

const OptimizedSelect: React.FC<OptimizedSelectProps> = ({
  id,
  name,
  value,
  onChange,
  options,
  placeholder,
  disabled = false,
  className = "",
}) => (
  <div className="relative">
    <select
      id={id}
      name={name}
      value={value}
      onChange={onChange}
      className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10 ${className}`}
      disabled={disabled}
    >
      <option value="">{placeholder}</option>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
      <ChevronDown className="h-4 w-4 text-gray-500" />
    </div>
  </div>
);

// Constants for better maintainability
const DEFAULT_QUESTION_TIME = 30;
const DEFAULT_QUIZ_TIME_LIMIT = 30;
const PASSING_SCORE = 100;
const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
const IMAGE_MAX_WIDTH = 500;
const IMAGE_MAX_HEIGHT = 500;
const JPEG_QUALITY = 0.9;

// Helper functions
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs < 10 ? "0" : ""}${secs}`
}

const generateQuestionId = (index: number) => `question-${index + 1}`;
const generateChoiceId = (questionIndex: number, choiceIndex: number) => `q${questionIndex + 1}-choice-${choiceIndex + 1}`;

// Image resize utility
const resizeImage = (file: File, maxWidth: number, maxHeight: number): Promise<string> => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = (event) => {
      const img = document.createElement("img")
      img.src = event.target?.result as string
      img.onload = () => {
        const canvas = document.createElement("canvas")
        let width = img.width
        let height = img.height

        // คำนวณขนาดใหม่โดยรักษาอัตราส่วน
        if (width > height) {
          if (width > maxWidth) {
            height = Math.round((height * maxWidth) / width)
            width = maxWidth
          }
        } else {
          if (height > maxHeight) {
            width = Math.round((width * maxHeight) / height)
            height = maxHeight
          }
        }

        canvas.width = width
        canvas.height = height
        const ctx = canvas.getContext("2d")
        ctx?.drawImage(img, 0, 0, width, height)

        // แปลงเป็น base64
        const dataUrl = canvas.toDataURL("image/jpeg", JPEG_QUALITY)
        resolve(dataUrl)
      }
    }
  })
}

// Default choice template
const createDefaultChoice = (questionIndex: number, choiceIndex: number): Choice => ({
  id: generateChoiceId(questionIndex, choiceIndex),
  content: "",
  type: "text",
  isCorrect: false,
});

// Default question template
const createDefaultQuestion = (index: number): Question => ({
  id: generateQuestionId(index),
  title: "",
  type: "text",
  content: "",
  choices: Array.from({ length: 4 }, (_, i) => createDefaultChoice(index, i)),
  videoTimestamp: DEFAULT_QUESTION_TIME,
});

type QuestionType = "text" | "image" | "text_and_image" | "video"
type ChoiceType = "text" | "image"

interface Choice {
  id: string
  slug?: string // เพิ่ม slug เพื่อใช้ในการแก้ไขตัวเลือก
  content: string
  type: ChoiceType
  isCorrect: boolean
  imageUrl?: string
}

interface Question {
  id: string
  slug?: string // เพิ่ม slug เพื่อใช้ในการแก้ไขคำถาม
  title: string
  type: QuestionType
  content: string
  imageUrl?: string
  videoTimestamp?: number // เพิ่มเวลาที่จะแสดงคำถามในวิดีโอ (วินาที)
  choices: Choice[] // เพิ่ม choices เพื่อแก้ไขข้อผิดพลาด
}

interface QuizFormData {
  id: string
  slug?: string // เพิ่ม slug เพื่อใช้ในการแก้ไขควิซ
  name: string
  course: string
  courseId: string
  lessonId: string // เพิ่ม lessonId
  contentId: string // เพิ่ม contentId
  description: string
  timeLimit: number
  passingScore: number
  status: string
  questions: Question[]
}

// SortableQuestion component for drag and drop
function SortableQuestion({
  question,
  index,
  isSelected,
  onSelect,
}: {
  question: Question
  index: number
  isSelected: boolean
  onSelect: () => void
}) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: question.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.8 : 1,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`p-3 rounded-md cursor-pointer mb-2 ${isSelected ? "bg-[#E6F2F0] border border-[#008268]" : "bg-white border border-gray-200"
        } ${isDragging ? "shadow-md" : ""}`}
    >
      <div className="flex items-center">
        <div
          {...attributes}
          {...listeners}
          className="mr-2 cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600"
        >
          <GripVertical size={16} />
        </div>
        <span className="text-sm font-medium mr-2">{index + 1}.</span>
        <div className="flex-1 text-sm truncate" onClick={onSelect}>
          {question.title ? question.title : `คำถามที่ ${index + 1}`}
        </div>
        {question.videoTimestamp !== undefined && (
          <div className="flex items-center text-xs text-gray-500 ml-2">
            <Clock size={12} className="mr-1" />
            <span>{formatTime(question.videoTimestamp)}</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default function QuizEditor({ quizId }: { quizId?: string }) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<"quiz" | "questions">("quiz")
  const [selectedQuestion, setSelectedQuestion] = useState<number>(0)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const questionImageRef = useRef<HTMLInputElement>(null)
  const choiceImageRefs = useRef<(HTMLInputElement | null)[]>([])
  const questionDropzoneRef = useRef<HTMLDivElement>(null)
  const choiceDropzoneRefs = useRef<(HTMLDivElement | null)[]>([])

  // State for courses
  const [courses, setCourses] = useState<any[]>([])
  const [isLoadingCourses, setIsLoadingCourses] = useState(true)
  const [coursesError, setCoursesError] = useState<string | null>(null)

  // State สำหรับเก็บบทเรียนของคอร์สที่เลือก
  const [lessons, setLessons] = useState<any[]>([])
  // State สำหรับเก็บเนื้อหาของบทเรียนที่เลือก
  const [contents, setContents] = useState<any[]>([])

  const [formData, setFormData] = useState<QuizFormData>({
    id: `quiz-${Date.now()}`,
    name: "",
    course: "",
    courseId: "",
    lessonId: "",
    contentId: "",
    description: "",
    timeLimit: DEFAULT_QUIZ_TIME_LIMIT,
    passingScore: PASSING_SCORE,
    status: "draft",
    questions: [createDefaultQuestion(0)],
  })
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [questionToDelete, setQuestionToDelete] = useState<number | null>(null)
  const [saveSuccess, setSaveSuccess] = useState<boolean | null>(null)
  const [showSaveConfirm, setShowSaveConfirm] = useState(false)
  const [isDraggingOver, setIsDraggingOver] = useState<number | null>(null)
  const [isQuestionDraggingOver, setIsQuestionDraggingOver] = useState(false)

  // Memoized select options
  const courseOptions = useMemo(() => 
    courses.map(course => ({
      value: course.slug,
      label: course.course_name
    })), 
    [courses]
  );

  const lessonOptions = useMemo(() => 
    lessons.map(lesson => ({
      value: lesson.slug,
      label: lesson.name
    })), 
    [lessons]
  );

  const contentOptions = useMemo(() => 
    contents.map(content => ({
      value: content.slug,
      label: content.name || `วิดีโอ ${content.slug}`
    })), 
    [contents]
  );

  // Memoized values
  const currentQuestion = useMemo(() => 
    formData.questions[selectedQuestion], 
    [formData.questions, selectedQuestion]
  );

  const isFormValid = useMemo(() => {
    return formData.name.trim() && 
           formData.courseId && 
           formData.lessonId && 
           formData.contentId &&
           formData.questions.every(q => 
             q.title.trim() && 
             q.choices.every(c => c.type === "image" || c.content.trim()) &&
             q.choices.some(c => c.isCorrect) &&
             q.videoTimestamp !== undefined
           );
  }, [formData]);

  // Callback functions for better performance
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }, []);

  const handleQuestionInputChange = useCallback((
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    index: number,
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions.map((question, i) => 
        i === index ? { ...question, [name]: value } : question
      )
    }))
  }, []);

  const handleVideoTimestampChange = useCallback((e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const value = Number.parseInt(e.target.value, 10) || 0
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions.map((question, i) => 
        i === index ? { ...question, videoTimestamp: value } : question
      )
    }))
  }, []);

  const handleChoiceInputChange = useCallback((
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    questionIndex: number,
    choiceIndex: number,
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions.map((question, qIndex) => 
        qIndex === questionIndex 
          ? {
              ...question,
              choices: question.choices.map((choice, cIndex) => 
                cIndex === choiceIndex ? { ...choice, [name]: value } : choice
              )
            }
          : question
      )
    }))
  }, []);

  const handleCorrectAnswerChange = useCallback((questionIndex: number, choiceIndex: number) => {
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions.map((question, qIndex) => 
        qIndex === questionIndex 
          ? {
              ...question,
              choices: question.choices.map((choice, cIndex) => 
                cIndex === choiceIndex ? { ...choice, isCorrect: !choice.isCorrect } : choice
              )
            }
          : question
      )
    }))
  }, []);

  const handleChoiceTypeChange = useCallback((questionIndex: number, choiceIndex: number, type: ChoiceType) => {
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions.map((question, qIndex) => 
        qIndex === questionIndex 
          ? {
              ...question,
              choices: question.choices.map((choice, cIndex) => 
                cIndex === choiceIndex ? { ...choice, type } : choice
              )
            }
          : question
      )
    }))
  }, []);

  // Optimized useEffect hooks
  useEffect(() => {
    if (formData.questions.length > 0 && selectedQuestion < formData.questions.length) {
      const currentChoicesLength = formData.questions[selectedQuestion]?.choices?.length || 0;
      choiceImageRefs.current = Array(currentChoicesLength).fill(null);
      choiceDropzoneRefs.current = Array(currentChoicesLength).fill(null);
    }
  }, [selectedQuestion, formData.questions]);

  // Update lessons when courseId changes
  useEffect(() => {
    if (!formData.courseId) {
      setLessons([]);
      // setFormData((prev) => ({ ...prev, lessonId: '', contentId: '' }));
      return;
    }

    courseService.getCourseBySlug(formData.courseId)
      .then((course) => {
        setLessons(course.lessons || []);
      })
      .catch(() => {
        setLessons([]);
      });
  }, [formData.courseId]);

  // Update contents when lessonId changes
  useEffect(() => {
    if (!formData.lessonId || lessons.length === 0) {
      setContents([]);
      // setFormData((prev) => ({ ...prev, contentId: '' }));
      return;
    }

    const selectedLesson = lessons.find((lesson) => String(lesson.slug) === String(formData.lessonId));
    if (selectedLesson?.content) {
      const videoContents = selectedLesson.content.filter((c: any) => c.typecontent === 'video');
      setContents(videoContents);
      
      const firstVideoContent = videoContents.find((c: any) => c.typecontent === 'video');
      // setFormData((prev) => ({
      //   ...prev,
      //   contentId: firstVideoContent?.id || ''
      // }));
    } else {
      setContents([]);
      setFormData((prev) => ({ ...prev, contentId: '' }));
    }
  }, [formData.lessonId, lessons]);

  // Optimized course selection handler
  const handleCourseChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const courseId = e.target.value;
    const selectedCourse = courses.find((course) => course.slug === courseId);
    
    setFormData((prev) => ({
      ...prev,
      courseId,
      course: selectedCourse ? selectedCourse.course_name : '',
      lessonId: '', // Reset lesson when course changes
      contentId: '', // Reset content when course changes
    }));
  }, [courses]);

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  // Optimized image upload handlers
  const handleQuestionImageUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>, questionIndex: number) => {
    const file = e.target.files?.[0]
    if (!file) return;

    if (file.size > MAX_FILE_SIZE) {
      await showErrorAlert("ขนาดไฟล์ต้องไม่เกิน 2MB");
      return
    }

    try {
      const resizedImage = await resizeImage(file, IMAGE_MAX_WIDTH, IMAGE_MAX_HEIGHT)
      setFormData((prev) => ({
        ...prev,
        questions: prev.questions.map((question, i) => 
          i === questionIndex ? { ...question, imageUrl: resizedImage } : question
        )
      }))
    } catch (error) {
      console.error("Error resizing image:", error)
      await showErrorAlert("เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ");
    }
  }, []);

  const handleChoiceImageUpload = useCallback(async (
    e: React.ChangeEvent<HTMLInputElement>,
    questionIndex: number,
    choiceIndex: number,
  ) => {
    const file = e.target.files?.[0]
    if (!file) return;

    if (file.size > MAX_FILE_SIZE) {
      await showErrorAlert("ขนาดไฟล์ต้องไม่เกิน 2MB");
      return
    }

    try {
      const resizedImage = await resizeImage(file, IMAGE_MAX_WIDTH, IMAGE_MAX_HEIGHT)
      setFormData((prev) => ({
        ...prev,
        questions: prev.questions.map((question, qIndex) => 
          qIndex === questionIndex 
            ? {
                ...question,
                choices: question.choices.map((choice, cIndex) => 
                  cIndex === choiceIndex ? { ...choice, imageUrl: resizedImage } : choice
                )
              }
            : question
        )
      }))
    } catch (error) {
      console.error("Error resizing image:", error)
      await showErrorAlert("เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ");
    }
  }, []);

  const handleQuestionDrop = useCallback(async (e: React.DragEvent<HTMLDivElement>, questionIndex: number) => {
    e.preventDefault()
    setIsQuestionDraggingOver(false)

    const files = e.dataTransfer.files
    if (files.length === 0) return;

    const file = files[0]
    if (!file.type.match("image.*")) {
      await showErrorAlert("กรุณาอัพโหลดไฟล์รูปภาพเท่านั้น");
      return
    }

    if (file.size > MAX_FILE_SIZE) {
      await showErrorAlert("ขนาดไฟล์ต้องไม่เกิน 2MB");
      return
    }

    try {
      const resizedImage = await resizeImage(file, IMAGE_MAX_WIDTH, IMAGE_MAX_HEIGHT)
      setFormData((prev) => ({
        ...prev,
        questions: prev.questions.map((question, i) => 
          i === questionIndex ? { ...question, imageUrl: resizedImage } : question
        )
      }))
    } catch (error) {
      console.error("Error resizing image:", error)
      await showErrorAlert("เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ");
    }
  }, []);

  const handleChoiceDrop = useCallback(async (e: React.DragEvent<HTMLDivElement>, questionIndex: number, choiceIndex: number) => {
    e.preventDefault()
    setIsDraggingOver(null)

    const files = e.dataTransfer.files
    if (files.length === 0) return;

    const file = files[0]
    if (!file.type.match("image.*")) {
      await showErrorAlert("กรุณาอัพโหลดไฟล์รูปภาพเท่านั้น");
      return
    }

    if (file.size > MAX_FILE_SIZE) {
      await showErrorAlert("ขนาดไฟล์ต้องไม่เกิน 2MB");
      return
    }

    try {
      const resizedImage = await resizeImage(file, IMAGE_MAX_WIDTH, IMAGE_MAX_HEIGHT)
      setFormData((prev) => ({
        ...prev,
        questions: prev.questions.map((question, qIndex) => 
          qIndex === questionIndex 
            ? {
                ...question,
                choices: question.choices.map((choice, cIndex) => 
                  cIndex === choiceIndex ? { ...choice, imageUrl: resizedImage } : choice
                )
              }
            : question
        )
      }))
    } catch (error) {
      console.error("Error resizing image:", error)
      await showErrorAlert("เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ");
    }
  }, []);

  const triggerQuestionImageInput = useCallback(() => {
    questionImageRef.current?.click()
  }, []);

  const triggerChoiceImageInput = useCallback((index: number) => {
    choiceImageRefs.current[index]?.click()
  }, []);

  const addNewQuestion = useCallback(() => {
    const newQuestion = createDefaultQuestion(formData.questions.length);
    setFormData((prev) => ({
      ...prev,
      questions: [...prev.questions, newQuestion],
    }))
    setSelectedQuestion(formData.questions.length)
  }, [formData.questions.length]);

  // --- ลบคำถาม ---
  const deleteQuestion = useCallback((index: number) => {
    if (formData.questions.length <= 1) return;
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index),
    }));
    const updatedLength = formData.questions.length - 1;
    if (selectedQuestion >= updatedLength) {
      setSelectedQuestion(updatedLength - 1);
    } else if (selectedQuestion === index) {
      setSelectedQuestion(Math.max(0, index - 1));
    }
  }, [formData.questions.length, selectedQuestion]);

  // --- ยืนยันลบคำถาม ---
  const confirmDeleteQuestion = useCallback(async (index: number) => {
    const result = await showDeleteConfirmDialog(
      `คุณแน่ใจหรือไม่ว่าต้องการลบคำถาม "${formData.questions[index]?.title || `คำถามที่ ${index + 1}` }"? การกระทำนี้ไม่สามารถย้อนกลับได้`
    );
    if (result.isConfirmed) {
      deleteQuestion(index);
    }
  }, [formData.questions, deleteQuestion]);

  const saveQuestion = useCallback((index: number) => {
    console.log("Saving question:", formData.questions[index])
    setSaveSuccess(true)
    setTimeout(() => setSaveSuccess(null), 3000)
  }, [formData.questions]);

  // Optimized form validation
  const validateForm = useCallback(() => {
    const validationErrors: string[] = [];

    if (!formData.name.trim()) {
      validationErrors.push("กรุณากรอกชื่อควิซ");
      setActiveTab("quiz");
    }

    if (!formData.courseId) {
      validationErrors.push("กรุณาเลือกคอร์สที่เกี่ยวข้อง");
      setActiveTab("quiz");
    }

    if (!formData.lessonId) {
      validationErrors.push("กรุณาเลือกบทเรียนที่เกี่ยวข้อง");
      setActiveTab("quiz");
    }

    if (!formData.contentId) {
      validationErrors.push("กรุณาเลือกเนื้อหาวิดีโอที่เกี่ยวข้อง");
      setActiveTab("quiz");
    }

    // Validate questions
    for (let i = 0; i < formData.questions.length; i++) {
      const question = formData.questions[i];
      
      if (!question.title.trim()) {
        setSelectedQuestion(i);
        setActiveTab("questions");
        validationErrors.push(`กรุณากรอกคำถามข้อที่ ${i + 1}`);
        break;
      }

      const emptyChoices = question.choices.some((c) => c.type === "text" && !c.content.trim());
      if (emptyChoices) {
        setSelectedQuestion(i);
        setActiveTab("questions");
        validationErrors.push(`กรุณากรอกตัวเลือกให้ครบทุกข้อในคำถามข้อที่ ${i + 1}`);
        break;
      }

      const hasCorrectAnswer = question.choices.some((c) => c.isCorrect);
      if (!hasCorrectAnswer) {
        setSelectedQuestion(i);
        setActiveTab("questions");
        validationErrors.push(`กรุณาเลือกคำตอบที่ถูกต้องอย่างน้อย 1 ข้อในคำถามข้อที่ ${i + 1}`);
        break;
      }

      if (question.videoTimestamp === undefined) {
        setSelectedQuestion(i);
        setActiveTab("questions");
        validationErrors.push(`กรุณากำหนดเวลาที่จะแสดงคำถามในวิดีโอสำหรับคำถามข้อที่ ${i + 1}`);
        break;
      }
    }

    return validationErrors;
  }, [formData]);

  // --- บันทึกควิซและแจ้งเตือนด้วย showSuccessAlert/showErrorAlert ---
  const saveAllChanges = useCallback(async () => {
    try {
      const quizReq: QuizRequest = {
        quiz_name: formData.name,
        quiz_description: formData.description,
        quiz_number: 1,
        quiz_time: Number(formData.timeLimit),
        quiz_status: formData.status === 'published',
        content_lesson_id: formData.contentId || '',
        slug: formData.slug || '',
        question: formData.questions.map((q) => ({
          question: q.title,
          slug: q.slug || '',
          detail: q.content || '',
          time_insert: q.videoTimestamp || 0,
          question_image: q.imageUrl || '',
          question_type: q.type === 'text' ? 1 : q.type === 'image' ? 2 : 3,
          choice: q.choices.map((c) => ({
            choice: c.content,
            slug: c.slug || '',
            choice_image: c.imageUrl || '',
            is_correct: !!c.isCorrect,
            choice_type: c.type === 'text' ? 1 : c.type === 'image' ? 2 : 1,
          })),
        })),
      };
      if (!quizId) {
        await quizService.createQuiz(quizReq);
      } else {
        await quizService.updateQuiz(quizId, quizReq);
      }
      await showSuccessAlert('บันทึกสำเร็จ', 'ควิซถูกบันทึกเรียบร้อย');
      setTimeout(() => {
        router.push('/admin/quiz/');
      }, 800);
    } catch (error: any) {
      await showErrorAlert('เกิดข้อผิดพลาด', error.message || 'เกิดข้อผิดพลาดในการบันทึกข้อมูล');
    }
  }, [formData, quizId, router]);

  // Drag event handlers (must be defined before use)
  const handleQuestionDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsQuestionDraggingOver(true)
  }, []);

  const handleQuestionDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsQuestionDraggingOver(false)
  }, []);

  const handleChoiceDragOver = useCallback((e: React.DragEvent<HTMLDivElement>, choiceIndex: number) => {
    e.preventDefault()
    setIsDraggingOver(choiceIndex)
  }, []);

  const handleChoiceDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDraggingOver(null)
  }, []);

  // --- ยืนยันบันทึกควิซด้วย showConfirmDialog ---
  const confirmSaveAllChanges = useCallback(async () => {
    const errors = validateForm();
    if (errors.length > 0) {
      await showErrorAlert('เกิดข้อผิดพลาด', errors[0]);
      return;
    }
    const result = await showConfirmDialog(
      `คุณแน่ใจหรือไม่ว่าต้องการบันทึกควิซ "${formData.name}"? ควิซนี้มีคำถามทั้งหมด ${formData.questions.length} ข้อ`
    );
    if (result.isConfirmed) {
      await saveAllChanges();
    }
  }, [validateForm, formData, saveAllChanges]);

  // Optimized drag and drop handler
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event

    if (over && active.id !== over.id) {
      setFormData((prev) => {
        const oldIndex = prev.questions.findIndex((question) => question.id === active.id)
        const newIndex = prev.questions.findIndex((question) => question.id === over.id)

        // Update selectedQuestion to follow the dragged item
        if (selectedQuestion === oldIndex) {
          setSelectedQuestion(newIndex)
        } else if (selectedQuestion >= newIndex && selectedQuestion < oldIndex) {
          setSelectedQuestion(selectedQuestion + 1)
        } else if (selectedQuestion <= newIndex && selectedQuestion > oldIndex) {
          setSelectedQuestion(selectedQuestion - 1)
        }

        return {
          ...prev,
          questions: arrayMove(prev.questions, oldIndex, newIndex),
        }
      })
    }
  }, [selectedQuestion]);

  // Optimized data fetching effects
  useEffect(() => {
    if (!quizId) return;

    const fetchQuizData = async () => {
      try {
        const quiz = await quizService.getQuizById(quizId);
        setFormData({
          id: quiz.id,
          name: quiz.quiz_name || '',
          course: '',
          courseId: quiz.course_slug || '',
          lessonId: quiz.lesson_slug || '',
          contentId: quiz.content_slug || '',
          description: quiz.quiz_description || '',
          timeLimit: quiz.quiz_time || DEFAULT_QUIZ_TIME_LIMIT,
          passingScore: PASSING_SCORE,
          status: quiz.quiz_status ? 'published' : 'draft',
          slug: quiz.slug || '',
          questions: (quiz.questions || []).map((q: any, index: number) => ({
            id: q.id || generateQuestionId(index),
            slug: q.slug || '',
            title: q.question || '',
            type: q.question_type === 1 ? 'text' : q.question_type === 2 ? 'image' : 'text_and_image',
            content: q.detail || '',
            imageUrl: q.question_image || '',
            videoTimestamp: q.time_insert || DEFAULT_QUESTION_TIME,
            choices: (q.choices || []).map((c: any, cIndex: number) => ({
              id: c.id || generateChoiceId(index, cIndex),
              slug: c.slug || '',
              content: c.choice || '',
              type: c.choice_type === 1 ? 'text' : c.choice_type === 2 ? 'image' : 'text',
              isCorrect: !!c.is_correct,
              imageUrl: c.choice_image || '',
            })),
          })),
        });
      } catch (err: any) {
        alert(err.message || 'เกิดข้อผิดพลาดในการโหลดข้อมูลควิซ');
      }
    };

    fetchQuizData();
  }, [quizId]);

  useEffect(() => {
    console.log("Form data updated:", formData);
  }, [formData]);

  // Fetch courses on mount
  useEffect(() => {
    const fetchCourses = async () => {
      setIsLoadingCourses(true);
      try {
        const data = await courseService.getCourses();
        setCourses(data);
      } catch (err: any) {
        setCoursesError(err.message || 'เกิดข้อผิดพลาดในการโหลดข้อมูลคอร์ส');
      } finally {
        setIsLoadingCourses(false);
      }
    };

    fetchCourses();
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header with ChevronLeft back button and save button */}
      <div className="flex justify-between items-center p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <button
            onClick={() => router.push('/admin/quiz/')}
            className="p-0  border-none focus:outline-none flex items-center"
            aria-label="ย้อนกลับไปหน้าตารางควิซ"
            type="button"
          >
            <ChevronLeft className="h-7 w-7" />
          </button>
          <h1 className="text-xl font-bold text-gray-800">{quizId ? "แก้ไขควิซ" : "สร้างควิซใหม่"}</h1>
        </div>
        <div className="flex items-center gap-3">
          {saveSuccess && (
            <div className="bg-green-50 text-green-700 px-3 py-1 rounded-md flex items-center">
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              บันทึกสำเร็จ
            </div>
          )}
          <button
            onClick={confirmSaveAllChanges}
            className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-md font-medium flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            บันทึก
          </button>
        </div>
      </div>

      {/* Navigation tabs */}
      <div className="flex border-b border-gray-200">
        <button
          type="button"
          className={`px-6 py-3 text-sm font-medium ${activeTab === "quiz" ? "text-[#008268] border-b-2 border-[#008268]" : "text-gray-500 hover:text-gray-700"
            }`}
          onClick={() => setActiveTab("quiz")}
        >
          ข้อมูลควิซ
        </button>
        <button
          type="button"
          className={`px-6 py-3 text-sm font-medium ${activeTab === "questions"
            ? "text-[#008268] border-b-2 border-[#008268]"
            : "text-gray-500 hover:text-gray-700"
            }`}
          onClick={() => setActiveTab("questions")}
        >
          จัดการคำถาม
        </button>
      </div>

      {/* Quiz form */}
      {activeTab === "quiz" && (
        <div className="p-6">
          {/* Two columns for details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left column - Quiz details */}
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-4">รายละเอียดของควิซ</h3>

                <div className="mb-4">
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    ชื่อควิซ
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกชื่อควิซ"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="courseId" className="block text-sm font-medium text-gray-700 mb-1">
                    คอร์สที่เกี่ยวข้อง
                  </label>
                  <div className="relative">
                    <select
                      id="courseId"
                      name="courseId"
                      value={formData.courseId}
                      onChange={handleCourseChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                      disabled={isLoadingCourses}
                    >
                      <option value="">{isLoadingCourses ? 'กำลังโหลด...' : 'เลือกคอร์ส'}</option>
                      {courses.map((course) => (
                        <option key={course.slug} value={course.slug}>
                          {course.course_name}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                  {isLoadingCourses && (
                    <p className="text-xs text-gray-500 mt-1">กำลังโหลดข้อมูลคอร์ส...</p>
                  )}
                  {coursesError && (
                    <p className="text-xs text-red-500 mt-1">{coursesError}</p>
                  )}
                </div>

                {/* เพิ่มส่วนเลือกบทเรียน */}
                <div className="mb-4">
                  <label htmlFor="lessonId" className="block text-sm font-medium text-gray-700 mb-1">
                    บทเรียนที่เกี่ยวข้อง
                  </label>
                  <div className="relative">
                    <select
                      id="lessonId"
                      name="lessonId"
                      value={formData.lessonId}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                      disabled={!formData.courseId || lessons.length === 0}
                    >
                      <option value="">{!formData.lessonId ? 'เลือกคอร์สก่อน' : lessons.length === 0 ? 'ไม่พบบทเรียน' : 'เลือกบทเรียน'}</option>
                      {lessons.map((lesson) => (
                        <option key={lesson.slug} value={lesson.slug}>
                          {lesson.name}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                  {!formData.courseId && (
                    <p className="text-xs text-gray-500 mt-1">กรุณาเลือกคอร์สก่อน</p>
                  )}
                  {formData.courseId && lessons.length === 0 && (
                    <p className="text-xs text-red-500 mt-1">ไม่พบบทเรียนในคอร์สนี้</p>
                  )}
                </div>

                {/* เพิ่มส่วนเลือกเนื้อหาวิดีโอ */}
                <div className="mb-4">
                  <label htmlFor="contentId" className="block text-sm font-medium text-gray-700 mb-1">
                    เนื้อหาวิดีโอที่เกี่ยวข้อง
                  </label>
                  <div className="relative">
                    <select
                      id="contentId"
                      name="contentId"
                      value={formData.contentId}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                      disabled={!formData.lessonId || contents.length === 0}
                    >
                      <option value="">{!formData.lessonId ? 'เลือกบทเรียนก่อน' : contents.length === 0 ? 'ไม่พบเนื้อหาวิดีโอ' : 'เลือกเนื้อหาวิดีโอ'}</option>
                      {contents.map((content) => (
                        <option key={content.slug} value={content.slug}>
                          {content.name || `วิดีโอ ${content.slug}`}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                  {!formData.lessonId && (
                    <p className="text-xs text-gray-500 mt-1">กรุณาเลือกบทเรียนก่อน</p>
                  )}
                  {formData.lessonId && contents.length === 0 && (
                    <p className="text-xs text-red-500 mt-1">ไม่พบเนื้อหาวิดีโอในบทเรียนนี้</p>
                  )}
                </div>

                <div className="mb-4">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    คำอธิบายควิซ
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={5}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกคำอธิบายควิซ"
                  />
                </div>
              </div>
            </div>

            {/* Right column - Additional details */}
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-4">รายละเอียดเพิ่มเติม</h3>

                <div className="mb-4">
                  <label htmlFor="timeLimit" className="block text-sm font-medium text-gray-700 mb-1">
                    เวลาในการทำควิซ (นาที)
                  </label>
                  <input
                    type="number"
                    id="timeLimit"
                    name="timeLimit"
                    min="1"
                    max="180"
                    value={formData.timeLimit}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                    สถานะควิซ
                  </label>
                  <div className="relative">
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                    >
                      <option value="draft">แบบร่าง</option>
                      <option value="published">เผยแพร่</option>
                      <option value="disabled">ปิดใช้งาน</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">จำนวนคำถาม</label>
                  <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-700">
                    {formData.questions.length} คำถาม
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Questions form */}
      {activeTab === "questions" && (
        <div className="flex h-[calc(100vh-220px)] min-h-[500px]">
          {/* Left sidebar - Question list with drag and drop */}
          <div className="w-full md:w-1/3 lg:w-3/10 border-r border-gray-200 bg-gray-50 p-4 overflow-y-auto">
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext
                items={formData.questions.map((question) => question.id)}
                strategy={verticalListSortingStrategy}
              >
                {formData.questions.map((question, index) => (
                  <SortableQuestion
                    key={question.id}
                    question={question}
                    index={index}
                    isSelected={selectedQuestion === index}
                    onSelect={() => setSelectedQuestion(index)}
                  />
                ))}
              </SortableContext>
            </DndContext>

            <button
              onClick={addNewQuestion}
              className="mt-4 w-full flex items-center justify-center p-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50"
            >
              <Plus className="h-4 w-4 mr-1" />
              <span className="text-sm">เพิ่มคำถาม</span>
            </button>
          </div>

          {/* Right content - Question editor */}
          <div className="w-full md:w-2/3 lg:w-7/10 p-6 overflow-y-auto">
            {formData.questions.length > 0 && (
              <div>
                <div className="mb-4">
                  <label htmlFor="questionType" className="block text-sm font-medium text-gray-700 mb-1">
                    ประเภทคำถาม
                  </label>
                  <div className="relative">
                    <select
                      id="questionType"
                      name="type"
                      value={formData.questions[selectedQuestion].type}
                      onChange={(e) => handleQuestionInputChange(e, selectedQuestion)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-10"
                    >
                      <option value="text">ข้อความ</option>
                      <option value="image">รูปภาพ</option>
                      <option value="text_and_image">ข้อความและรูปภาพ</option>
                      <option value="video">วิดีโอ</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                </div>

                {/* เพิ่มส่วนกำหนดเวลาในวิดีโอ */}
                <div className="mb-4">
                  <label htmlFor="videoTimestamp" className="block text-sm font-medium text-gray-700 mb-1">
                    เวลาที่จะแสดงคำถามในวิดีโอ (วินาที)
                  </label>
                  <div className="flex items-center">
                    <input
                      type="number"
                      id="videoTimestamp"
                      name="videoTimestamp"
                      min="0"
                      value={formData.questions[selectedQuestion].videoTimestamp || 0}
                      onChange={(e) => handleVideoTimestampChange(e, selectedQuestion)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                      placeholder="กรอกเวลาที่จะแสดงคำถาม (วินาที)"
                    />
                    <div className="ml-2 text-sm text-gray-500">
                      {formData.questions[selectedQuestion].videoTimestamp !== undefined &&
                        formatTime(formData.questions[selectedQuestion].videoTimestamp)}
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">คำถามจะปรากฏเมื่อวิดีโอเล่นถึงเวลาที่กำหนด</p>
                </div>

                <div className="mb-4">
                  <label htmlFor="questionTitle" className="block text-sm font-medium text-gray-700 mb-1">
                    คำถาม
                  </label>
                  <input
                    type="text"
                    id="questionTitle"
                    name="title"
                    value={formData.questions[selectedQuestion].title}
                    onChange={(e) => handleQuestionInputChange(e, selectedQuestion)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                    placeholder="กรอกคำถาม"
                  />
                </div>

                {(formData.questions[selectedQuestion].type === "image" ||
                  formData.questions[selectedQuestion].type === "text_and_image") && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">รูปภาพประกอบคำถาม</label>
                      <input
                        type="file"
                        ref={questionImageRef}
                        onChange={(e) => handleQuestionImageUpload(e, selectedQuestion)}
                        className="hidden"
                        accept="image/*"
                      />
                      <div
                        ref={questionDropzoneRef}
                        onClick={triggerQuestionImageInput}
                        onDragOver={handleQuestionDragOver}
                        onDragLeave={handleQuestionDragLeave}
                        onDrop={(e) => handleQuestionDrop(e, selectedQuestion)}
                        className={`relative w-full h-40 bg-gray-100 rounded-lg border-2 ${isQuestionDraggingOver ? "border-[#008268] bg-[#E6F2F0]" : "border-dashed border-gray-300"
                          } flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50`}
                      >
                        {formData.questions[selectedQuestion].imageUrl ? (
                          <div className="relative w-full h-full">
                            <Image
                              src={formData.questions[selectedQuestion].imageUrl || "/placeholder.svg"}
                              alt="Question image"
                              fill
                              sizes="100%"
                              className="object-contain rounded-lg"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity rounded-lg">
                              <div className="bg-white p-2 rounded-full">
                                <Upload className="h-5 w-5 text-gray-700" />
                              </div>
                            </div>
                          </div>
                        ) : (
                          <>
                            <Upload className="h-12 w-12 text-gray-400 mb-2" />
                            <p className="text-sm text-gray-500">คลิกหรือลากไฟล์มาวางที่นี่เพื่ออัพโหลดรูปภาพ</p>
                            <p className="text-xs text-gray-400 mt-1">ขนาดแนะนำ 500x500 px</p>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                {(formData.questions[selectedQuestion].type === "text" ||
                  formData.questions[selectedQuestion].type === "text_and_image") && (
                    <div className="mb-4">
                      <label htmlFor="questionContent" className="block text-sm font-medium text-gray-700 mb-1">
                        รายละเอียดคำถาม
                      </label>
                      <textarea
                        id="questionContent"
                        name="content"
                        value={formData.questions[selectedQuestion].content}
                        onChange={(e) => handleQuestionInputChange(e, selectedQuestion)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                        placeholder="กรอกรายละเอียดคำถาม (ถ้ามี)"
                      />
                    </div>
                  )}
                <div className="mt-6 mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">ตัวเลือกคำตอบ</h3>

                  <div className="space-y-4">
                    {formData.questions[selectedQuestion].choices.map((choice, choiceIndex) => (
                      <div key={choice.id} className="border border-gray-200 rounded-md p-4">
                        <div className="flex items-center mb-3">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id={`choice-${choiceIndex}`}
                              name={`correct-answer-${selectedQuestion}-${choiceIndex}`}
                              checked={choice.isCorrect}
                              onChange={() => handleCorrectAnswerChange(selectedQuestion, choiceIndex)}
                              className="h-4 w-4 text-[#008268] focus:ring-[#008268] border-gray-300 rounded"
                            />
                            <label htmlFor={`choice-${choiceIndex}`} className="ml-2 text-sm font-medium text-gray-700">
                              คำตอบที่ถูกต้อง
                            </label>
                          </div>

                          <div className="ml-auto">
                            <div className="relative">
                              <select
                                value={choice.type}
                                onChange={(e) =>
                                  handleChoiceTypeChange(selectedQuestion, choiceIndex, e.target.value as ChoiceType)
                                }
                                className="text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] appearance-none bg-gray-50 pr-8 pl-2 py-1"
                              >
                                <option value="text">ข้อความ</option>
                                <option value="image">รูปภาพ</option>
                              </select>
                              <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <ChevronDown className="h-3 w-3 text-gray-500" />
                              </div>
                            </div>
                          </div>
                        </div>

                        {choice.type === "text" ? (
                          <input
                            type="text"
                            name="content"
                            value={choice.content}
                            onChange={(e) => handleChoiceInputChange(e, selectedQuestion, choiceIndex)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                            placeholder={`ตัวเลือกที่ ${choiceIndex + 1}`}
                          />
                        ) : (
                          <div>
                            <input
                              type="file"
                              ref={(el) => {
                                // แก้ไขวิธีการกำหนดค่า ref
                                if (choiceImageRefs.current.length > choiceIndex) {
                                  choiceImageRefs.current[choiceIndex] = el
                                }
                              }}
                              onChange={(e) => handleChoiceImageUpload(e, selectedQuestion, choiceIndex)}
                              className="hidden"
                              accept="image/*"
                            />
                            <div
                              ref={(el) => {
                                if (choiceDropzoneRefs.current.length > choiceIndex) {
                                  choiceDropzoneRefs.current[choiceIndex] = el
                                }
                              }}
                              onClick={() => triggerChoiceImageInput(choiceIndex)}
                              onDragOver={(e) => handleChoiceDragOver(e, choiceIndex)}
                              onDragLeave={handleChoiceDragLeave}
                              onDrop={(e) => handleChoiceDrop(e, selectedQuestion, choiceIndex)}
                              className={`relative w-full h-24 bg-gray-100 rounded-lg border-2 ${isDraggingOver === choiceIndex
                                ? "border-[#008268] bg-[#E6F2F0]"
                                : "border-dashed border-gray-300"
                                } flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50`}
                            >
                              {choice.imageUrl ? (
                                <div className="relative w-full h-full">
                                  <Image
                                    src={choice.imageUrl || "/placeholder.svg"}
                                    alt={`Choice ${choiceIndex + 1}`}
                                    fill
                                    sizes="100%"
                                    className="object-contain rounded-lg"
                                  />
                                  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity rounded-lg">
                                    <div className="bg-white p-2 rounded-full">
                                      <Upload className="h-4 w-4 text-gray-700" />
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                <>
                                  <Upload className="h-8 w-8 text-gray-400 mb-1" />
                                  <p className="text-xs text-gray-500">คลิกหรือลากไฟล์มาวางที่นี่</p>
                                  <p className="text-xs text-gray-400">ขนาดแนะนำ 500x500 px</p>
                                </>
                              )}
                            </div>
                            <input
                              type="text"
                              name="content"
                              value={choice.content}
                              onChange={(e) => handleChoiceInputChange(e, selectedQuestion, choiceIndex)}
                              className="w-full mt-2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#008268] bg-gray-50"
                              placeholder="คำอธิบายรูปภาพ (ถ้ามี)"
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex justify-between mt-6">
                  <button
                    onClick={() => confirmDeleteQuestion(selectedQuestion)}
                    className="flex items-center px-4 py-2 border border-red-300 text-red-600 rounded-md hover:bg-red-50"
                    disabled={formData.questions.length <= 1}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    <span>ลบคำถาม</span>
                  </button>
                  <button
                    onClick={() => saveQuestion(selectedQuestion)}
                    className="px-4 py-2 bg-[#008268] hover:bg-[#006e58] text-white rounded-md flex items-center gap-2"
                  >
                    <Save className="h-4 w-4" />
                    บันทึกคำถาม
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
