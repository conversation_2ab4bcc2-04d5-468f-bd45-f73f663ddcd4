"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function ProfileRedirect() {
  const router = useRouter()

  useEffect(() => {
    // ตรวจสอบว่ามีการ login หรือไม่
    const userId = localStorage.getItem("userId")
    if (!userId) {
      router.push("/login")
      return
    }

    // Redirect to edit profile page
    router.push("/profile/edit")
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#008268]"></div>
    </div>
  )
}
