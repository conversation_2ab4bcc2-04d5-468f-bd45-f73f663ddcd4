"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { ChevronRightIcon, PlayIcon, SparklesIcon } from "@heroicons/react/24/outline";
import { UserGroupIcon, ClockIcon, TrophyIcon, BookOpenIcon, ComputerDesktopIcon } from "@heroicons/react/24/solid";
import { Stethoscope, Brain, Shield, Zap } from "lucide-react";

const Hero = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeCard, setActiveCard] = useState(0);

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setActiveCard((prev) => (prev + 1) % 4);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const features = [
    {
      icon: ComputerDesktopIcon,
      title: "เรียนออนไลน์ได้ทุกที่ ทุกเวลา",
      description: "ระบบ LMS ที่ทันสมัย รองรับทุกอุปกรณ์",
      color: "from-[#008067] to-[#00a078]"
    },
    {
      icon: TrophyIcon,
      title: "แบบทดสอบท้ายบท พร้อมคำแนะนำ",
      description: "ระบบประเมินผลอัจฉริยะ ให้ข้อเสนอแนะทันที",
      color: "from-[#00a078] to-[#2CBCA0]"
    },
    {
      icon: Brain,
      title: "สุ่มคำถามระหว่างเรียน",
      description: "กระตุ้นการมีส่วนร่วม เพิ่มประสิทธิภาพการเรียนรู้",
      color: "from-[#008067] to-[#2CBCA0]"
    },
    {
      icon: Shield,
      title: "แดชบอร์ดรายบุคคลและกลุ่ม",
      description: "ติดตามความก้าวหน้า วางแผนพัฒนาบุคลากร",
      color: "from-[#2CBCA0] to-[#008067]"
    }
  ];



  return (
    <div
      className="relative min-h-screen overflow-hidden"
      style={{
        background: `
          linear-gradient(135deg, rgba(0, 128, 103, 0.03) 0%, rgba(255, 255, 255, 0.95) 25%, rgba(248, 255, 254, 0.9) 50%, rgba(0, 160, 120, 0.05) 100%),
          url('/img/SynphaetBuilding.jpg')
        `,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Bottom Irregular Heart Rhythm ECG Wave */}
        <div className="absolute bottom-1/4 left-0 w-full h-24 overflow-hidden z-10">
          <div className="relative w-full h-full">
            {/* Main irregular heart rhythm ECG - คลื่นหัวใจไม่เป็นรูปแบบ */}
            <svg
              className="absolute top-8 left-0 w-full h-16"
              viewBox="0 0 900 80"
              preserveAspectRatio="none"
              style={{
                animation: 'heartbeat-move 4.2s linear infinite'
              }}
            >
              <svg viewBox="0 0 900 80" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M0,40 
       L50,40 
       L55,38 L60,42 
       L65,20 L70,60 
       L75,38 L80,42 
       L90,40 L100,40 

       L110,15 L115,65 
       L120,22 L125,58 
       L130,30 L135,50 
       L140,40 L150,40

       L160,10 L170,70 
       L180,40 L190,30 L200,50 L210,35 L220,45 

       L230,10 L240,75 
       L250,40 L260,35 L270,45 L280,40 L290,40 

       L300,20 L310,60 L320,40 

       L330,5 L340,75 L350,40 

       L360,30 L370,50 L380,38 L390,42 

       L400,40 L450,40 
       L460,15 L470,65 L480,40

       L490,40 L500,30 L510,50 L520,40

       L530,5 L540,75 L550,40

       L560,25 L570,55 L580,35 L590,45 L600,40

       L650,40 
       L660,38 L665,42 L670,40 
       L680,20 L690,60 
       L700,40 

       L710,10 L720,70 L730,40 
       L740,28 L750,52 L760,40

       L770,5 L780,75 L790,40 

       L800,40 L850,40 L900,40"
                  stroke="#dc2626"
                  strokeWidth="3"
                  fill="none"
                  vectorEffect="non-scaling-stroke"
                  filter="drop-shadow(0 0 3px #dc2626)"
                  opacity="1"
                />
              </svg>

            </svg>
            {/* Subtle baseline - normal flat line */}
            <svg
              className="absolute top-8 left-0 w-full h-16"
              viewBox="0 0 900 80"
              preserveAspectRatio="none"
              style={{
                animation: 'heartbeat-move 4.2s linear infinite',
                animationDelay: '3.1s'
              }}
            >
              <path
                d="M0,40 L900,40"
                stroke="#ff6b6b"
                strokeWidth="1"
                fill="none"
                vectorEffect="non-scaling-stroke"
                opacity="0.3"
              />
            </svg>
          </div>
        </div>

        {/* Floating Lines */}
        <div className="absolute top-1/4 left-0 w-32 h-px bg-gradient-to-r from-transparent via-[#008067]/30 to-transparent animate-pulse"></div>
        <div className="absolute bottom-1/3 right-0 w-24 h-px bg-gradient-to-l from-transparent via-[#00a078]/30 to-transparent animate-pulse delay-500"></div>

        {/* Morphing Blobs */}
        <div className="absolute top-16 right-16 w-64 h-64 bg-gradient-to-br from-[#008067]/5 to-[#00a078]/10 rounded-full blur-3xl animate-blob"></div>
        <div className="absolute bottom-20 left-20 w-48 h-48 bg-gradient-to-tr from-[#2CBCA0]/5 to-[#008067]/10 rounded-full blur-2xl animate-blob animation-delay-2000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-r from-[#00a078]/8 to-[#2CBCA0]/8 rounded-full blur-xl animate-blob animation-delay-4000"></div>
      </div>

      {/* Content Container */}
      <div className="relative z-10">
        {/* Top Navigation Bar */}
        <div className="absolute top-0 left-0 right-0 bg-white/80 backdrop-blur-md border-b border-[#008067]/10 z-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r  rounded-lg flex items-center justify-center p-1">
                  <Image
                    src="/e-med/img/logo-3.png"
                    alt="CareAcademy Logo"
                    width={32}
                    height={32}
                    className="w-full h-full object-contain"
                  />
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-[#008067] to-[#283db2] bg-clip-text text-transparent">
                  Synphaet Hospital
                </span>
              </div>
              <div className="hidden md:flex items-center space-x-6">
                <span className="text-sm text-gray-600">โรงพยาบาลสินแพทย์ × มหาวิทยาลัยกรุงเทพ</span>
                <div className="bg-gradient-to-br from-gray-700 to-gray-900 rounded-lg p-1">
                  <Image
                    src="/e-med/img/logo-4.png"
                    alt="Partner Logo"
                    width={24}
                    height={24}
                    className="w-6 h-6 object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Hero Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20">
          <div className="text-center mb-16">
            {/* Hero Badge */}
            <div className={`inline-flex items-center px-8 py-4 bg-white/60 backdrop-blur-md rounded-full border border-[#008067]/20 shadow-2xl mb-8 transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
              <SparklesIcon className="w-6 h-6 text-[#008067] mr-3" />
              <span className="text-[#008067] font-semibold text-lg">
                อนาคตของการศึกษาการพยาบาล เริ่มต้นที่นี่
              </span>
            </div>

            {/* Main Heading with Typing Effect */}
            <h1 className={`text-5xl lg:text-7xl xl:text-8xl font-black leading-tight mb-8 transform transition-all duration-1000 delay-200 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
              <span className="block text-gray-900 mb-4">
                การศึกษา
                <span className="relative inline-block mx-4">
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#008067] via-[#00a078] to-[#2CBCA0]">
                    อัจฉริยะ
                  </span>
                  <div className="absolute -inset-1 bg-gradient-to-r from-[#008067]/20 to-[#00a078]/20 blur-lg rounded-lg"></div>
                </span>
              </span>
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-[#008067] via-[#00a078] to-[#2CBCA0] leading-tight">
                สำหรับบุคลากรการพยาบาล
              </span>
            </h1>

            {/* Subheading */}
            <p className={`text-xl lg:text-2xl xl:text-3xl text-gray-700 mb-12 max-w-5xl mx-auto leading-relaxed transform transition-all duration-1000 delay-400 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
              ยกระดับการพัฒนาบุคลากรทางการแพทย์ ด้วยแพลตฟอร์มเรียนรู้ออนไลน์ที่มีมาตรฐานเดียวกันทั้งองค์กร
              รองรับการเรียนรู้ได้ทุกที่ ทุกเวลา พร้อมระบบวิเคราะห์ผล และเทคโนโลยีสมัยใหม่สำหรับการอบรมอย่างมีประสิทธิภาพ
              <span className="text-[#008067] font-semibold"> เพื่อสร้างบุคลากรทางการแพทย์ที่เป็นเลิศ</span>
            </p>

            {/* CTA Buttons */}
            <div className={`flex flex-col lg:flex-row items-center justify-center gap-6 mb-16 transform transition-all duration-1000 delay-600 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
              <Link href="/login" className="group">
                <button className="relative px-12 py-6 bg-gradient-to-r from-[#008067] to-[#00a078] text-white font-bold text-xl rounded-2xl overflow-hidden transition-all duration-500 hover:scale-105 hover:shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-r from-[#007055] to-[#008067] translate-x-full group-hover:translate-x-0 transition-transform duration-500"></div>
                  <div className="relative flex items-center space-x-3">
                    <span>เริ่มเรียนรู้ทันที</span>
                    <ChevronRightIcon className="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300" />
                  </div>
                </button>
              </Link>

              <button className="group flex items-center space-x-3 px-8 py-6 bg-white/80 backdrop-blur-md border-2 border-[#008067]/30 text-[#008067] font-semibold text-xl rounded-2xl hover:bg-[#008067] hover:text-white transition-all duration-500 hover:scale-105 hover:shadow-xl">
                <PlayIcon className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                <span>ดูตัวอย่างการเรียน</span>
              </button>
            </div>
          </div>

          {/* Features Grid */}
          <div className="relative grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-8 mb-16">
            {/* Background Heartbeat Animation for Features */}
            <div className="absolute top-1/2 left-0 w-full h-6 overflow-hidden z-0 opacity-30">
              <div className="relative w-full h-full">
                <svg
                  className="absolute top-2 left-0 w-full h-2"
                  viewBox="0 0 200 10"
                  preserveAspectRatio="none"
                  style={{
                    animation: 'heartbeat-move 6s linear infinite'
                  }}
                >
                  <path
                    d="M0,5 L35,5 L37,5 L39,1 L41,9 L43,1 L45,5 L47,5 L49,1 L51,9 L53,5 L55,5 L200,5"
                    stroke="#dc2626"
                    strokeWidth="1.5"
                    fill="none"
                    vectorEffect="non-scaling-stroke"
                    opacity="0.4"
                  />
                </svg>
              </div>
            </div>

            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              const isActive = activeCard === index;

              return (
                <div
                  key={index}
                  className={`group relative p-8 bg-white/60 backdrop-blur-md rounded-3xl border border-white/40 shadow-xl hover:shadow-2xl transition-all duration-700 transform hover:scale-105 hover:-translate-y-2 z-10 ${isActive ? 'ring-2 ring-[#008067]/50 bg-white/80' : ''
                    } ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
                  style={{ transitionDelay: `${800 + index * 200}ms` }}
                >
                  {/* Background Gradient */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-5 rounded-3xl transition-opacity duration-500 group-hover:opacity-10`}></div>

                  {/* Icon */}
                  <div className={`relative w-16 h-16 mb-6 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-500`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#008067] transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Hover Effect */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#008067] to-[#00a078] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 rounded-b-3xl"></div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;