"use client"

import React, { useCallback, useRef } from 'react'
import { useE<PERSON><PERSON>, EditorContent } from '@tiptap/react'

import StarterKit from '@tiptap/starter-kit'
import Heading from '@tiptap/extension-heading'
import BulletList from '@tiptap/extension-bullet-list'
import OrderedList from '@tiptap/extension-ordered-list'
import ListItem from '@tiptap/extension-list-item'
import Blockquote from '@tiptap/extension-blockquote'
import TextAlign from '@tiptap/extension-text-align'
import Link from '@tiptap/extension-link'
import Image from '@tiptap/extension-image'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import Highlight from '@tiptap/extension-highlight'
import Underline from '@tiptap/extension-underline'
import Placeholder from '@tiptap/extension-placeholder'
import History from '@tiptap/extension-history'
import ResizeImage from 'tiptap-extension-resize-image'
import {
    Bold,
    Italic,
    Underline as UnderlineIcon,
    Strikethrough,
    List,
    ListOrdered,
    Quote,
    Link as LinkIcon,
    ImageIcon,
    Table as TableIcon,
    Highlighter,
    AlignLeft,
    AlignCenter,
    AlignRight,
    Undo,
    Redo,
    Type,
    Minus
} from 'lucide-react'

interface TiptapEditorProps {
    content: string
    onChange: (content: string) => void
    placeholder?: string
}

const TiptapEditor: React.FC<TiptapEditorProps> = ({
    content,
    onChange,
    placeholder = "เริ่มเขียนเนื้อหา..."
}) => {
    const fileInputRef = useRef<HTMLInputElement>(null)
    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                history: false, // We'll use the History extension instead
                heading: false, // We'll configure heading separately
                bulletList: false, // We'll configure lists separately
                orderedList: false, // We'll configure lists separately
                listItem: false, // We'll configure lists separately
                blockquote: false, // We'll configure blockquote separately
            }),
            Heading.configure({
                levels: [1, 2, 3, 4, 5, 6],
                HTMLAttributes: {
                    class: 'tiptap-editor',
                },
                // Remove HTMLAttributes to allow default tag rendering
            }),
            BulletList.configure({
                HTMLAttributes: {
                    class: 'tiptap-bullet-list',
                },
            }),
            OrderedList.configure({
                HTMLAttributes: {
                    class: 'tiptap-ordered-list',
                },
            }),
            ListItem.configure({
                HTMLAttributes: {
                    class: 'tiptap-list-item',
                },
            }),
            Blockquote.configure({
                HTMLAttributes: {
                    class: 'tiptap-blockquote',
                },
            }),
            History.configure({
                depth: 50,
            }),
            TextAlign.configure({
                types: ['heading', 'paragraph'],
            }),
            Link.configure({
                openOnClick: false,
                linkOnPaste: true,
                HTMLAttributes: {
                    class: 'text-blue-600 underline hover:text-blue-800 cursor-pointer',
                    target: '_blank',
                    rel: 'noopener noreferrer',
                },
            }),
            Image.configure({
                HTMLAttributes: {
                    class: 'rounded-lg max-w-full h-auto',
                },
            }),
            ResizeImage,
            Table.configure({
                resizable: true,
                HTMLAttributes: {
                    class: 'border-collapse border border-gray-300 w-full',
                },
            }),
            TableRow.configure({
                HTMLAttributes: {
                    class: 'border border-gray-300',
                },
            }),
            TableHeader.configure({
                HTMLAttributes: {
                    class: 'border border-gray-300 bg-gray-100 font-bold p-2 text-left',
                },
            }),
            TableCell.configure({
                HTMLAttributes: {
                    class: 'border border-gray-300 p-2',
                },
            }),
            Highlight.configure({
                multicolor: true,
                HTMLAttributes: {
                    class: 'bg-yellow-200',
                },
            }),
            Underline,
            Placeholder.configure({
                placeholder,
                emptyEditorClass: 'is-editor-empty',
            }),
        ],
        content,
        onUpdate: ({ editor }) => {
            onChange(editor.getHTML())
        },
        editorProps: {
            attributes: {
                class: 'focus:outline-none min-h-[200px] p-4 tiptap-editor',
                spellcheck: 'false',
            },
            handleKeyDown: (view, event) => {
                // Add keyboard shortcuts
                if (event.ctrlKey || event.metaKey) {
                    switch (event.key) {
                        case 'b':
                            event.preventDefault()
                            editor?.chain().focus().toggleBold().run()
                            return true
                        case 'i':
                            event.preventDefault()
                            editor?.chain().focus().toggleItalic().run()
                            return true
                        case 'u':
                            event.preventDefault()
                            editor?.chain().focus().toggleUnderline().run()
                            return true
                        case 'k':
                            event.preventDefault()
                            addLink()
                            return true
                    }
                }
                return false
            },
        },
    })

    const addImage = useCallback(() => {
        fileInputRef.current?.click()
    }, [])

    const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (file && editor) {
            // Check file size (limit to 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('ไฟล์รูปภาพต้องมีขนาดไม่เกิน 5MB')
                return
            }

            // Check file type
            if (!file.type.startsWith('image/')) {
                alert('กรุณาเลือกไฟล์รูปภาพเท่านั้น')
                return
            }

            // Create a URL for the file to display immediately
            const url = URL.createObjectURL(file)
            editor.chain().focus().setImage({ src: url }).run()

            // TODO: Upload to server and replace URL
            // You can implement server upload here and replace the blob URL
            // with the actual server URL after successful upload
        }
        // Reset the input value so the same file can be selected again
        event.target.value = ''
    }, [editor])

    const addLink = useCallback(() => {
        const previousUrl = editor?.getAttributes('link').href
        const url = window.prompt('กรุณาใส่ URL (เช่น https://example.com):', previousUrl || 'https://')

        if (url === null) {
            return
        }

        if (url === '' || url === 'https://') {
            editor?.chain().focus().extendMarkRange('link').unsetLink().run()
            return
        }

        // Ensure URL has protocol
        let finalUrl = url
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            finalUrl = 'https://' + url
        }

        editor?.chain().focus().extendMarkRange('link').setLink({
            href: finalUrl,
            target: '_blank',
            rel: 'noopener noreferrer'
        }).run()
    }, [editor])

    const addTable = useCallback(() => {
        editor?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
    }, [editor])

    if (!editor) {
        return <div>กำลังโหลด...</div>
    }

    return (
        <div className="border border-gray-300 rounded-md overflow-hidden">
            {/* Hidden file input for image upload */}
            <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                style={{ display: 'none' }}
            />

            {/* Toolbar */}
            <div className="border-b border-gray-200 bg-gray-50 p-2">
                <div className="flex flex-wrap items-center gap-1">
                    {/* Text Formatting */}
                    <div className="flex items-center gap-1 border-r border-gray-300 pr-2 mr-2">
                        <button
                            onClick={() => editor.chain().focus().toggleBold().run()}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('bold') ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="ตัวหนา (Ctrl+B)"
                        >
                            <Bold size={16} />
                        </button>
                        <button
                            onClick={() => editor.chain().focus().toggleItalic().run()}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('italic') ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="ตัวเอียง (Ctrl+I)"
                        >
                            <Italic size={16} />
                        </button>
                        <button
                            onClick={() => editor.chain().focus().toggleUnderline().run()}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('underline') ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="ขีดเส้นใต้ (Ctrl+U)"
                        >
                            <UnderlineIcon size={16} />
                        </button>
                        <button
                            onClick={() => editor.chain().focus().toggleStrike().run()}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('strike') ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="ขีดฆ่า (Ctrl+Shift+X)"
                        >
                            <Strikethrough size={16} />
                        </button>
                        <button
                            onClick={() => editor.chain().focus().toggleHighlight().run()}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('highlight') ? 'bg-yellow-200 text-yellow-800' : ''
                                }`}
                            title="เน้นข้อความด้วยสีเหลือง"
                        >
                            <Highlighter size={16} />
                        </button>
                    </div>

                    {/* Headings */}
                    <div className="flex items-center gap-1 border-r border-gray-300 pr-2 mr-2">
                        <button
                            onClick={() => {
                                console.log('H1 clicked, editor:', editor)
                                editor.chain().focus().toggleHeading({ level: 1 }).run()
                            }}
                            className={`px-2 py-1 rounded hover:bg-gray-200 text-sm font-bold transition-colors ${editor.isActive('heading', { level: 1 }) ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="หัวข้อใหญ่ (H1)"
                        >
                            H1
                        </button>
                        <button
                            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                            className={`px-2 py-1 rounded hover:bg-gray-200 text-sm font-bold ${editor.isActive('heading', { level: 2 }) ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="หัวข้อกลาง (H2)"
                        >
                            H2
                        </button>
                        <button
                            onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                            className={`px-2 py-1 rounded hover:bg-gray-200 text-sm font-bold ${editor.isActive('heading', { level: 3 }) ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="หัวข้อเล็ก (H3)"
                        >
                            H3
                        </button>
                        <button
                            onClick={() => editor.chain().focus().toggleHeading({ level: 4 }).run()}
                            className={`px-2 py-1 rounded hover:bg-gray-200 text-sm font-bold ${editor.isActive('heading', { level: 4 }) ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="หัวข้อย่อย (H4)"
                        >
                            H4
                        </button>
                        <button
                            onClick={() => editor.chain().focus().setParagraph().run()}
                            className={`px-2 py-1 rounded hover:bg-gray-200 text-sm ${editor.isActive('paragraph') ? 'bg-gray-300' : ''
                                }`}
                            title="ข้อความธรรมดา"
                        >
                            P
                        </button>
                    </div>

                    {/* Text Alignment */}
                    <div className="flex items-center gap-1 border-r border-gray-300 pr-2 mr-2">
                        <button
                            onClick={() => editor.chain().focus().setTextAlign('left').run()}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive({ textAlign: 'left' }) ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="จัดชิดซ้าย (Ctrl+Shift+L)"
                        >
                            <AlignLeft size={16} />
                        </button>
                        <button
                            onClick={() => editor.chain().focus().setTextAlign('center').run()}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive({ textAlign: 'center' }) ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="จัดกึ่งกลาง (Ctrl+Shift+E)"
                        >
                            <AlignCenter size={16} />
                        </button>
                        <button
                            onClick={() => editor.chain().focus().setTextAlign('right').run()}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive({ textAlign: 'right' }) ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="จัดชิดขวา (Ctrl+Shift+R)"
                        >
                            <AlignRight size={16} />
                        </button>
                    </div>

                    {/* Lists */}
                    <div className="flex items-center gap-1 border-r border-gray-300 pr-2 mr-2">
                        <button
                            onClick={() => {
                                console.log('Bullet list clicked, editor:', editor)
                                editor.chain().focus().toggleBulletList().run()
                            }}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('bulletList') ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="รายการแบบจุด (Ctrl+Shift+8)"
                        >
                            <List size={16} />
                        </button>
                        <button
                            onClick={() => {
                                console.log('Ordered list clicked, editor:', editor)
                                editor.chain().focus().toggleOrderedList().run()
                            }}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('orderedList') ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="รายการแบบตัวเลข (Ctrl+Shift+7)"
                        >
                            <ListOrdered size={16} />
                        </button>
                        <button
                            onClick={() => {
                                console.log('Blockquote clicked, editor:', editor)
                                editor.chain().focus().toggleBlockquote().run()
                            }}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('blockquote') ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="คำพูด/อ้างอิง (Ctrl+Shift+B)"
                        >
                            <Quote size={16} />
                        </button>
                    </div>

                    {/* Media and Links */}
                    <div className="flex items-center gap-1 border-r border-gray-300 pr-2 mr-2">
                        {/* <button
                            onClick={addLink}
                            className={`p-2 rounded hover:bg-gray-200 transition-colors ${editor.isActive('link') ? 'bg-blue-200 text-blue-800' : ''
                                }`}
                            title="เพิ่มลิงก์ (Ctrl+K)"
                        >
                            <LinkIcon size={16} />
                        </button> */}
                        <button
                            onClick={addImage}
                            className="p-2 rounded hover:bg-green-100 transition-colors"
                            title="เพิ่มรูปภาพจากไฟล์ (คลิกเพื่อเลือกไฟล์)"
                        >
                            <ImageIcon size={16} />
                        </button>
                        <button
                            onClick={addTable}
                            className="p-2 rounded hover:bg-gray-200 transition-colors"
                            title="เพิ่มตาราง (3x3)"
                        >
                            <TableIcon size={16} />
                        </button>
                    </div>

                    {/* Utilities */}
                    <div className="flex items-center gap-1 border-r border-gray-300 pr-2 mr-2">
                        <button
                            onClick={() => editor.chain().focus().setHorizontalRule().run()}
                            className="p-2 rounded hover:bg-gray-200"
                            title="เส้นคั่น"
                        >
                            <Minus size={16} />
                        </button>
                    </div>

                    {/* History */}
                    <div className="flex items-center gap-1">
                        <button
                            onClick={() => editor.chain().focus().undo().run()}
                            disabled={!editor.can().chain().focus().undo().run()}
                            className="p-2 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            title="ยกเลิก (Ctrl+Z)"
                        >
                            <Undo size={16} />
                        </button>
                        <button
                            onClick={() => editor.chain().focus().redo().run()}
                            disabled={!editor.can().chain().focus().redo().run()}
                            className="p-2 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            title="ทำซ้ำ (Ctrl+Y)"
                        >
                            <Redo size={16} />
                        </button>
                    </div>
                </div>
            </div>

            {/* Editor Content */}
            <div className="bg-white">
                <EditorContent
                    editor={editor}
                    className="tiptap-editor"
                />
            </div>
        </div>
    )
}

export default TiptapEditor
