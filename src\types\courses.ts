import { LearningStatus } from './progress';

export type ContentType = "video" | "pdf" | "text" | "quiz" | "final-exam"

// โครงสร้างข้อมูลเนื้อหา
export interface Content {
  id: string
  name: string
  typecontent: string
  details: string
  time: number
  duration?: string // เพิ่ม duration string จาก API
  is_locked?: boolean // เพิ่ม is_locked property จาก API
}

// โครงสร้างข้อมูลบทเรียน
export interface Lesson {
  id: string
  name: string
  description: string
  time: number
  duration?: string // เพิ่ม duration string จาก API
  content: Content[]
  is_locked?: boolean // เพิ่ม is_locked property จาก API
}

// โครงสร้างข้อมูลผู้สอน
export interface Teacher {
  name: string
  description: string
  avatar?: string
}

// โครงสร้างข้อมูลคอร์ส
export interface CourseType {
  difficulty: string;
  title: any;
  flexibility: any;
  id: string
  name: string
  instruction: string
  description: string
  level: string
  time: number
  status: string
  certify: boolean
  coverImage?: string
  LearningStatus?: LearningStatus
  progress?: number
  teacher: Teacher
  lesson: Lesson[]
  summary?:{
    lesson_count: number
    exam_count: number
    video_count: number
  }
}
