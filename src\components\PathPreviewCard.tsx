"use client"

import type React from "react"
import { useEffect, useState } from "react"
import Link from "next/link"
import { Clock, Route } from "lucide-react"
import profileService, { UserPathway } from "@/hook/profileService"

interface PathPreviewCardProps {
  userId?: string
  maxItems?: number
}

const PathPreviewCard: React.FC<PathPreviewCardProps> = ({ userId, maxItems = 3 }) => {
  const [userPathways, setUserPathways] = useState<UserPathway[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadUserPathways = async () => {
      setLoading(true)
      try {
        // Fetch from backend API
        const response = await profileService.getUserPathwaysDashboard()
        let pathways: UserPathway[] = []
        if (response && Array.isArray(response.data)) {
          pathways = response.data
        }
        setUserPathways(pathways.slice(0, maxItems))
      } catch (error) {
        console.error("Error loading pathways from API:", error)
        setUserPathways([])
      } finally {
        setLoading(false)
      }
    }
    loadUserPathways()
  }, [userId, maxItems])

  const getProgressColor = (percentage: number) => {
    if (percentage === 0) return "bg-gray-300"
    if (percentage <= 25) return "bg-red-500"
    if (percentage <= 50) return "bg-orange-500"
    if (percentage <= 75) return "bg-yellow-500"
    return "bg-green-500"
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg p-4 shadow-sm border animate-pulse">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded mb-2 w-full"></div>
                <div className="flex gap-2 mb-2">
                  <div className="h-5 bg-gray-200 rounded w-16"></div>
                  <div className="h-5 bg-gray-200 rounded w-12"></div>
                </div>
              </div>
              <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
            </div>
            <div className="mb-3">
              <div className="h-2 bg-gray-200 rounded mb-1"></div>
              <div className="h-1.5 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (!userPathways || userPathways.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="mb-4">
          <Route size={48} className="mx-auto text-gray-300" />
        </div>
        <h3 className="text-lg font-semibold text-gray-600 mb-2">ยังไม่มีเส้นทางการเรียน</h3>
        <p className="text-gray-500 mb-4">กรุณาติดต่อผู้ดูแลระบบเพื่อขอรับมอบหมายเส้นทางการเรียน</p>
        <Link
          href="/courses"
          className="inline-flex items-center px-4 py-2 bg-[#008268] text-white rounded-lg hover:bg-[#006e58] transition-colors"
        >
          ดูคอร์สทั้งหมด
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {userPathways.map((pathway) => {
        // Calculate progress and course stats
        const totalCourses = pathway.path_amount || (pathway.courses ? pathway.courses.length : 0)
        const completedCourses = pathway.path_lessoned || (pathway.courses ? pathway.courses.filter(c => c.lesson_progress === 100).length : 0)
        const progressPercentage = pathway.path_processing || (totalCourses > 0 ? Math.round((completedCourses / totalCourses) * 100) : 0)
        const estimatedDuration = pathway.path_duration || (pathway.courses ? pathway.courses.reduce((sum, c) => sum + (c.lesson_duration || 0), 0) : 0)
        return (
          <div
            key={pathway.path_slug}
            className="bg-white rounded-lg p-4 shadow-sm border hover:shadow-md transition-shadow duration-200"
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 text-sm mb-1">{pathway.path_name}</h3>
                <p className="text-xs text-gray-600 mb-2 line-clamp-2">{pathway.path_description}</p>
                <div className="flex items-center gap-2">
                  <span className="text-xs px-2 py-0.5 rounded-full bg-blue-100 text-blue-800">
                    {totalCourses} คอร์ส
                  </span>
                  <span className="text-xs text-gray-500 flex items-center">
                    <Clock size={10} className="mr-1" />
                    {estimatedDuration} ชม.
                  </span>
                </div>
              </div>
              <div className="ml-3">
                <div className="w-10 h-10 bg-gray-400 rounded-lg flex items-center justify-center">
                  <Route size={20} className="text-white" />
                </div>
              </div>
            </div>

            {/* Progress */}
            <div className="mb-3">
              <div className="flex justify-between text-xs text-gray-600 mb-1">
                <span>ความก้าวหน้า</span>
                <span className="text-gray-500">
                  {completedCourses} จาก {totalCourses} คอร์ส
                </span>
              </div>
              <div className="w-full h-1.5 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className={`h-full rounded-full transition-all duration-300 ${getProgressColor(progressPercentage)}`}
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
              <div className="text-right text-xs text-gray-500 mt-1">{progressPercentage}%</div>
            </div>
          </div>
        )
      })}

      {/* View All Link */}
      {userPathways.length > 0 && (
        <div className="text-center pt-2">
          <Link href="/profile/certificate-pathways" className="text-sm text-[#008268] hover:text-[#006e58] font-medium">
            ดูเส้นทางการเรียนทั้งหมด →
          </Link>
        </div>
      )}
    </div>
  )
}

export default PathPreviewCard
