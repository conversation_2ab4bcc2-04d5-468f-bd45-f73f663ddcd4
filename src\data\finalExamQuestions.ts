import { getCoursesData } from "./allCourses"

// Interface สำหรับตัวเลือกคำตอบ
export interface Choice {
  id: string
  content: string
  type?: string
  isCorrect: boolean
  imageUrl?: string
}

// Interface สำหรับคำถาม
export interface Question {
  id: string
  title: string
  type?: string
  content: string
  imageUrl?: string
  choices: Choice[]
}

// Interface สำหรับข้อมูลข้อสอบท้ายบท
export interface FinalExamData {
  id: string
  name: string
  description?: string
  courseId: string
  questions: Question[]
  passingScore: number
  timeLimit: number
}

// แก้ไขข้อมูล finalExamData โดยแทนที่ข้อมูลเดิมของ exam-001 และเพิ่ม exam-003
const finalExamData: FinalExamData[] = [
  {
    id: "e1",
    courseId: "WnH5F0qZF938", // คอร์สการเตรียมอุปกรณ์และการให้เลือด
    name: "แบบทดสอบก่อนเรียน - การปฐมพยาบาลเบื้องต้น",
    description: "ข้อสอบนี้ทดสอบความรู้เกี่ยวกับการปฐมพยาบาลเบื้องต้น",
    passingScore: 70,
    timeLimit: 45, // หน่วยเป็นนาที
    questions: [
      {
        id: "p1",
        title: "โรคปอดอักเสบคืออะไร",
        type: "text",
        content: "เลือกคำอธิบายที่ถูกต้องเกี่ยวกับโรคปอดอักเสบ",
        choices: [
          { id: "a", content: "ภาวะปอดแฟบเฉียบพลัน", type: "text", isCorrect: false },
          { id: "b", content: "ภาวะการติดเชื้อในถุงลมปอด", type: "text", isCorrect: true },
          { id: "c", content: "โรคหัวใจวายเรื้อรัง", type: "text", isCorrect: false },
          { id: "d", content: "การหายใจผิดปกติชั่วคราว", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p2",
        title: "สาเหตุหลักของโรคปอดอักเสบเกิดจากอะไรได้บ้าง (เลือกทุกข้อที่ถูกต้อง)",
        type: "text",
        content: "เลือกทุกข้อที่เป็นสาเหตุของโรคปอดอักเสบ",
        choices: [
          { id: "a", content: "เชื้อแบคทีเรีย", type: "text", isCorrect: true },
          { id: "b", content: "เชื้อไวรัส", type: "text", isCorrect: true },
          { id: "c", content: "เชื้อรา", type: "text", isCorrect: true },
          { id: "d", content: "ลมหายใจไม่สดชื่น", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p3",
        title: "อาการใดเป็นอาการสำคัญของโรคปอดอักเสบ",
        type: "text",
        content: "เลือกอาการที่พบบ่อยในผู้ป่วยโรคปอดอักเสบ",
        choices: [
          { id: "a", content: "ไอ มีเสมหะ เจ็บหน้าอก หายใจลำบาก", type: "text", isCorrect: true },
          { id: "b", content: "ท้องเสียเฉียบพลัน", type: "text", isCorrect: false },
          { id: "c", content: "เวียนศีรษะ หน้ามืด", type: "text", isCorrect: false },
          { id: "d", content: "ตาบวมแดง", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p4",
        title: "วิธีการแพร่กระจายของเชื้อโรคที่ทำให้เกิดปอดอักเสบคืออะไร",
        type: "text",
        content: "เลือกคำตอบที่ถูกต้อง",
        choices: [
          { id: "a", content: "การหายใจเอาเชื้อในอากาศเข้าไป", type: "text", isCorrect: true },
          { id: "b", content: "การสัมผัสแสงแดดมากเกินไป", type: "text", isCorrect: false },
          { id: "c", content: "การกินอาหารมันเยอะ", type: "text", isCorrect: false },
          { id: "d", content: "การนอนหลับน้อย", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p5",
        title: "ผู้ที่เสี่ยงต่อโรคปอดอักเสบมากที่สุดคือกลุ่มใด",
        type: "text",
        content: "เลือกกลุ่มที่มีความเสี่ยงสูง",
        choices: [
          { id: "a", content: "คนวัยทำงานทั่วไป", type: "text", isCorrect: false },
          { id: "b", content: "นักกีฬา", type: "text", isCorrect: false },
          { id: "c", content: "ผู้สูงอายุและเด็กเล็ก", type: "text", isCorrect: true },
          { id: "d", content: "ผู้ที่ชอบออกกำลังกายกลางแจ้ง", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p6",
        title: "การวินิจฉัยโรคปอดอักเสบมักใช้วิธีใด",
        type: "text",
        content: "เลือกคำตอบที่ถูกต้อง",
        choices: [
          { id: "a", content: "ตรวจเลือด", type: "text", isCorrect: false },
          { id: "b", content: "X-ray ปอด", type: "text", isCorrect: true },
          { id: "c", content: "ตรวจสายตา", type: "text", isCorrect: false },
          { id: "d", content: "ตรวจคลื่นหัวใจ", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p7",
        title: "โรคปอดอักเสบที่เกิดจากเชื้อไวรัสมักรักษาอย่างไร",
        type: "text",
        content: "เลือกวิธีรักษาที่เหมาะสม",
        choices: [
          { id: "a", content: "ให้ยาปฏิชีวนะทันที", type: "text", isCorrect: false },
          { id: "b", content: "ดูแลตามอาการและพักผ่อน", type: "text", isCorrect: true },
          { id: "c", content: "ให้ยาสเตียรอยด์แรงๆ", type: "text", isCorrect: false },
          { id: "d", content: "ล้างปอดด้วยน้ำเกลือ", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p8",
        title: "อาการแทรกซ้อนที่อาจเกิดจากโรคปอดอักเสบคืออะไร",
        type: "text",
        content: "เลือกคำตอบที่ถูกต้อง",
        choices: [
          { id: "a", content: "หัวใจโต", type: "text", isCorrect: false },
          { id: "b", content: "ปอดบวมรุนแรงถึงขั้นเสียชีวิต", type: "text", isCorrect: true },
          { id: "c", content: "ข้ออักเสบ", type: "text", isCorrect: false },
          { id: "d", content: "เส้นเลือดขอด", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p9",
        title: "การป้องกันโรคปอดอักเสบสามารถทำได้อย่างไร (เลือกทุกข้อที่ถูกต้อง)",
        type: "text",
        content: "เลือกทุกข้อที่ช่วยลดความเสี่ยงในการเกิดโรคปอดอักเสบ",
        choices: [
          { id: "a", content: "ฉีดวัคซีนป้องกัน", type: "text", isCorrect: true },
          { id: "b", content: "ล้างมือบ่อยๆ", type: "text", isCorrect: true },
          { id: "c", content: "หลีกเลี่ยงการสูบบุหรี่", type: "text", isCorrect: true },
          { id: "d", content: "กินอาหารรสจัดเป็นประจำ", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p10",
        title: "ควรทำอย่างไรหากสงสัยว่าตนเองเป็นโรคปอดอักเสบ",
        type: "text",
        content: "เลือกแนวทางที่ถูกต้อง",
        choices: [
          { id: "a", content: "ซื้อยากินเอง", type: "text", isCorrect: false },
          { id: "b", content: "รอให้อาการหายเอง", type: "text", isCorrect: false },
          { id: "c", content: "รีบพบแพทย์เพื่อรับการวินิจฉัย", type: "text", isCorrect: true },
          { id: "d", content: "ดื่มน้ำเย็นมากๆ", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p11",
        title: "ข้อใดไม่ใช่วิธีการปฐมพยาบาลผู้ป่วยที่หายใจลำบาก",
        type: "text",
        content: "เลือกคำตอบที่ไม่ถูกต้อง",
        choices: [
          { id: "a", content: "ให้นั่งตัวตรง", type: "text", isCorrect: false },
          { id: "b", content: "คลายเสื้อผ้า", type: "text", isCorrect: false },
          { id: "c", content: "ให้นอนราบ", type: "text", isCorrect: true },
          { id: "d", content: "ให้ความมั่นใจ", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p12",
        title: "อุณหภูมิกายปกติของคนเราอยู่ที่กี่องศาเซลเซียส",
        type: "text",
        content: "เลือกคำตอบที่ถูกต้อง",
        choices: [
          { id: "a", content: "35-36 องศาเซลเซียส", type: "text", isCorrect: false },
          { id: "b", content: "36.5-37.5 องศาเซลเซียส", type: "text", isCorrect: true },
          { id: "c", content: "38-39 องศาเซลเซียส", type: "text", isCorrect: false },
          { id: "d", content: "39-40 องศาเซลเซียส", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p13",
        title: "การล้างมืออย่างถูกต้องควรใช้เวลานานเท่าใด",
        type: "text",
        content: "เลือกระยะเวลาที่เหมาะสม",
        choices: [
          { id: "a", content: "10 วินาที", type: "text", isCorrect: false },
          { id: "b", content: "20 วินาที", type: "text", isCorrect: true },
          { id: "c", content: "5 วินาที", type: "text", isCorrect: false },
          { id: "d", content: "1 นาที", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p14",
        title: "ข้อใดเป็นสัญญาณอันตรายที่ต้องส่งโรงพยาบาลทันที",
        type: "text",
        content: "เลือกสัญญาณที่ต้องรีบส่งโรงพยาบาล",
        choices: [
          { id: "a", content: "ปวดหัวเล็กน้อย", type: "text", isCorrect: false },
          { id: "b", content: "หายใจลำบากมาก", type: "text", isCorrect: true },
          { id: "c", content: "ไอเล็กน้อย", type: "text", isCorrect: false },
          { id: "d", content: "เหนื่อยล้าทั่วไป", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p15",
        title: "การใส่หน้ากากอนามัยที่ถูกต้องคือ",
        type: "text",
        content: "เลือกวิธีการใส่หน้ากากที่ถูกต้อง",
        choices: [
          { id: "a", content: "ปิดปากเท่านั้น", type: "text", isCorrect: false },
          { id: "b", content: "ปิดจมูกและปาก", type: "text", isCorrect: true },
          { id: "c", content: "ใส่หลวมๆ", type: "text", isCorrect: false },
          { id: "d", content: "ใส่กลับด้าน", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p16",
        title: "อัตราการเต้นของหัวใจปกติในคนผู้ใหญ่คือกี่ครั้งต่อนาที",
        type: "text",
        content: "เลือกคำตอบที่ถูกต้อง",
        choices: [
          { id: "a", content: "40-60 ครั้ง", type: "text", isCorrect: false },
          { id: "b", content: "60-100 ครั้ง", type: "text", isCorrect: true },
          { id: "c", content: "100-120 ครั้ง", type: "text", isCorrect: false },
          { id: "d", content: "120-140 ครั้ง", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p17",
        title: "การปฐมพยาบาลผู้ป่วยไข้สูงควรทำอย่างไร",
        type: "text",
        content: "เลือกวิธีการที่ถูกต้อง",
        choices: [
          { id: "a", content: "ให้นอนในห้องอุ่น", type: "text", isCorrect: false },
          { id: "b", content: "เช็ดตัวด้วยน้ำอุ่น", type: "text", isCorrect: true },
          { id: "c", content: "ใส่เสื้อผ้าหนาๆ", type: "text", isCorrect: false },
          { id: "d", content: "ไม่ให้ดื่มน้ำ", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p18",
        title: "ยาใดที่ใช้ลดไข้ได้อย่างปลอดภัย",
        type: "text",
        content: "เลือกยาลดไข้ที่ปลอดภัย",
        choices: [
          { id: "a", content: "แอสไพริน", type: "text", isCorrect: false },
          { id: "b", content: "พาราเซตามอล", type: "text", isCorrect: true },
          { id: "c", content: "ยาแก้ปวดสมุนไพร", type: "text", isCorrect: false },
          { id: "d", content: "ยาต้มจีน", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p19",
        title: "การออกซิเจนบำบัดใช้ในกรณีใด",
        type: "text",
        content: "เลือกกรณีที่ต้องใช้ออกซิเจน",
        choices: [
          { id: "a", content: "ผู้ป่วยปวดหัว", type: "text", isCorrect: false },
          { id: "b", content: "ผู้ป่วยหายใจลำบาก", type: "text", isCorrect: true },
          { id: "c", content: "ผู้ป่วยท้องเสีย", type: "text", isCorrect: false },
          { id: "d", content: "ผู้ป่วยปวดท้อง", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p20",
        title: "หลักการสำคัญของการปฐมพยาบาลคือข้อใด",
        type: "text",
        content: "เลือกหลักการที่สำคัญที่สุด",
        choices: [
          { id: "a", content: "รักษาให้หายขาด", type: "text", isCorrect: false },
          { id: "b", content: "ไม่ให้เกิดอันตรายเพิ่มเติม", type: "text", isCorrect: true },
          { id: "c", content: "ใช้ยาแก้ปวด", type: "text", isCorrect: false },
          { id: "d", content: "ส่งโรงพยาบาลทันที", type: "text", isCorrect: false },
        ],
      }
    ],
  },
  {
    id: "e2",
    courseId: "QlH3I647MCuN", // ตัวอย่างคอร์สที่ 2
    name: "แบบทดสอบหลังเรียน - การปฐมพยาบาลเบื้องต้น",
    description: "ข้อสอบนี้ทดสอบความรู้หลังเรียนเกี่ยวกับการปฐมพยาบาลเบื้องต้น",
    passingScore: 80,
    timeLimit: 45, // หน่วยเป็นนาที
    questions: [
      {
        id: "d1",
        title: "โรคซึมเศร้าคืออะไร",
        type: "text",
        content: "เลือกคำอธิบายที่ถูกต้องเกี่ยวกับโรคซึมเศร้า",
        choices: [
          { id: "a", content: "อารมณ์แปรปรวนตามฤดูกาล", type: "text", isCorrect: false },
          { id: "b", content: "ความผิดปกติทางอารมณ์ที่เกิดขึ้นชั่วคราว", type: "text", isCorrect: false },
          { id: "c", content: "ภาวะทางจิตที่ทำให้รู้สึกเศร้า สิ้นหวัง และหมดแรงอย่างต่อเนื่อง", type: "text", isCorrect: true },
          { id: "d", content: "พฤติกรรมต่อต้านสังคม", type: "text", isCorrect: false },
        ],
      },
      {
        id: "d2",
        title: "อาการใดเป็นอาการหลักของโรคซึมเศร้า",
        type: "text",
        content: "เลือกอาการหลักที่พบบ่อยในผู้ป่วยโรคซึมเศร้า",
        choices: [
          { id: "a", content: "เบื่ออาหารและน้ำหนักลด", type: "text", isCorrect: false },
          { id: "b", content: "นอนไม่หลับหรือนอนมากเกินไป", type: "text", isCorrect: false },
          { id: "c", content: "ไม่มีความสุขกับกิจกรรมที่เคยชอบ", type: "text", isCorrect: true },
          { id: "d", content: "พูดเร็วเกินปกติ", type: "text", isCorrect: false },
        ],
      },
      {
        id: "d3",
        title: "โรคซึมเศร้าส่งผลต่อร่างกายอย่างไร",
        type: "text",
        content: "เลือกคำตอบที่ถูกต้องเกี่ยวกับผลกระทบทางร่างกายจากโรคซึมเศร้า",
        choices: [
          { id: "a", content: "ทำให้กล้ามเนื้อเจริญเติบโตเร็วขึ้น", type: "text", isCorrect: false },
          { id: "b", content: "อาจทำให้รู้สึกเหนื่อยล้าเรื้อรังและปวดเมื่อย", type: "text", isCorrect: true },
          { id: "c", content: "เพิ่มสมรรถภาพทางกาย", type: "text", isCorrect: false },
          { id: "d", content: "ไม่มีผลต่อร่างกาย", type: "text", isCorrect: false },
        ],
      },
      {
        id: "d4",
        title: "ปัจจัยเสี่ยงที่อาจนำไปสู่โรคซึมเศร้ามีอะไรบ้าง",
        type: "text",
        content: "เลือกข้อที่เป็นปัจจัยเสี่ยงของโรคซึมเศร้า",
        choices: [
          { id: "a", content: "พันธุกรรม", type: "text", isCorrect: true },
          { id: "b", content: "ทัศนคติในเชิงบวก", type: "text", isCorrect: false },
          { id: "c", content: "การออกกำลังกายสม่ำเสมอ", type: "text", isCorrect: false },
          { id: "d", content: "อารมณ์มั่นคง", type: "text", isCorrect: false },
        ],
      },
      {
        id: "d5",
        title: "แนวทางการรักษาโรคซึมเศร้ามีอะไรบ้าง (เลือกทุกข้อที่ถูกต้อง)",
        type: "text",
        content: "เลือกทุกข้อที่เป็นแนวทางในการรักษาโรคซึมเศร้า",
        choices: [
          { id: "a", content: "การใช้ยา", type: "text", isCorrect: true },
          { id: "b", content: "การบำบัดจิตใจ (Psychotherapy)", type: "text", isCorrect: true },
          { id: "c", content: "การออกกำลังกาย", type: "text", isCorrect: true },
          { id: "d", content: "การปฏิเสธความรู้สึก", type: "text", isCorrect: false },
        ],
        
      },
      {
        id: "d6",
        title: "บุคคลใดควรได้รับการปรึกษาเมื่อสงสัยว่าตนเองเป็นโรคซึมเศร้า",
        type: "text",
        content: "เลือกคำตอบที่เหมาะสมที่สุด",
        choices: [
          { id: "a", content: "แพทย์หรือจิตแพทย์", type: "text", isCorrect: true },
          { id: "b", content: "เพื่อนสนิท", type: "text", isCorrect: false },
          { id: "c", content: "ช่างเสริมสวย", type: "text", isCorrect: false },
          { id: "d", content: "ดารานักแสดง", type: "text", isCorrect: false },
        ],
      },
      {
        id: "d7",
        title: "การรับฟังอย่างเข้าใจ (Empathic listening) ช่วยผู้มีภาวะซึมเศร้าอย่างไร",
        type: "text",
        content: "เลือกคำตอบที่ถูกต้องที่สุด",
        choices: [
          { id: "a", content: "ช่วยให้ผู้ป่วยรู้สึกว่าไม่ได้อยู่คนเดียว", type: "text", isCorrect: true },
          { id: "b", content: "เพิ่มความเครียด", type: "text", isCorrect: false },
          { id: "c", content: "กระตุ้นให้ผู้ป่วยตัดสินใจเร็ว", type: "text", isCorrect: false },
          { id: "d", content: "ไม่มีผลใดๆ", type: "text", isCorrect: false },
        ],
      },
      {
        id: "d8",
        title: "ระยะเวลาของอาการซึมเศร้าที่สามารถจัดเป็น 'โรคซึมเศร้า' ต้องนานเท่าใดอย่างน้อย",
        type: "text",
        content: "เลือกคำตอบที่ถูกต้อง",
        choices: [
          { id: "a", content: "1 วัน", type: "text", isCorrect: false },
          { id: "b", content: "3 วัน", type: "text", isCorrect: false },
          { id: "c", content: "1 สัปดาห์", type: "text", isCorrect: false },
          { id: "d", content: "2 สัปดาห์", type: "text", isCorrect: true },
        ],
      },
      {
        id: "d9",
        title: "โรคซึมเศร้าสามารถรักษาให้หายได้หรือไม่",
        type: "text",
        content: "เลือกคำตอบที่ถูกต้อง",
        choices: [
          { id: "a", content: "ไม่ได้ ต้องอยู่กับโรคตลอดชีวิต", type: "text", isCorrect: false },
          { id: "b", content: "สามารถรักษาให้หายหรือควบคุมอาการได้", type: "text", isCorrect: true },
          { id: "c", content: "ต้องรักษาด้วยสมุนไพรเท่านั้น", type: "text", isCorrect: false },
          { id: "d", content: "ปล่อยไว้จะหายเอง", type: "text", isCorrect: false },
        ],
      },
      {
        id: "d10",
        title: "สิ่งใดไม่ควรทำเมื่อพูดคุยกับผู้ที่มีภาวะซึมเศร้า",
        type: "text",
        content: "เลือกคำตอบที่ไม่เหมาะสม",
        choices: [
          { id: "a", content: "บอกให้เขา 'สู้ๆ'", type: "text", isCorrect: true },
          { id: "b", content: "ฟังเขาอย่างตั้งใจ", type: "text", isCorrect: false },
          { id: "c", content: "อยู่เคียงข้างเขา", type: "text", isCorrect: false },
          { id: "d", content: "ให้กำลังใจโดยไม่ตัดสิน", type: "text", isCorrect: false },
        ],
      }
      
    ],
  },
  {
    id: "e3",
    courseId: "19x4uNv4RgED", // คอร์สการดูแลผู้ป่วยเบาหวาน
    name: "แบบทดสอบความรู้ - การดูแลผู้ป่วยเบาหวาน",
    description: "ข้อสอบนี้ทดสอบความรู้เกี่ยวกับการดูแลผู้ป่วยเบาหวาน",
    passingScore: 75,
    timeLimit: 30, // หน่วยเป็นนาที
    questions: [
      {
        id: "p1",
        title: "อาการของโรคเบาหวานที่พบได้บ่อย",
        type: "text",
        content: "เลือกอาการที่พบได้บ่อยในผู้ป่วยเบาหวาน",
        choices: [
          { id: "a", content: "ปวดหัวรุนแรง", type: "text", isCorrect: false },
          { id: "b", content: "กระหายน้ำ ปัสสาวะบ่อย น้ำหนักลด", type: "text", isCorrect: true },
          { id: "c", content: "ไอแสบคอ", type: "text", isCorrect: false },
          { id: "d", content: "ปวดท้อง", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p2",
        title: "การควบคุมระดับน้ำตาลในเลือด",
        type: "multiple",
        content: "วิธีการควบคุมระดับน้ำตาลในเลือดที่ถูกต้อง (เลือกได้หลายข้อ)",
        choices: [
          { id: "a", content: "การกินอาหารที่มีเส้นใยสูง", type: "text", isCorrect: true },
          { id: "b", content: "การออกกำลังกายสม่ำเสมอ", type: "text", isCorrect: true },
          { id: "c", content: "การรับประทานยาตามแพทย์สั่ง", type: "text", isCorrect: true },
          { id: "d", content: "การกินขนมหวานมากๆ", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p3",
        title: "ภาวะแทรกซ้อนจากโรคเบาหวาน",
        type: "text",
        content: "ภาวะแทรกซ้อนระยะยาวที่สำคัญของโรคเบาหวาน",
        choices: [
          { id: "a", content: "โรคไตเรื้อรัง และโรคตาเสื่อม", type: "text", isCorrect: true },
          { id: "b", content: "โรคหัวใจขาดเลือด", type: "text", isCorrect: false },
          { id: "c", content: "โรคปอดอักเสบ", type: "text", isCorrect: false },
          { id: "d", content: "โรคไทรอยด์", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p4",
        title: "การดูแลเท้าของผู้ป่วยเบาหวาน",
        type: "text",
        content: "ข้อใดถูกต้องเกี่ยวกับการดูแลเท้าของผู้ป่วยเบาหวาน",
        choices: [
          { id: "a", content: "ตรวจดูเท้าทุกวัน และใส่รองเท้าที่พอดี", type: "text", isCorrect: true },
          { id: "b", content: "แช่เท้าในน้ำร้อนทุกวัน", type: "text", isCorrect: false },
          { id: "c", content: "เดินเท้าเปล่าในบ้าน", type: "text", isCorrect: false },
          { id: "d", content: "ตัดเล็บเท้าให้สั้นมาก", type: "text", isCorrect: false },
        ],
      },
      {
        id: "p5",
        title: "อาหารที่เหมาะสำหรับผู้ป่วยเบาหวาน",
        type: "text",
        content: "อาหารใดเหมาะสำหรับผู้ป่วยเบาหวานมากที่สุด",
        choices: [
          { id: "a", content: "ผลไม้หวานทุกชนิด", type: "text", isCorrect: false },
          { id: "b", content: "ข้าวโพดต้ม", type: "text", isCorrect: false },
          { id: "c", content: "ผักใบเขียว โปรตีนไม่ติดมัน", type: "text", isCorrect: true },
          { id: "d", content: "น้ำผลไม้ปั่น", type: "text", isCorrect: false },
        ],
      }
      
    ],
  },
  
  // เพิ่มข้อมูลข้อสอบท้ายบทสำหรับคอร์สอื่นๆ ตามต้องการ
]

// ฟังก์ชันสำหรับดึงข้อมูลข้อสอบท้ายบทตาม courseId
export function getFinalExamData(courseId: string): FinalExamData | undefined {
  return finalExamData.find((exam) => exam.courseId === courseId)
}

// ฟังก์ชันสำหรับตรวจสอบว่าคอร์สมีข้อสอบท้ายบทหรือไม่
export function hasFinalExam(courseId: string): boolean {
  // ตรวจสอบจากข้อมูลข้อสอบท้ายบท
  const hasExam = finalExamData.some((exam) => exam.courseId === courseId)

  // ถ้าไม่มีในข้อมูลข้อสอบท้ายบท ให้ตรวจสอบจากข้อมูลคอร์ส
  if (!hasExam) {
    const course = getCoursesData.find((c) => c.id === courseId)
    return course?.certify === true
  }

  return hasExam
}

// ฟังก์ชันสำหรับดึงข้อมูลข้อสอบท้ายบทตาม examId
export function getFinalExamById(examId: string): FinalExamData | undefined {
  return finalExamData.find((exam) => exam.id === examId)
}

// ฟังก์ชันสำหรับดึงข้อมูลข้อสอบท้ายบททั้งหมด
export function getAllFinalExams(): FinalExamData[] {
  return finalExamData
}
