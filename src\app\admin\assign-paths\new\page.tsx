"use client"

import { Suspense } from "react"
import { useSearchParams } from "next/navigation"
import AssignEditor from "@/components/admin/assign-path-editor"
import AdminLayout from "@/components/admin/layout"

function NewAssignPageContent() {
  const searchParams = useSearchParams()
  const studentId = searchParams.get("studentId")

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
        </div>

        <AssignEditor mode="create" preSelectedStudentId={studentId || undefined} />
      </div>
    </AdminLayout>
  )
}

export default function NewAssignPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <NewAssignPageContent />
    </Suspense>
  )
}
