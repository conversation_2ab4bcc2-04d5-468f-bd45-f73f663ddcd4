"use client"

import { useEffect, useState, useRef, useCallback } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { BookOpen, Award, Calendar, ChevronRight, Filter, CheckCircle, Circle, Search, ArrowLeft, Target } from "lucide-react"
import { profileService, UserCourse } from "@/hook/profileService"
import Navbar from "@/components/headers"
import CourseCard from "@/components/progressCard"
import CourseCardSkeleton from "@/components/CourseCardSkeleton"
import type { CoursesDashboardResponse } from "@/hook/profileService"

// นำเข้า Types จากไฟล์ types ทั้งหมด
import type { MedicalUser } from "@/types/users"
import type { UserProgress, LearningStatus } from "@/types/progress"
import DashboardSidebar from "@/components/DashboardSidebar"

// ประเภทของการกรองคอร์ส
type FilterType = "all" | LearningStatus

export default function MyCoursesPage() {
  const router = useRouter()
  const [userCourses, setUserCourses] = useState<UserCourse[]>([])
  const [filteredCourses, setFilteredCourses] = useState<UserCourse[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentFilter, setCurrentFilter] = useState<FilterType>("all")
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    items_per_page: 0,
    total_items: 0,
    has_next: false,
    has_prev: false,
  })
  const [page, setPage] = useState(1)
  const [isFetchingMore, setIsFetchingMore] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [showPlaceholders, setShowPlaceholders] = useState(false)
  const observer = useRef<IntersectionObserver | null>(null)
  const sentinelRef = useRef<HTMLDivElement>(null)

  // Fetch courses for the current page
  useEffect(() => {
    const fetchCourses = async () => {
      setIsLoading(true)
      try {
        const response: CoursesDashboardResponse | UserCourse[] = await profileService.getUserCoursesDashboard(page)
        let courses: UserCourse[] = []
        let paginationData = {
          current_page: 1,
          total_pages: 1,
          items_per_page: 0,
          total_items: 0,
          has_next: false,
          has_prev: false,
        }
        if (Array.isArray(response)) {
          courses = response
        } else if (response && Array.isArray(response.data)) {
          courses = response.data
          if (response.pagination) {
            paginationData = {
              current_page: response.pagination.current_page,
              total_pages: response.pagination.total_pages,
              items_per_page: response.pagination.items_per_page,
              total_items: response.pagination.total_items,
              has_next: response.pagination.has_next,
              has_prev: response.pagination.has_prev,
            }
          }
        }
        setUserCourses(courses)
        setFilteredCourses(courses)
        setPagination(paginationData)
      } catch (error) {
        setUserCourses([])
        setFilteredCourses([])
        setPagination({
          current_page: 1,
          total_pages: 1,
          items_per_page: 0,
          total_items: 0,
          has_next: false,
          has_prev: false,
        })
      } finally {
        setIsLoading(false)
      }
    }
    fetchCourses()
  }, [page])

  // Infinite scroll: fetch next page when sentinel is visible
  const fetchMore = useCallback(async () => {
    if (isFetchingMore || isLoading || !pagination.has_next) return
    setIsFetchingMore(true)
    setShowPlaceholders(true)
    setHasError(false)

    // Start timer for minimum 2 seconds
    const startTime = Date.now()

    try {
      const nextPage = pagination.current_page + 1
      const response: CoursesDashboardResponse | UserCourse[] = await profileService.getUserCoursesDashboard(nextPage)
      let newCourses: UserCourse[] = []

      if (Array.isArray(response)) {
        newCourses = response
      } else if (response && Array.isArray(response.data)) {
        newCourses = response.data
      }

      // Ensure minimum 2 seconds have passed before showing data
      const elapsedTime = Date.now() - startTime
      const remainingTime = Math.max(0, 500 - elapsedTime)

      setTimeout(() => {
        setUserCourses((prev) => [...prev, ...newCourses])
        setFilteredCourses((prev) => [...prev, ...newCourses])

        if (!Array.isArray(response) && response.pagination) {
          setPagination({
            current_page: response.pagination.current_page,
            total_pages: response.pagination.total_pages,
            items_per_page: response.pagination.items_per_page,
            total_items: response.pagination.total_items,
            has_next: response.pagination.has_next,
            has_prev: response.pagination.has_prev,
          })
        }
        setShowPlaceholders(false)
        setIsFetchingMore(false)
      }, remainingTime)
    } catch (error) {
      setHasError(true)
      console.error('Failed to fetch more courses:', error)

      // Ensure minimum 2 seconds have passed before hiding placeholders on error
      const elapsedTime = Date.now() - startTime
      const remainingTime = Math.max(0, 2000 - elapsedTime)

      setTimeout(() => {
        setShowPlaceholders(false)
        setIsFetchingMore(false)
      }, remainingTime)
    }
  }, [isFetchingMore, isLoading, pagination])

  useEffect(() => {
    if (!sentinelRef.current) return
    if (observer.current) observer.current.disconnect()

    observer.current = new window.IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && pagination.has_next && !isFetchingMore && !showPlaceholders) {
          fetchMore()
        }
      },
      {
        root: null,
        rootMargin: '100px', // Start loading 100px before reaching the sentinel
        threshold: 0.1
      }
    )

    observer.current.observe(sentinelRef.current)
    return () => {
      if (observer.current) observer.current.disconnect()
    }
  }, [fetchMore, pagination.has_next, isFetchingMore, showPlaceholders])

  // กรองคอร์สตามสถานะและคำค้นหา (client-side filtering)
  useEffect(() => {
    if (!searchQuery && currentFilter === "all") {
      setFilteredCourses(userCourses)
      return
    }

    let filtered = [...userCourses]
    if (currentFilter !== "all") {
      filtered = filtered.filter((course) =>
        currentFilter === "completed"
          ? course.course_graduated
          : !course.course_graduated
      )
    }
    if (searchQuery) {
      filtered = filtered.filter((course) =>
        course.course_name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }
    setFilteredCourses(filtered)
  }, [currentFilter, userCourses, searchQuery])

  // แปลงข้อความสถานะเป็นภาษาไทย
  const getStatusText = (status: LearningStatus | "all"): string => {
    switch (status) {
      case "completed":
        return "เรียนจบแล้ว"
      case "in_progress":
        return "กำลังเรียน"
      case "not_started":
        return "ยังไม่ได้เริ่ม"
      case "all":
        return "ทั้งหมด"
      default:
        return ""
    }
  }

  // Utility for base64 images
  const formatImageSrc = (imageData: string | null | undefined): string => {
    if (!imageData || imageData.trim() === "") return "/placeholder.svg"
    const trimmedData = imageData.trim()
    if (trimmedData.startsWith("data:image/")) return trimmedData
    return `data:image/jpeg;base64,${trimmedData}`
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#008268]"></div>
      </div>
    )
  }

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#f9fafb] w-full">
        <div className="flex w-full">
          {/* Sidebar */}
          <DashboardSidebar />
          {/* Main Content */}
          <div className="flex-1 ml-[60px] pt-[64px] w-[calc(100%-60px)]">
            <div className="w-full p-6">
              {/* Header */}
              <div className="mb-8">
                <div className="flex items-center mb-2">
                  <button
                    onClick={() => router.push("/profile/dashboard")}
                    className="mr-2 p-1 rounded-full hover:bg-gray-200"
                  >
                    <ArrowLeft size={20} />
                  </button>
                  <h1 className="text-2xl font-bold text-gray-800">คอร์สเรียนของฉัน</h1>
                </div>
                <p className="text-gray-600">จัดการและติดตามความก้าวหน้าในการเรียนของคุณ</p>
              </div>
              {/* Search and Filter */}
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                <div className="relative w-full md:w-80">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="ค้นหาคอร์สเรียน..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-2 border bg-white border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-[#008268]"
                  />
                </div>
                <div className="relative">
                  <button
                    onClick={() => setShowFilterDropdown(!showFilterDropdown)}
                    className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <Filter size={16} />
                    <span>{getStatusText(currentFilter)}</span>
                    <ChevronRight
                      size={16}
                      className={`transition-transform ${showFilterDropdown ? "rotate-90" : ""}`}
                    />
                  </button>
                  {showFilterDropdown && (
                    <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10">
                      <div className="py-1">
                        <button
                          onClick={() => {
                            setCurrentFilter("all")
                            setShowFilterDropdown(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-left hover:bg-gray-100"
                        >
                          {currentFilter === "all" ? (
                            <CheckCircle size={16} className="mr-2 text-green-600" />
                          ) : (
                            <Circle size={16} className="mr-2 text-gray-300" />
                          )}
                          ทั้งหมด
                        </button>
                        <button
                          onClick={() => {
                            setCurrentFilter("completed")
                            setShowFilterDropdown(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-left hover:bg-gray-100"
                        >
                          {currentFilter === "completed" ? (
                            <CheckCircle size={16} className="mr-2 text-green-600" />
                          ) : (
                            <Circle size={16} className="mr-2 text-gray-300" />
                          )}
                          เรียนจบแล้ว
                        </button>
                        <button
                          onClick={() => {
                            setCurrentFilter("in_progress")
                            setShowFilterDropdown(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-left hover:bg-gray-100"
                        >
                          {currentFilter === "in_progress" ? (
                            <CheckCircle size={16} className="mr-2 text-green-600" />
                          ) : (
                            <Circle size={16} className="mr-2 text-gray-300" />
                          )}
                          กำลังเรียน
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {/* Course Grid */}
              {filteredCourses.length > 0 ? (
                <>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 md:gap-6 w-full">
                    {filteredCourses.map((course) => (
                      <CourseCard
                        key={course.course_slug}
                        id={course.course_slug}
                        name={course.course_name}
                        description={course.course_description}
                        coverImage={formatImageSrc(course.course_image)}
                        progress={course.course_processing || 0}
                        completedLessons={course.course_lessoned || 0}
                        totalLessons={course.course_amount || 0}
                        duration={course.course_duration}
                        status={course.course_graduated ? "completed" : "in_progress"}
                        level={course.course_difficulty}
                        certify={false}
                      />
                    ))}

                    {/* Loading Placeholder Cards */}
                    {showPlaceholders && (
                      <CourseCardSkeleton count={4} />
                    )}
                  </div>

                  {/* Loading States and End Indicators */}
                  {pagination.has_next && (
                    <div ref={sentinelRef} className="h-4" />
                  )}

                  {isFetchingMore && !showPlaceholders && (
                    <div className="flex flex-col items-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-2 border-[#008268] border-t-transparent mb-3"></div>
                      <p className="text-gray-600 text-sm">กำลังโหลดคอร์สเพิ่มเติม...</p>
                    </div>
                  )}

                  {hasError && pagination.has_next && !showPlaceholders && (
                    <div className="flex flex-col items-center py-8">
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                        <p className="text-red-600 text-sm mb-3">เกิดข้อผิดพลาดในการโหลดข้อมูล</p>
                        <button
                          onClick={fetchMore}
                          className="px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 transition-colors"
                        >
                          ลองใหม่
                        </button>
                      </div>
                    </div>
                  )}

                  {!pagination.has_next && filteredCourses.length > 0 && !isFetchingMore && !showPlaceholders && (
                    <div className="flex flex-col items-center py-8">
                      <div className="bg-gray-100 rounded-full p-3 mb-3">
                        <CheckCircle className="h-6 w-6 text-gray-500" />
                      </div>
                      <p className="text-gray-500 text-sm">คุณได้ดูคอร์สเรียนทั้งหมดแล้ว</p>
                      <p className="text-gray-400 text-xs mt-1">รวม {pagination.total_items} คอร์สเรียน</p>
                    </div>
                  )}
                </>
              ) : (
                <div className="bg-white rounded-xl p-8 text-center w-full">
                  <div className="mb-4">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
                      <BookOpen size={32} className="text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 mb-2">ไม่พบคอร์สเรียน</h3>
                    <p className="text-gray-600 mb-6">
                      {searchQuery
                        ? "ไม่พบคอร์สเรียนที่ตรงกับคำค้นหา"
                        : currentFilter === "all"
                          ? "คุณยังไม่มีคอร์สที่กำลังเรียนหรือเรียนจบแล้ว"
                          : getStatusText(currentFilter as LearningStatus) === "เรียนจบแล้ว"
                            ? "คุณยังไม่มีคอร์สที่เรียนจบ"
                            : "คุณยังไม่มีคอร์สที่กำลังเรียน"}
                    </p>
                    <Link
                      href="/courses"
                      className="inline-block px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58] transition-colors"
                    >
                      ดูคอร์สเรียนทั้งหมด
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        <style jsx global>{`
          .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
  
          /* iPad Pro และอุปกรณ์ขนาดกลาง */
          @media (min-width: 768px) and (max-width: 1024px) {
            .grid-cols-2 {
              grid-template-columns: repeat(2, minmax(0, 1fr));
            }
    
            /* เพิ่ม padding ให้กับ content เพื่อให้มีพื้นที่มากขึ้น */
            .p-6 {
              padding: 1.25rem;
            }
    
            /* ปรับขนาด gap ระหว่างการ์ด */
            .gap-6 {
              gap: 1rem;
            }
          }
  
          /* สำหรับ iPad Pro แนวนอน */
          @media (min-width: 1024px) and (max-width: 1366px) {
            .grid-cols-3 {
              grid-template-columns: repeat(3, minmax(0, 1fr));
            }
          }
        `}</style>
      </div>
    </>
  )
}
