"use client"

import { useState } from "react"
import { Search, Download, Filter, User, BookOpen, Route, TrendingUp, Calendar, Mail, Building2, X, ChevronDown } from "lucide-react"
import AdminLayout from "@/components/admin/layout"
import { useRouter } from "next/navigation"
import html2canvas from "html2canvas"
import jsPDF from "jspdf"


// Mock student list for dropdown (now includes course and path data)
const mockStudentList = [
  {
    id: "STU001",
    name: "นางสาวสุภา ใจดี",
    email: "<EMAIL>",
    department: "แผนกการพยาบาล",
    joinDate: "2024-01-15",
    mockCourseData: [
      // 20+ mock courses for multi-page PDF
      { id: 1, courseName: "หลักการพยาบาลเบื้องต้น", totalScore: 85, passingScore: 70, status: "ผ่าน", completedDate: "2024-02-20" },
      { id: 2, courseName: "การดูแลผู้ป่วยวิกฤต", totalScore: 92, passingScore: 75, status: "ผ่าน", completedDate: "2024-03-15" },
      { id: 3, courseName: "เวชศาสตร์ฉุกเฉิน", totalScore: 65, passingScore: 70, status: "ไม่ผ่าน", completedDate: "2024-04-10" },
      { id: 4, courseName: "การจัดการยาและเวชภัณฑ์", totalScore: 88, passingScore: 75, status: "ผ่าน", completedDate: "2024-04-25" },
      { id: 5, courseName: "การพยาบาลผู้สูงอายุ", totalScore: 78, passingScore: 70, status: "ผ่าน", completedDate: "2024-05-01" },
      { id: 6, courseName: "การดูแลแผล", totalScore: 82, passingScore: 70, status: "ผ่าน", completedDate: "2024-05-10" },
      { id: 7, courseName: "การให้ยา", totalScore: 90, passingScore: 75, status: "ผ่าน", completedDate: "2024-05-15" },
      { id: 8, courseName: "การดูแลผู้ป่วยเด็ก", totalScore: 74, passingScore: 70, status: "ผ่าน", completedDate: "2024-05-20" },
      { id: 9, courseName: "การพยาบาลฉุกเฉิน", totalScore: 68, passingScore: 70, status: "ไม่ผ่าน", completedDate: "2024-05-25" },
      { id: 10, courseName: "การดูแลผู้ป่วยโรคเรื้อรัง", totalScore: 80, passingScore: 70, status: "ผ่าน", completedDate: "2024-06-01" },
      { id: 11, courseName: "การดูแลผู้ป่วยติดเตียง", totalScore: 77, passingScore: 70, status: "ผ่าน", completedDate: "2024-06-05" },
      { id: 12, courseName: "การดูแลผู้ป่วยหลังผ่าตัด", totalScore: 83, passingScore: 75, status: "ผ่าน", completedDate: "2024-06-10" },
      { id: 13, courseName: "การดูแลผู้ป่วยเบาหวาน", totalScore: 69, passingScore: 70, status: "ไม่ผ่าน", completedDate: "2024-06-15" },
      { id: 14, courseName: "การดูแลผู้ป่วยความดันสูง", totalScore: 91, passingScore: 75, status: "ผ่าน", completedDate: "2024-06-20" },
      { id: 15, courseName: "การดูแลผู้ป่วยมะเร็ง", totalScore: 87, passingScore: 75, status: "ผ่าน", completedDate: "2024-06-25" },
      { id: 16, courseName: "การดูแลผู้ป่วยติดเชื้อ", totalScore: 73, passingScore: 70, status: "ผ่าน", completedDate: "2024-07-01" },
      { id: 17, courseName: "การดูแลผู้ป่วยอุบัติเหตุ", totalScore: 66, passingScore: 70, status: "ไม่ผ่าน", completedDate: "2024-07-05" },
      { id: 18, courseName: "การดูแลผู้ป่วยจิตเวช", totalScore: 79, passingScore: 70, status: "ผ่าน", completedDate: "2024-07-10" },
      { id: 19, courseName: "การดูแลผู้ป่วยเด็กแรกเกิด", totalScore: 84, passingScore: 75, status: "ผ่าน", completedDate: "2024-07-15" },
      { id: 20, courseName: "การดูแลผู้ป่วยไตวาย", totalScore: 76, passingScore: 70, status: "ผ่าน", completedDate: "2024-07-20" },
      { id: 21, courseName: "การดูแลผู้ป่วยหัวใจ", totalScore: 88, passingScore: 75, status: "ผ่าน", completedDate: "2024-07-25" },
      { id: 22, courseName: "การดูแลผู้ป่วยทางเดินหายใจ", totalScore: 81, passingScore: 70, status: "ผ่าน", completedDate: "2024-07-30" },
    ],
    mockPathData: [
      { id: 1, pathName: "เส้นทางการเรียนรู้พยาบาลเบื้องต้น", totalCourses: 10, completedCourses: 8, progress: 80, status: "กำลังเรียน" },
      { id: 2, pathName: "เส้นทางการเรียนรู้การดูแลผู้ป่วยวิกฤต", totalCourses: 12, completedCourses: 6, progress: 50, status: "กำลังเรียน" },
      { id: 3, pathName: "เส้นทางการเรียนรู้เวชศาสตร์ฉุกเฉิน", totalCourses: 8, completedCourses: 8, progress: 100, status: "เสร็จสมบูรณ์" },
      { id: 4, pathName: "เส้นทางการเรียนรู้ผู้ป่วยเด็ก", totalCourses: 7, completedCourses: 5, progress: 71, status: "กำลังเรียน" },
      { id: 5, pathName: "เส้นทางการเรียนรู้ผู้ป่วยสูงอายุ", totalCourses: 9, completedCourses: 9, progress: 100, status: "เสร็จสมบูรณ์" },
      { id: 6, pathName: "เส้นทางการเรียนรู้ผู้ป่วยโรคเรื้อรัง", totalCourses: 6, completedCourses: 3, progress: 50, status: "กำลังเรียน" },
      { id: 7, pathName: "เส้นทางการเรียนรู้ผู้ป่วยหลังผ่าตัด", totalCourses: 5, completedCourses: 2, progress: 40, status: "กำลังเรียน" },
      { id: 8, pathName: "เส้นทางการเรียนรู้ผู้ป่วยติดเชื้อ", totalCourses: 4, completedCourses: 4, progress: 100, status: "เสร็จสมบูรณ์" },
    ]
  },
  {
    id: "STU002",
    name: "นายสมชาย รักดี",
    email: "<EMAIL>",
    department: "แผนกฉุกเฉิน",
    joinDate: "2024-02-01",
    mockCourseData: [
      { id: 1, courseName: "หลักการพยาบาลเบื้องต้น", totalScore: 70, passingScore: 70, status: "ผ่าน", completedDate: "2024-02-20" },
      { id: 2, courseName: "การดูแลผู้ป่วยวิกฤต", totalScore: 60, passingScore: 75, status: "ไม่ผ่าน", completedDate: "2024-03-15" },
    ],
    mockPathData: [
      { id: 1, pathName: "เส้นทางการเรียนรู้พยาบาลเบื้องต้น", totalCourses: 4, completedCourses: 2, progress: 50, status: "กำลังเรียน" },
    ]
  },
  {
    id: "STU003",
    name: "นางสาวมานี ใจงาม",
    email: "<EMAIL>",
    department: "แผนกศัลยกรรม",
    joinDate: "2024-03-10",
    mockCourseData: [
      { id: 1, courseName: "หลักการพยาบาลเบื้องต้น", totalScore: 90, passingScore: 70, status: "ผ่าน", completedDate: "2024-02-20" },
    ],
    mockPathData: [
      { id: 1, pathName: "เส้นทางการเรียนรู้พยาบาลเบื้องต้น", totalCourses: 4, completedCourses: 4, progress: 100, status: "เสร็จสมบูรณ์" },
    ]
  }
]

// Helper to get selected student's course/path data or fallback to []
const getStudentCourseData = (student: any) => student?.mockCourseData || []
const getStudentPathData = (student: any) => student?.mockPathData || []

type Student = {
  id: string
  name: string
  email: string
  department: string
  joinDate: string
}

export default function PersonalReportPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)
  const router = useRouter()

  const handleDownloadPDF = async () => {
    if (!selectedStudent) {
      alert("กรุณาเลือกนักเรียนก่อนดาวน์โหลดรายงาน")
      return
    }

    setIsGeneratingPDF(true)
    
    try {
      // Create an isolated iframe for PDF generation
      const iframe = document.createElement('iframe')
      iframe.style.position = 'absolute'
      iframe.style.top = '-10000px'
      iframe.style.left = '-10000px'
      iframe.style.width = '1200px'
      iframe.style.height = '1600px'
      document.body.appendChild(iframe)

      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      if (!iframeDoc) throw new Error('Unable to access iframe document')

      // Create PDF HTML content with isolated styles
      const pdfContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>รายงานส่วนบุคคล - ${selectedStudent.name}</title>
          <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&display=swap" rel="stylesheet">
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }
            
            body {
              font-family: 'Noto Sans Thai', sans-serif;
              line-height: 1.6;
              color: #1f2937;
              background: #ffffff;
            }
            
            .container {
              max-width: 1200px;
              margin: 0 auto;
              padding: 40px;
            }
            
            .header {
              text-align: center;
              margin-bottom: 40px;
              border-bottom: 3px solid #008067;
              padding-bottom: 30px;
            }
            
            .logo {
              font-size: 28px;
              font-weight: 700;
              color: #008067;
              margin-bottom: 10px;
            }
            
            .title {
              font-size: 24px;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 8px;
            }
            
            .subtitle {
              font-size: 16px;
              color: #6b7280;
            }
            
            .student-info {
              background: linear-gradient(135deg, #008067 0%, #006b57 100%);
              color: white;
              border-radius: 12px;
              padding: 30px;
              margin-bottom: 30px;
            }
            
            .student-name {
              font-size: 22px;
              font-weight: 600;
              margin-bottom: 20px;
            }
            
            .student-details {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 20px;
            }
            
            .detail-item {
              display: flex;
              flex-direction: column;
            }
            
            .detail-label {
              font-size: 14px;
              opacity: 0.8;
              margin-bottom: 4px;
            }
            
            .detail-value {
              font-size: 16px;
              font-weight: 500;
            }
            
            .section {
              margin-bottom: 40px;
            }
            
            .section-header {
              background: #f8fafc;
              border-left: 4px solid #008067;
              padding: 20px;
              margin-bottom: 20px;
              border-radius: 0 8px 8px 0;
            }
            
            .section-title {
              font-size: 20px;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 4px;
            }
            
            .section-description {
              font-size: 14px;
              color: #6b7280;
            }
            
            .table {
              width: 100%;
              border-collapse: collapse;
              background: white;
              border-radius: 8px;
              overflow: hidden;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              page-break-inside: auto;
            }

            .table th {
              background: #f9fafb;
              padding: 16px;
              text-align: left;
              font-weight: 600;
              color: #374151;
              font-size: 14px;
              border-bottom: 1px solid #e5e7eb;
              page-break-inside: avoid;
              page-break-after: avoid;
            }

            .table td {
              padding: 16px;
              border-bottom: 1px solid #f3f4f6;
              font-size: 14px;
              page-break-inside: avoid;
            }

            .table thead {
              display: table-header-group;
            }

            .table tbody tr {
              page-break-inside: avoid;
              page-break-after: auto;
            }
            
            .table tr:last-child td {
              border-bottom: none;
            }
            
            .status-badge {
              display: inline-block;
              padding: 6px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 600;
            }
            
            .status-pass {
              background: #dcfce7;
              color: #166534;
            }
            
            .status-fail {
              background: #fef2f2;
              color: #991b1b;
            }
            
            .status-complete {
              background: #dbeafe;
              color: #1e40af;
            }
            
            .status-learning {
              background: #fef3c7;
              color: #92400e;
            }
            
            .progress-bar {
              width: 80px;
              height: 8px;
              background: #e5e7eb;
              border-radius: 4px;
              overflow: hidden;
            }
            
            .progress-fill {
              height: 100%;
              border-radius: 4px;
            }
            
            .progress-green {
              background: #10b981;
            }
            
            .progress-yellow {
              background: #f59e0b;
            }
            
            .progress-red {
              background: #ef4444;
            }
            
            .score {
              font-weight: 600;
            }
            
            .score-pass {
              color: #059669;
            }
            
            .score-fail {
              color: #dc2626;
            }
            
            .summary-stats {
              display: grid;
              grid-template-columns: repeat(4, 1fr);
              gap: 20px;
              margin-bottom: 30px;
            }
            
            .stat-card {
              background: white;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              padding: 20px;
              text-align: center;
            }
            
            .stat-number {
              font-size: 24px;
              font-weight: 700;
              color: #008067;
              margin-bottom: 4px;
            }
            
            .stat-label {
              font-size: 14px;
              color: #6b7280;
            }
            
            .footer {
              margin-top: 60px;
              padding-top: 30px;
              border-top: 2px solid #e5e7eb;
              text-align: center;
              color: #6b7280;
              font-size: 12px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">ระบบการเรียนรู้ออนไลน์ E-Med</div>
              <div class="title">รายงานส่วนบุคคล</div>
              <div class="subtitle">รายงานผลการเรียนรู้และความคืบหน้าของผู้เรียน</div>
            </div>

            <div class="student-info">
              <div class="student-name">${selectedStudent.name} (${selectedStudent.id})</div>
              <div class="student-details">
                <div class="detail-item">
                  <div class="detail-label">อีเมล</div>
                  <div class="detail-value">${selectedStudent.email}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">แผนก</div>
                  <div class="detail-value">${selectedStudent.department}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">วันที่เข้าร่วม</div>
                  <div class="detail-value">${selectedStudent.joinDate}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">วันที่สร้างรายงาน</div>
                  <div class="detail-value">${new Date().toLocaleDateString('th-TH')}</div>
                </div>
              </div>
            </div>

            <div class="summary-stats">
              <div class="stat-card">
            <div class="stat-number">${getStudentCourseData(selectedStudent).length}</div>
                <div class="stat-label">คอร์สทั้งหมด</div>
              </div>
              <div class="stat-card">
            <div class="stat-number">${getStudentCourseData(selectedStudent).filter((c: any) => c.status === 'ผ่าน').length}</div>
                <div class="stat-label">คอร์สที่ผ่าน</div>
              </div>
              <div class="stat-card">
            <div class="stat-number">${getStudentPathData(selectedStudent).length}</div>
                <div class="stat-label">เส้นทางการเรียนรู้</div>
              </div>
              <div class="stat-card">
            <div class="stat-number">${getStudentCourseData(selectedStudent).length > 0 ? Math.round(getStudentCourseData(selectedStudent).reduce((sum: number, c: any) => sum + c.totalScore, 0) / getStudentCourseData(selectedStudent).length) : 0}</div>
                <div class="stat-label">คะแนนเฉลี่ย</div>
              </div>
            </div>

            <div class="section">
              <div class="section-header">
                <div class="section-title">ผลการเรียนรู้รายวิชา</div>
                <div class="section-description">คะแนนและผลการเรียนจากคอร์สต่างๆ</div>
              </div>
              <table class="table">
                <thead>
                  <tr>
                    <th>ลำดับ</th>
                    <th>ชื่อคอร์ส</th>
                    <th>คะแนนที่ได้</th>
                    <th>คะแนนผ่าน</th>
                    <th>สถานะ</th>
                    <th>วันที่เสร็จสิ้น</th>
                  </tr>
                </thead>
                <tbody>
                  ${getStudentCourseData(selectedStudent).map((course: any, index: number) => `
                    <tr>
                      <td>${index + 1}</td>
                      <td>${course.courseName}</td>
                      <td>
                        <span class="score ${course.totalScore >= course.passingScore ? 'score-pass' : 'score-fail'}">
                          ${course.totalScore}
                        </span>
                      </td>
                      <td>${course.passingScore}</td>
                      <td>
                        <span class="status-badge ${course.status === 'ผ่าน' ? 'status-pass' : 'status-fail'}">
                          ${course.status}
                        </span>
                      </td>
                      <td>${course.completedDate}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>

            <div class="section">
              <div class="section-header">
                <div class="section-title">ความคืบหน้าเส้นทางการเรียนรู้</div>
                <div class="section-description">ติดตามความคืบหน้าในแต่ละเส้นทางการเรียนรู้</div>
              </div>
              <table class="table">
                <thead>
                  <tr>
                    <th>ลำดับ</th>
                    <th>ชื่อเส้นทางการเรียนรู้</th>
                    <th>คอร์สทั้งหมด</th>
                    <th>คอร์สที่เสร็จแล้ว</th>
                    <th>ความคืบหน้า</th>
                    <th>สถานะ</th>
                  </tr>
                </thead>
                <tbody>
                  ${getStudentPathData(selectedStudent).map((path: any, index: number) => `
                    <tr>
                      <td>${index + 1}</td>
                      <td>${path.pathName}</td>
                      <td>${path.totalCourses}</td>
                      <td>${path.completedCourses}</td>
                      <td>
                        <div style="display: flex; align-items: center; gap: 8px;">
                          <div class="progress-bar">
                            <div class="progress-fill ${path.progress >= 80 ? 'progress-green' : path.progress >= 60 ? 'progress-yellow' : 'progress-red'}" 
                                 style="width: ${path.progress}%"></div>
                          </div>
                          <span>${path.progress}%</span>
                        </div>
                      </td>
                      <td>
                        <span class="status-badge ${path.status === 'เสร็จสมบูรณ์' ? 'status-complete' : 'status-learning'}">
                          ${path.status}
                        </span>
                      </td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>

            <div class="footer">
              <div>รายงานนี้สร้างโดยระบบการเรียนรู้ออนไลน์ E-Med</div>
              <div>วันที่สร้าง: ${new Date().toLocaleDateString('th-TH')} เวลา: ${new Date().toLocaleTimeString('th-TH')}</div>
            </div>
          </div>
        </body>
        </html>
      `

      // Write content to iframe
      iframeDoc.open()
      iframeDoc.write(pdfContent)
      iframeDoc.close()

      // Wait for fonts to load
      await new Promise(resolve => {
        const checkFontLoad = () => {
          if (iframeDoc.fonts.ready) {
            iframeDoc.fonts.ready.then(resolve)
          } else {
            // Fallback timeout
            setTimeout(resolve, 2000)
          }
        }
        
        if (iframe.contentWindow) {
          iframe.onload = checkFontLoad
        } else {
          checkFontLoad()
        }
      })

      // Additional wait to ensure everything is rendered
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Generate PDF
      const canvas = await html2canvas(iframeDoc.body, {
        width: 1200,
        height: 1600,
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })

      const imgData = canvas.toDataURL('image/png')
      const pdf = new jsPDF('p', 'mm', 'a4')
      
      const imgWidth = 210
      const pageHeight = 297
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight
      let position = 0

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      // Clean up iframe
      document.body.removeChild(iframe)

      // Download PDF
      const fileName = `รายงานส่วนบุคคล_${selectedStudent.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`
      pdf.save(fileName)

    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('เกิดข้อผิดพลาดในการสร้าง PDF กรุณาลองอีกครั้ง')
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ผ่าน":
        return "bg-green-100 text-green-800"
      case "ไม่ผ่าน":
        return "bg-red-100 text-red-800"
      case "เสร็จสมบูรณ์":
        return "bg-blue-100 text-blue-800"
      case "กำลังเรียน":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return "bg-green-500"
    if (progress >= 60) return "bg-yellow-500"
    return "bg-red-500"
  }

  const handleCourseClick = (courseId: number, courseName: string) => {
    router.push(`/admin/report/personal/details/course?id=${courseId}&name=${encodeURIComponent(courseName)}`)
  }

  const handlePathClick = (pathId: number, pathName: string) => {
    router.push(`/admin/report/personal/details/path?id=${pathId}&name=${encodeURIComponent(pathName)}`)
  }

  // Filter students based on search term
  const filteredStudents = mockStudentList.filter(s => 
    s.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    s.id.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleStudentSelect = (student: Student) => {
    setSelectedStudent(student)
    setSearchTerm(student.name)
    setShowDropdown(false)
  }

  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    setShowDropdown(value.length > 0 || showDropdown)
    
    // If the search term exactly matches a student name, auto-select them
    const exactMatch = mockStudentList.find(s => 
      s.name.toLowerCase() === value.toLowerCase()
    )
    if (exactMatch) {
      setSelectedStudent(exactMatch)
    }
  }

  const handleInputFocus = () => {
    setShowDropdown(true)
  }

  const handleInputBlur = () => {
    // Delay hiding dropdown to allow clicks on dropdown items
    setTimeout(() => setShowDropdown(false), 200)
  }

  return (
    <AdminLayout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-[#008067] rounded-lg">
                <User className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">รายงานส่วนบุคคล</h1>
                <p className="text-sm text-gray-600">ตรวจสอบผลการเรียนรู้และความคืบหน้าของนักเรียน</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <button 
                onClick={handleDownloadPDF}
                disabled={!selectedStudent || isGeneratingPDF}
                className={`flex items-center px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md ${
                  !selectedStudent || isGeneratingPDF
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-[#008067] text-white hover:bg-[#006b57]'
                }`}
              >
                <Download size={16} className="mr-2" />
                {isGeneratingPDF ? 'กำลังสร้าง PDF...' : 'ดาวน์โหลด PDF'}
              </button>
              <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md">
                <Filter size={16} className="mr-2" />
                ตัวกรอง
              </button>
            </div>
          </div>

          {/* Combined Search & Select Student */}
          <div className="space-y-4">
            <div className="relative">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 z-10" size={20} />
                <input
                  type="text"
                  placeholder="ค้นหาและเลือกนักเรียน..."
                  value={searchTerm}
                  onChange={e => handleSearchChange(e.target.value)}
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                  className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#008067] focus:border-transparent transition-all duration-200"
                />
                <button
                  onClick={(e) => {
                    e.preventDefault()
                    setShowDropdown(!showDropdown)
                  }}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors z-10"
                  type="button"
                >
                  <ChevronDown size={20} />
                </button>
                
                {/* Dropdown for filtered students */}
                {showDropdown && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-20 max-h-60 overflow-y-auto">
                    {(() => {
                      const studentsToShow = searchTerm ? filteredStudents : mockStudentList
                      
                      if (studentsToShow.length === 0) {
                        return (
                          <div className="px-4 py-3 text-center text-gray-500">
                            {searchTerm ? 'ไม่พบนักเรียนที่ค้นหา' : 'ไม่มีข้อมูลนักเรียน'}
                          </div>
                        )
                      }
                      
                      return studentsToShow.map(student => (
                        <button
                          key={student.id}
                          onClick={() => handleStudentSelect(student)}
                          className="w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 border-b border-gray-100 last:border-b-0"
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="font-medium text-gray-900">{student.name}</div>
                              <div className="text-sm text-gray-500">{student.department}</div>
                            </div>
                            <div className="text-sm text-gray-400">{student.id}</div>
                          </div>
                        </button>
                      ))
                    })()}
                  </div>
                )}
              </div>
            </div>
            
            {/* Search results info */}
            {searchTerm && !selectedStudent && filteredStudents.length > 0 && (
              <div className="text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200">
                <div className="flex items-center space-x-2">
                  <Search size={16} className="text-blue-500" />
                  <span>พบนักเรียน {filteredStudents.length} คน - คลิกเพื่อเลือก</span>
                </div>
              </div>
            )}
            
            {/* No results */}
            {searchTerm && filteredStudents.length === 0 && (
              <div className="text-sm text-gray-500 bg-red-50 p-3 rounded-lg border border-red-200">
                <div className="flex items-center space-x-2">
                  <Search size={16} className="text-red-500" />
                  <span>ไม่พบนักเรียนที่ตรงกับ "{searchTerm}"</span>
                </div>
              </div>
            )}
          </div>

          {/* Student Info */}
          {selectedStudent && (
            <div className="mt-6 bg-gradient-to-r from-[#008067] to-[#006b57] text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold flex items-center space-x-2">
                  <User size={24} />
                  <span>{selectedStudent.name}</span>
                </h2>
                <div className="px-3 py-1 bg-white/20 rounded-full text-sm">
                  {selectedStudent.id}
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <Mail size={16} className="opacity-80" />
                  <div>
                    <span className="opacity-80">อีเมล:</span>
                    <div className="font-medium">{selectedStudent.email}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Building2 size={16} className="opacity-80" />
                  <div>
                    <span className="opacity-80">แผนก:</span>
                    <div className="font-medium">{selectedStudent.department}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar size={16} className="opacity-80" />
                  <div>
                    <span className="opacity-80">วันที่เข้าร่วม:</span>
                    <div className="font-medium">{selectedStudent.joinDate}</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!selectedStudent && (
            <div className="mt-6 text-center py-12 bg-gray-50 rounded-xl border-2 border-dashed border-gray-200">
              <User size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">เลือกนักเรียนเพื่อดูรายงาน</h3>
              <p className="text-gray-600">ใช้ช่องค้นหาหรือเลือกจากรายการด้านบนเพื่อดูผลการเรียนรู้</p>
            </div>
          )}
        </div>

        {/* Course Results Table (Top Half) */}
        {selectedStudent && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-6 overflow-hidden">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BookOpen className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">ผลการเรียนรู้รายวิชา</h2>
                  <p className="text-sm text-gray-600">คะแนนและผลการเรียนจากคอร์สต่างๆ</p>
                </div>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ชื่อคอร์ส
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      คะแนนที่ได้
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      คะแนนผ่าน
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      สถานะ
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      วันที่เสร็จสิ้น
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {getStudentCourseData(selectedStudent).map((course: any, index: number) => (
                    <tr key={course.id} className="hover:bg-gray-50 transition-colors duration-150">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => handleCourseClick(course.id, course.courseName)}
                          className="text-sm font-medium text-[#008067] hover:text-[#006b57] hover:underline cursor-pointer transition-colors duration-150 flex items-center space-x-2"
                        >
                          <span className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">
                            {index + 1}
                          </span>
                          <span>{course.courseName}</span>
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <span className={`text-sm font-bold ${course.totalScore >= course.passingScore ? 'text-green-600' : 'text-red-600'}`}>
                            {course.totalScore}
                          </span>
                          <div className="w-12 bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${course.totalScore >= course.passingScore ? 'bg-green-500' : 'bg-red-500'}`}
                              style={{ width: `${(course.totalScore / 100) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{course.passingScore}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getStatusColor(course.status)}`}>
                          {course.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 flex items-center space-x-1">
                          <Calendar size={14} className="text-gray-400" />
                          <span>{course.completedDate}</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Learning Path Progress Table (Bottom Half) */}
        {selectedStudent && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Route className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">ความคืบหน้าเส้นทางการเรียนรู้</h2>
                  <p className="text-sm text-gray-600">ติดตามความคืบหน้าในแต่ละเส้นทางการเรียนรู้</p>
                </div>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ชื่อเส้นทางการเรียนรู้
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      คอร์สทั้งหมด
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      คอร์สที่เสร็จแล้ว
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ความคืบหน้า
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      สถานะ
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {getStudentPathData(selectedStudent).map((path: any, index: number) => (
                    <tr key={path.id} className="hover:bg-gray-50 transition-colors duration-150">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => handlePathClick(path.id, path.pathName)}
                          className="text-sm font-medium text-[#008067] hover:text-[#006b57] hover:underline cursor-pointer transition-colors duration-150 flex items-center space-x-2"
                        >
                          <span className="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-bold">
                            {index + 1}
                          </span>
                          <span>{path.pathName}</span>
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <BookOpen size={14} className="text-gray-400" />
                          <span className="text-sm text-gray-900">{path.totalCourses}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <TrendingUp size={14} className="text-green-500" />
                          <span className="text-sm text-gray-900">{path.completedCourses}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-3">
                          <div className="w-24 bg-gray-200 rounded-full h-3">
                            <div 
                              className={`h-3 rounded-full transition-all duration-300 ${getProgressColor(path.progress)}`}
                              style={{ width: `${path.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium text-gray-900 whitespace-nowrap min-w-[40px]">
                            {path.progress}%
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getStatusColor(path.status)}`}>
                          {path.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
