'use client';

import React, { Suspense, useRef, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface StableMouseGLBProps {
  modelPath: string;
  scale?: number;
}

const Model: React.FC<{ modelPath: string; scale: number }> = ({ modelPath, scale }) => {
  const groupRef = useRef<THREE.Group>(null);
  const [mouse, setMouse] = useState({ x: 0, y: 0 });
  
  const { scene } = useGLTF(modelPath);

  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      const x = (event.clientX / window.innerWidth) * 2 - 1;
      const y = -(event.clientY / window.innerHeight) * 2 + 1;
      setMouse({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Set up brighter materials when scene loads
  useEffect(() => {
    if (scene) {
      scene.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh) {
          // Make materials brighter
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((mat) => {
                if (mat instanceof THREE.MeshStandardMaterial) {
                  mat.emissive = new THREE.Color(0x333333); // Add slight glow
                  mat.emissiveIntensity = 0.1;
                }
              });
            } else if (child.material instanceof THREE.MeshStandardMaterial) {
              child.material.emissive = new THREE.Color(0x333333);
              child.material.emissiveIntensity = 0.1;
            }
          }
        }
      });
    }
  }, [scene]);

  useFrame(() => {
    if (groupRef.current) {
      // Adjust rotation calculation to work with the 270-degree base rotation (90 + 180)
      const baseRotationY = (3 * Math.PI) / 2; // 270 degrees base rotation (flipped 180 from 90)
      const targetRotationY = baseRotationY + (mouse.x * 0.3);
      const targetRotationX = mouse.y * 0.2;
      
      groupRef.current.rotation.y = THREE.MathUtils.lerp(
        groupRef.current.rotation.y,
        targetRotationY,
        0.05
      );
      groupRef.current.rotation.x = THREE.MathUtils.lerp(
        groupRef.current.rotation.x,
        targetRotationX,
        0.05
      );
    }
  });

  if (!scene) return null;

  return (
    <group 
      ref={groupRef} 
      scale={scale} 
      position={[0, -0.1, 0]} 
      rotation={[0, (3 * Math.PI) / 2, 0]} // Initial 270-degree rotation (90 + 180)
    >
      <primitive object={scene} />
    </group>
  );
};

const LoadingFallback = () => (
  <mesh>
    <boxGeometry args={[0.1, 0.1, 0.1]} />
    <meshBasicMaterial transparent opacity={0} />
  </mesh>
);

const StableMouseGLB: React.FC<StableMouseGLBProps> = ({ 
  modelPath, 
  scale = 1.0 
}) => {
  return (
    <div className="w-full h-full">
      <Canvas
        camera={{ position: [0, 0, 2], fov: 60 }}
        gl={{ alpha: true, antialias: true }}
      >
        {/* Brighter lighting setup */}
        <ambientLight intensity={1.0} />
        <directionalLight position={[2, 2, 2]} intensity={1.2} />
        <directionalLight position={[-2, 2, 2]} intensity={0.8} />
        <pointLight position={[0, 2, 1]} intensity={0.6} />
        <pointLight position={[0, -1, 1]} intensity={0.4} />
        
        <Suspense fallback={<LoadingFallback />}>
          <Model modelPath={modelPath} scale={scale} />
        </Suspense>
      </Canvas>
    </div>
  );
};

// Preload the model
useGLTF.preload('/e-med/models/SP.glb');

export default StableMouseGLB;
