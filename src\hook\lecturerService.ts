import { api } from "@/lib/api"

// API Endpoints
const TRACK_LEARNING_API = "/track-learning"
const TRACK_LEARNING_COURSES_API = "/track-learning-courses"
const TRACK_LEARNING_DETAIL_API = "/track-learning-detail"

// Type definitions based on backend response structure
export interface PaginationMeta {
  CurrentPage: number
  TotalPages: number
  TotalItems: number
  ItemsPerPage: number
  HasNext: boolean
  HasPrev: boolean
}

export interface PaginationInfo {
  Page: number
  Limit: number
  TotalItems: number
  TotalPages: number
  HasNext: boolean
  HasPrevious: boolean
}

export interface TrackLearningRow {
  CourseName: string
  PathNames: string[]
  AssignedCount: number
  CourseStatus: string
  CourseSlug: string
}

export interface TrackLearningResponse {
  Data: TrackLearningRow[]
  Pagination: PaginationMeta
}

export interface TrackLearningDetailResponse {
  success: boolean
  data: TrackLearningRow
}

// Course Overview Types
export interface CourseOverview {
  course_id: number
  course_slug: string
  course_name: string
  course_description: string
  total_students: number
  completed_students: number
  in_progress_students: number
  not_started_students: number
}

// Student Tracking Types
export interface StudentDetail {
  user_slug: string
  name: string
  first_name: string
  last_name: string
  position: string
  avatar: string
  progress: number
  last_finish_content_lesson: string
  last_login: string
  status: string
  completed_lessons: number
  total_lessons: number
}

// Pagination Types
export interface PaginationResponse {
  page: number
  limit: number
  total_items: number
  total_pages: number
  has_next: boolean
  has_previous: boolean
}

export interface TrackLearningDetailResponse {
  course_overview: CourseOverview
  students: StudentDetail[]
  pagination: PaginationResponse
}

// --- Exam Stats Types ---
export interface ExamOverview {
  exam_slug: string
  exam_name: string
  course_slug: string
  course_name: string
  status: string
  student_count: number
  submitted_count: number
  passed_count: number
  failed_count: number
  average_score: number
  max_score: number
  passing_score: number
  created_at: string
  updated_at: string
}

export interface ExamStatsPagination {
  page: number
  limit: number
  total_items: number
  total_pages: number
  has_next: boolean
  has_previous: boolean
}

export interface ExamStatsResponse {
  exams: ExamOverview[]
  pagination: ExamStatsPagination
}

export interface ExamStatistics {
  total_students: number
  submitted_count: number
  passed_count: number
  failed_count: number
  not_taken_count: number
  average_score: number
  highest_score: number
  lowest_score: number
  pass_rate: number
  completion_rate: number
}

export interface StudentExamResult {
  user_slug: string
  name: string
  first_name: string
  last_name: string
  position: string
  avatar: string
  score: number | null
  max_score: number
  percentage: number | null
  status: string // "passed", "failed", "not_taken"
  attempt_count: number
  last_attempt_at: string | null
  completed_at: string | null
  time_spent: string | null
  is_passed: boolean
}

export interface ExamDetailStats {
  exam_slug: string
  exam_name: string
  course_slug: string
  course_name: string
  status: string
  total_questions: number
  max_score: number
  passing_score: number
  exam_duration: number
  statistics: ExamStatistics
  students: StudentExamResult[]
  pagination: ExamStatsPagination
}

export interface ExamAttempt {
  attempt_number: number
  score: number
  total_score: number
  status: string
  time_spent: string
  submitted_at: string
}

export interface StudentExamStats {
  id: string
  name: string
  position: string
  status: string
  best_score: number
  attempts: ExamAttempt[]
}

export interface ExamTrackData {
  exam_name: string
  students: StudentExamStats[]
}

// Lecturer Service
export const lecturerService = {
  async getTrackLearning(page: number = 1, search: string = ""): Promise<TrackLearningResponse> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        ...(search && { search })
      })
      
      const response = await api.get<TrackLearningResponse>(`${TRACK_LEARNING_API}?${params}`)
      return response
    } catch (error) {
      console.error('Failed to fetch track learning data:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch track learning data')
    }
  },

  // Get detailed track learning data for a specific course (legacy - uses course_id)
  async getTrackLearningByCourse(courseSlug: string): Promise<TrackLearningRow> {
    try {
      const response = await api.get<TrackLearningDetailResponse>(`${TRACK_LEARNING_API}/${courseSlug}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch track learning course details:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch track learning course details')
    }
  },

  // Get all courses overview for track learning - shows course statistics and student counts
  async getTrackLearningCourses(): Promise<CourseOverview[]> {
    try {
      const response = await api.get<{ courses: CourseOverview[] }>(TRACK_LEARNING_COURSES_API)
      return response.courses
    } catch (error) {
      console.error('Failed to fetch track learning courses:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch track learning courses')
    }
  },

  // Get detailed student tracking data for a specific course
  async getTrackLearningDetail(
    courseSlug: string,
    page: number = 1,
    limit: number = 9,
    search: string = "",
    status: string = ""
  ): Promise<TrackLearningDetailResponse> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(status && { status })
      })
      
      const response = await api.get<TrackLearningDetailResponse>(`${TRACK_LEARNING_DETAIL_API}/${courseSlug}?${params}`)
      return response
    } catch (error) {
      console.error('Failed to fetch track learning detail:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to fetch track learning detail')
    }
  }
}

// --- Lecturer Service Exam Stats Methods ---
export const lecturerExamService = {
  async getExamsStats(page: number = 1, limit: number = 5, search: string = ""): Promise<ExamStatsResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(search && { search })
    })
    return await api.get<ExamStatsResponse>(`/exam-stats?${params}`)
  },

  async getExamStatsDetails(
    examSlug: string,
    page: number = 1,
    limit: number = 10,
    search: string = "",
    status: string = ""
  ): Promise<ExamDetailStats> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(search && { search }),
      ...(status && { status })
    })
    return await api.get<ExamDetailStats>(`/exam-stats/${examSlug}?${params}`)
  },

  async getExamTrackingDetails(examSlug: string): Promise<ExamTrackData> {
    return await api.get<ExamTrackData>(`/exam-stats/details/${examSlug}`)
  }
}
