import UsersTable from "@/components/admin/user-table"
import AdminLayout from "@/components/admin/layout"
import { Users } from "lucide-react"

export default function UsersManagementPage() {
  return (
    <AdminLayout>
      <div className="flex flex-col gap-4">
        <div className="rounded-xl bg-gradient-to-r from-[#e6f7f3] via-[#f1f8f6] to-[#f6fefc] p-6 shadow flex items-center gap-4 border border-[#b2dfdb] mt-2">
          <div className="flex-shrink-0 flex items-center justify-center h-14 w-14 rounded-full bg-[#b2dfdb]">
            <Users className="h-8 w-8 text-[#008067]" strokeWidth={2.2} />
          </div>
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-[#008067] mb-1">
              จัดการผู้ใช้
            </h1>
            <p className="text-[#3a6356] text-base md:text-lg">
              จัดการผู้ใช้งานทั้งหมดในระบบ
            </p>
          </div>
        </div>

        <div className="rounded-lg border bg-white p-4 shadow-sm">
          <UsersTable />
        </div>
      </div>
    </AdminLayout>
  )
}

