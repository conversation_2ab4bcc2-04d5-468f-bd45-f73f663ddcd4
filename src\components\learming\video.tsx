"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import dynamic from "next/dynamic"
import UserActivityTracker from "@/components/useWindows"
import type ReactPlayerType from "react-player"
import { getQuizData, type QuizData, type Question } from "@/data/quizQuestion"
import { getQuizContentBySlug, submitQuiz, type submitRequest, type submitResponse } from "@/hook/displaycourseService"
import Swal from "sweetalert2"

// Dynamically import ReactPlayer for client-side rendering
const ReactPlayer = dynamic(() => import("react-player"), { ssr: false })

// เพิ่ม interface สำหรับ props
interface VideoWithQuestionProps {
  videoUrl: string,
  contentSlug?: string, // เพิ่ม contentSlug เพื่อใช้ในการดึงข้อมูลควิซ
  onComplete?: (result: { contentId: string; completed: boolean }) => void
}

const VideoWithQuestion: React.FC<VideoWithQuestionProps> = ({
  videoUrl,
  contentSlug,
  // questionTime = 30,
  // quizData,
  onComplete,
}) => {
  const [isQuestionOpen, setIsQuestionOpen] = useState(false)
  const [questionAsked, setQuestionAsked] = useState(false)
  const [playing, setPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(true) // Start muted for autoplay compliance
  const playerRef = useRef<ReactPlayerType | null>(null)
  const [videoDuration, setVideoDuration] = useState(0)
  const [videoProgress, setVideoProgress] = useState(0)
  const [videoCompleted, setVideoCompleted] = useState(false)
  const [hasQuiz, setHasQuiz] = useState(false)
  const [isReviewMode, setIsReviewMode] = useState(false) // เพิ่มสถานะสำหรับโหมดทบทวน
  const [showPlayButton, setShowPlayButton] = useState(true) // เพิ่มสถานะสำหรับแสดงปุ่มเล่น

  // ฟังก์ชันสำหรับสร้าง key ที่ใช้ใน localStorage โดยรวม userId
  // const getStorageKey = (key: string) => {
  //   return `${key}_${userId}`
  // }

  // เพิ่ม state สำหรับ quiz
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [selectedChoices, setSelectedChoices] = useState<string[]>([])
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [score, setScore] = useState(0)
  const [totalCorrect, setTotalCorrect] = useState(0)
  const [showResult, setShowResult] = useState(false)
  const [isPassed, setIsPassed] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [loadedQuizData, setLoadedQuizData] = useState<QuizData | null>(null)
  const [askedQuestions, setAskedQuestions] = useState<string[]>([]) // เก็บ ID ของคำถามที่ถามไปแล้ว
  const [QuizData, setQuizData] = useState<QuizData | null>(null)
  const [submittedAnswers, setSubmittedAnswers] = useState<Map<string, submitResponse>>(new Map()) // เก็บผลลัพธ์การส่งคำตอบ
  const [isSubmitting, setIsSubmitting] = useState(false) // สถานะการส่งคำตอบ

  // โหลดข้อมูลคำถามตาม courseId, lessonId และ contentId
  useEffect(() => {
    const loadQuizData = async () => {
      try {
        if (!contentSlug) {
          setHasQuiz(false)
          setLoadedQuizData(null)
          return
        }

        const response = await getQuizContentBySlug(contentSlug)
        
        if (response.success && response.data.quiz_questions.length > 0) {
          // แปลงข้อมูลจาก ContentQuizResponse เป็น QuizData
          const quizData: QuizData = {
            id: response.data.content_slug,
            name: response.data.content_name,
            description: response.data.content_description,
            passingScore: 100, // กำหนดคะแนนผ่านเป็น 100%
            questions: response.data.quiz_questions.map((question, index) => ({
              id: question.quiz_slug,
              title: question.quiz_title,
              content: question.quiz_details,
              imageUrl: question.quiz_image || undefined,
              videoTimestamp: question.quiz_time ? parseInt(question.quiz_time) : undefined,
              type: question.quiz_type,
              choices: question.quiz_choices.map((choice) => ({
                id: choice.choice_slug,
                content: choice.choice,
                imageUrl: choice.choice_image || undefined,
                isCorrect: choice.choice_type === 1, // assuming 1 means correct
                type: choice.choice_type.toString()
              }))
            })),
            questionCount: response.data.quiz_questions.length
          }

          setLoadedQuizData(quizData)
          setHasQuiz(true)
        } else {
          setHasQuiz(false)
          setLoadedQuizData(null)
        }
      } catch (error) {
        console.error("Error loading quiz data:", error)
        setHasQuiz(false)
        setLoadedQuizData(null)
      }
    }

    loadQuizData()
  }, [contentSlug])

  // รีเซ็ตข้อมูลเมื่อเปลี่ยน content
  useEffect(() => {
    setSubmittedAnswers(new Map())
    setTotalCorrect(0)
    setAskedQuestions([])
    setVideoCompleted(false)
    setShowResult(false)
    setShowPlayButton(true) // รีเซ็ตปุ่มเล่นเมื่อเปลี่ยนเนื้อหา
    setPlaying(false) // หยุดการเล่นเมื่อเปลี่ยนเนื้อหา
    setIsMuted(true) // รีเซ็ตการปิดเสียง
  }, [contentSlug])

  // เพิ่ม useEffect สำหรับตรวจสอบคำถามตามเวลาในวิดีโอ
  useEffect(() => {
    if (!loadedQuizData || !loadedQuizData.questions || loadedQuizData.questions.length === 0) return

    // เรียงลำดับคำถามตามเวลา
    const sortedQuestions = [...loadedQuizData.questions].filter((q) => q.videoTimestamp !== undefined).sort((a, b) => (a.videoTimestamp! - b.videoTimestamp!))

    if (currentQuestionIndex < sortedQuestions.length) {
      setCurrentQuestion(sortedQuestions[currentQuestionIndex])
    }
  }, [loadedQuizData, currentQuestionIndex])

  useEffect(() => {
    const setVh = () => {
      document.documentElement.style.setProperty("--vh", `${window.innerHeight * 0.01}px`)
    }
    setVh()
    window.addEventListener("resize", setVh)
    return () => window.removeEventListener("resize", setVh)
  }, [])

  useEffect(() => {
    if (isQuestionOpen) {
      document.body.classList.add("modal-open")
    } else {
      document.body.classList.remove("modal-open")
    }
  }, [isQuestionOpen])

  // เพิ่มฟังก์ชันสำหรับย้อนกลับไปยังเวลาที่กำหนด
  const seekToTime = (time: number) => {
    if (playerRef.current) {
      playerRef.current.seekTo(time, "seconds")
    }
  }

  // แก้ไขฟังก์ชัน handleSubmitAnswer ให้ใช้ API
  const handleSubmitAnswer = async () => {
    if (!currentQuestion || !loadedQuizData || isSubmitting) return

    setIsSubmitting(true)

    try {
      // เตรียมข้อมูลสำหรับส่ง API
      const submitData: submitRequest = {
        question_slug: currentQuestion.id,
        choice_slug: selectedChoices
      }

      // ส่งคำตอบไปยัง API
      const response = await submitQuiz(submitData)
      console.log("Quiz submission response:", response)
      
      // เก็บผลลัพธ์ใน state
      setSubmittedAnswers(prev => new Map(prev.set(currentQuestion.id, response)))

      if (response.is_correct) {
        setTotalCorrect((prev) => prev + 1)
        setIsSubmitted(true)

        // หน่วงเวลาก่อนปิด popup เมื่อตอบถูก
        setTimeout(() => {
          handleCloseQuestion()
        }, 2000)
      } else {
        // เมื่อตอบผิด
        setIsSubmitted(true)

        // หาคำถามก่อนหน้า
        const sortedQuestions = [...loadedQuizData.questions].sort((a, b) => (a.videoTimestamp ?? 0) - (b.videoTimestamp ?? 0))
        const currentIndex = sortedQuestions.findIndex((q) => q.id === currentQuestion.id)

        // กำหนดเวลาที่จะย้อนกลับไป
        let seekBackTime = 0 // เริ่มต้นที่ 0 สำหรับคำถามแรก

        if (currentIndex > 0) {
          // ถ้าไม่ใช่คำถามแรก ให้ย้อนกลับไปที่คำถามก่อนหน้า
          seekBackTime = sortedQuestions[currentIndex - 1].videoTimestamp ?? 0
        }

        // แสดงข้อความแจ้งเตือน
        setTimeout(() => {
          setIsQuestionOpen(false)
          resetQuestionState() // ใช้ฟังก์ชันรีเซ็ต

          // ลบคำถามนี้ออกจาก askedQuestions เพื่อให้ถามใหม่ได้
          setAskedQuestions((prev) => prev.filter((id) => id !== currentQuestion.id))

          // ย้อนกลับไปเล่นวิดีโอที่จุดเริ่มต้นของเนื้อหาช่วงนั้น
          seekToTime(seekBackTime)
          setPlaying(true)
        }, 2000)
      }
    } catch (error) {
      console.error("Error submitting answer:", error)
      // จัดการ error - แสดงข้อความแจ้งเตือน
      alert("เกิดข้อผิดพลาดในการส่งคำตอบ กรุณาลองใหม่")
    } finally {
      setIsSubmitting(false)
    }
  }

  // เพิ่มฟังก์ชันสำหรับจัดการเมื่อวิดีโอเล่นจบ
  const handleVideoEnded = () => {
    console.log("Video ended")

    // หยุดวิดีโอทันที
    setPlaying(false)

    // ตรวจสอบว่าต้องทำควิซหรือไม่
    if (!hasQuiz) {
      // ถ้าไม่มีควิซ ให้แสดงสถานะว่าวิดีโอเล่นจบแล้ว
      console.log("No quiz for this video, video ended - showing completion popup")
      onComplete?.({ contentId: contentSlug || "", completed: true }) // เรียก callback เมื่อวิดีโอจบ
      setVideoCompleted(true)
      setShowResult(true)
      setIsPassed(true)
    } else {
      // ถ้ามีควิซ ตรวจสอบว่าตอบครบทุกข้อหรือยัง
      const correctAnswers = Array.from(submittedAnswers.values()).filter(response => response.is_correct).length
      const allQuizCompleted = loadedQuizData && correctAnswers === loadedQuizData.questions.length

      if (allQuizCompleted) {
        // ถ้าตอบควิซครบทุกข้อแล้ว ให้แสดง popup
        console.log("Video ended and all quiz questions completed - showing completion popup")
        onComplete?.({ contentId: contentSlug || "", completed: true }) // เรียก callback เมื่อวิดีโอจบ
        setVideoCompleted(true)
        setShowResult(true)
        setIsPassed(true)
        Swal.fire({
          title: "วิดีโอจบแล้ว",
          text: "คุณตอบคำถามควิซครบทุกข้อแล้ว",
          icon: "success",
          confirmButtonText: "ตกลง"
        }).then(() => {
          // ปิด popup และรีเซ็ตสถานะ
          setShowResult(false)
          setPlaying(true) // ให้เล่นวิดีโอต่อ
        })
      } else {
        // ถ้ายังตอบควิซไม่ครบ ให้แสดงข้อความแจ้งเตือน
        console.log("Video ended but quiz not completed yet")
        alert("กรุณาตอบคำถามระหว่างวิดีโอให้ครบทุกข้อก่อนจบบทเรียน")
        // ย้อนกลับไปเล่นวิดีโอต่อ
        setPlaying(true)
      }
    }
  }

  // แก้ไขฟังก์ชัน handleProgress เพื่อให้ตรวจสอบคำถามที่ยังไม่ได้ตอบถูก:
  const handleProgress = (state: { playedSeconds: number; played: number; loaded: number; loadedSeconds: number }) => {
    setCurrentTime(state.playedSeconds)
    setVideoProgress(state.played)

    // ถ้าไม่มีข้อมูล quiz หรือไม่มีคำถาม หรืออยู่ในโหมดทบทวน
    if (!loadedQuizData || !loadedQuizData.questions || loadedQuizData.questions.length === 0 || isReviewMode) {
      return
    }

    // ตรวจสอบทุกคำถามว่าถึงเวลาแสดงหรือยัง
    loadedQuizData.questions.forEach((question, index) => {
      // ถ้าถึงเวลาแสดงคำถาม และยังไม่เคยแสดงคำถามนี้ และไม่มีคำถามอื่นกำลังแสดงอยู่
      const shouldShowQuestion =
        state.playedSeconds >= (question.videoTimestamp ?? 0) && !isQuestionOpen && !askedQuestions.includes(question.id)

      if (shouldShowQuestion) {
        setCurrentQuestionIndex(index)
        setCurrentQuestion(question)
        setIsQuestionOpen(true)
        setPlaying(false)
        resetQuestionState() // ใช้ฟังก์ชันรีเซ็ต
        setAskedQuestions((prev) => [...prev, question.id]) // เพิ่ม ID คำถามที่แสดงแล้ว
      }
    })
  }

  // ฟังก์ชันสำหรับรีเซ็ตสถานะเมื่อเริ่มคำถามใหม่
  const resetQuestionState = () => {
    setSelectedChoices([])
    setIsSubmitted(false)
    setIsSubmitting(false)
  }

  // เพิ่มฟังก์ชันสำหรับเริ่มคำถามใหม่
  const handleStartNewQuestion = (index: number) => {
    setCurrentQuestionIndex(index)
    setIsQuestionOpen(true)
    resetQuestionState()
  }

  // เพิ่มฟังก์ชันสำหรับข้ามคำถาม
  const handleSkipQuestion = () => {
    if (currentQuestionIndex < (loadedQuizData?.questions.length ?? 1) - 1) {
      // ถ้ายังไม่ใช่คำถามสุดท้าย
      handleStartNewQuestion(currentQuestionIndex + 1)
    } else {
      // ถ้าเป็นคำถามสุดท้ายแล้ว ให้ปิดคำถาม
      setIsQuestionOpen(false)
    }
  }

  // เพิ่มฟังก์ชันสำหรับย้อนกลับไปยังคำถามก่อนหน้า
  const handlePrevQuestion = () => {
    if (currentQuestionIndex > 0) {
      handleStartNewQuestion(currentQuestionIndex - 1)
    }
  }

  // เพิ่มฟังก์ชันสำหรับรีวิวคำถาม
  const handleReviewQuestion = () => {
    setIsReviewMode(true)
    setCurrentQuestionIndex(0)
    setIsQuestionOpen(true)
  }

  // เพิ่มฟังก์ชันสำหรับออกจากโหมดรีวิว
  const handleExitReview = () => {
    setIsReviewMode(false)
    setIsQuestionOpen(false)
    setSelectedChoices([])
    setIsSubmitted(false)
    setCurrentQuestion(null)
  }

  // เพิ่มฟังก์ชันสำหรับเริ่มต้นใหม่ทั้งหมด
  const handleRestart = () => {
    setIsQuestionOpen(false)
    setPlaying(false)
    setVideoCompleted(false)
    setHasQuiz(false)
    setIsReviewMode(false)
    setCurrentQuestion(null)
    setCurrentQuestionIndex(0)
    setSelectedChoices([])
    setIsSubmitted(false)
    setTotalCorrect(0)
    setShowResult(false)
    setSubmittedAnswers(new Map())
    setLoadedQuizData(null)
  }

  // เพิ่ม useEffect เพื่อตรวจสอบเมื่อตอบคำถามครบทุกข้อ (ใช้ข้อมูลจาก API)
  useEffect(() => {
    if (loadedQuizData && loadedQuizData.questions.length > 0) {
      // นับจำนวนคำถามที่ตอบถูกจาก API response
      const correctAnswers = Array.from(submittedAnswers.values()).filter(response => response.is_correct).length
      
      if (correctAnswers === loadedQuizData.questions.length && correctAnswers > 0) {
        console.log("All quiz questions answered correctly")
        
        // อัพเดทจำนวนคำถามที่ตอบถูก
        setTotalCorrect(correctAnswers)
        setIsPassed(true)
        
        // ไม่แสดง popup หรือ complete ทันที - ให้รอจนกว่าวิดีโอจะจบ
        // popup จะแสดงเฉพาะเมื่อวิดีโอจบจริงๆ ใน handleVideoEnded
        
        // ไม่ต้องหยุดวิดีโอ ให้เล่นต่อไปจนจบ
        // setPlaying(false)
        // setVideoCompleted(true)
        // setShowResult(true)
      }
    }
  }, [submittedAnswers, loadedQuizData])

  // แก้ไขฟังก์ชัน handleCloseQuestion ให้ไม่แสดงผลลัพธ์หลังตอบคำถาม
  const handleCloseQuestion = () => {
    setIsQuestionOpen(false)
    setPlaying(true)

    // ไม่แสดงผลลัพธ์หลังตอบคำถาม - ให้รอจนกว่าวิดีโอจะจบ
    // ผลลัพธ์จะแสดงเฉพาะเมื่อวิดีโอจบใน handleVideoEnded
  }

  const handleUnmute = () => {
    setIsMuted(false) // Set muted state to false
  }

  // เพิ่มฟังก์ชันสำหรับเลือกคำตอบ
  const toggleChoiceSelection = (choiceId: string) => {
    if (isSubmitted || isSubmitting) return // ไม่ให้เปลี่ยนคำตอบหลังจากส่งคำตอบแล้วหรือกำลังส่ง

    setSelectedChoices((prev) => {
      if (prev.includes(choiceId)) {
        return prev.filter((id) => id !== choiceId)
      } else {
        return [...prev, choiceId]
      }
    })
  }

  // เพิ่มฟังก์ชันสำหรับจัดการเมื่อวิดีโอโหลดเสร็จ
  const handleReady = () => {
    // ไม่ให้วิดีโอเล่นอัตโนมัติ - ให้ผู้ใช้คลิกเล่นเอง
    if (playerRef.current) {
      setVideoDuration(playerRef.current.getDuration())
    }
  }

  // เพิ่มฟังก์ชันสำหรับจัดการเมื่อผู้ใช้คลิกปุ่มเล่น
  const handlePlayClick = () => {
    setShowPlayButton(false)
    setPlaying(true)
    setIsMuted(false) // เปิดเสียงเมื่อผู้ใช้คลิกเล่น
  }

  return (
    <div className="h-screen">
      <div className="relative h-full w-full">
        <ReactPlayer
          ref={playerRef}
          url={videoUrl}
          playing={playing}
          controls={false} // ไม่ให้ผู้ใช้ควบคุมวิดีโอได้
          muted={isMuted} // Controlled mute state
          onProgress={handleProgress}
          onReady={handleReady}
          onEnded={handleVideoEnded}
          width="100%"
          height="100%"
          className="react-player"
          style={{ position: "absolute", top: 0, left: 0 }}
        />

        {/* ปุ่มเล่นวิดีโอเมื่อเริ่มต้น */}
        {showPlayButton && (
          <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-black bg-opacity-50 z-20">
            <button
              className="bg-white text-gray-800 p-6 rounded-full shadow-xl hover:bg-gray-100 transition-all transform hover:scale-105"
              onClick={handlePlayClick}
            >
              <svg
                className="w-12 h-12 ml-1"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M8 5v14l11-7z" />
              </svg>
            </button>
          </div>
        )}

        {/* แสดงสถานะการดูวิดีโอ */}
      </div>

      <UserActivityTracker 
        onPlay={(shouldPlay) => {
          // ถ้าผู้ใช้ยังไม่เคยคลิกปุ่มเล่น ไม่ให้เริ่มเล่นอัตโนมัติ
          if (showPlayButton && shouldPlay) {
            return;
          }
          setPlaying(shouldPlay);
        }} 
      />

      {/* แสดงปุ่มเปิดเสียงเมื่อวิดีโอกำลังเล่นแต่ยังปิดเสียงอยู่ */}
      {playing && isMuted && !showPlayButton && (
        <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-black bg-opacity-50 z-10">
          <button
            className="bg-white text-gray-800 px-6 py-3 rounded-lg font-medium shadow-lg hover:bg-gray-100 transition-colors"
            onClick={handleUnmute}
          >
            คลิกเพื่อเปิดเสียง
          </button>
        </div>
      )}

      {/* คำถามระหว่างวิดีโอ */}
      {isQuestionOpen && currentQuestion && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-70">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 overflow-hidden">
            {/* Header */}
            <div className="bg-[#008268] text-white p-4">
              <div className="flex justify-between items-center">
                <h3 className="font-bold text-lg">
                  คำถามที่ {currentQuestionIndex + 1} จาก {loadedQuizData?.questions.length}
                </h3>
                <div className="text-sm">
                  ตอบถูก {Array.from(submittedAnswers.values()).filter(response => response.is_correct).length} จาก {submittedAnswers.size} ข้อ
                </div>
              </div>
              {/* Progress bar */}
              <div className="w-full bg-white bg-opacity-30 h-2 mt-2 rounded-full overflow-hidden">
                <div
                  className="bg-white h-full rounded-full"
                  style={{ width: `${((currentQuestionIndex + 1) / (loadedQuizData?.questions.length || 1)) * 100}%` }}
                ></div>
              </div>
            </div>

            {/* Question content */}
            <div className="p-6">
              <h4 className="text-xl font-medium text-gray-800 mb-4">{currentQuestion.title}</h4>
              {currentQuestion.content && <p className="text-gray-600 mb-4">{currentQuestion.content}</p>}
              {currentQuestion.imageUrl && (
                <div className="mb-4 flex justify-center">
                  <img
                    src={currentQuestion.imageUrl || "/placeholder.svg"}
                    alt="Question"
                    className="max-h-48 object-contain rounded-lg"
                  />
                </div>
              )}

              {/* Choices */}
              <div className="space-y-3 mt-6">
                {currentQuestion.choices.map((choice) => {
                  // ดึงผลลัพธ์จาก API สำหรับคำถามนี้
                  const apiResult = submittedAnswers.get(currentQuestion.id)
                  const isCorrectChoice = apiResult?.correct_slugs.includes(choice.id) || false
                  const isSelectedChoice = selectedChoices.includes(choice.id)
                  
                  return (
                    <div
                      key={choice.id}
                      onClick={() => toggleChoiceSelection(choice.id)}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        selectedChoices.includes(choice.id)
                          ? "border-[#008268] bg-[#e6f7f4]"
                          : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                      } ${
                        isSubmitted && isCorrectChoice
                          ? "border-green-500 bg-green-50"
                          : isSubmitted && isSelectedChoice && !isCorrectChoice
                          ? "border-red-500 bg-red-50"
                          : ""
                      }`}
                    >
                      <div className="flex items-center">
                        <div
                          className={`w-6 h-6 flex-shrink-0 rounded-full border ${
                            selectedChoices.includes(choice.id) ? "border-[#008268] bg-[#008268]" : "border-gray-300"
                          } flex items-center justify-center mr-3`}
                        >
                          {selectedChoices.includes(choice.id) && (
                            <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                        </div>
                        <div className="flex-1">
                          {choice.imageUrl ? (
                            <div className="flex flex-col">
                              <img
                                src={choice.imageUrl || "/placeholder.svg"}
                                alt={choice.content || ""}
                                className="h-20 object-contain mb-2"
                              />
                              {choice.content && <span className="text-sm">{choice.content}</span>}
                            </div>
                          ) : (
                            <span>{choice.content || ""}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 p-4 flex justify-between items-center border-t border-gray-200">
              {isSubmitted ? (
                <div
                  className={`text-lg font-medium ${
                    submittedAnswers.get(currentQuestion.id)?.is_correct
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {submittedAnswers.get(currentQuestion.id)?.is_correct
                    ? "✓ ถูกต้อง!"
                    : "✗ ไม่ถูกต้อง"}
                </div>
              ) : (
                <div></div>
              )}

              {!isSubmitted ? (
                <button
                  onClick={handleSubmitAnswer}
                  disabled={selectedChoices.length === 0 || isSubmitting}
                  className={`px-6 py-2 rounded-md font-medium ${
                    selectedChoices.length > 0 && !isSubmitting
                      ? "bg-[#008268] text-white hover:bg-[#006e58]"
                      : "bg-gray-200 text-gray-500 cursor-not-allowed"
                  }`}
                >
                  {isSubmitting ? "กำลังส่ง..." : "ส่งคำตอบ"}
                </button>
              ) : (
                <div className="text-sm text-gray-500">กำลังไปยังคำถามถัดไป...</div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* แสดงผลลัพธ์เมื่อตอบคำถามครบทุกข้อหรือดูวิดีโอจบ
      {showResult && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-70">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4 overflow-hidden">
            <div className="p-6 text-center bg-[#E6F7F4]">
              <div className="w-20 h-20 rounded-full mx-auto flex items-center justify-center bg-[#d1efe9] text-[#008268]">
                <svg className="w-12 h-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold mt-4 text-[#008268]">วิดีโอเล่นจบแล้ว!</h3>
              {hasQuiz ? (
                <p className="text-gray-600 mt-2">คุณตอบคำถามระหว่างบทเรียนครบทุกข้อแล้ว<br />กดปุ่ม "ฉันเสร็จแล้ว" เพื่อบันทึกความคืบหน้า</p>
              ) : (
                <p className="text-gray-600 mt-2">คุณได้ดูวิดีโอจบแล้ว<br />กดปุ่ม "ฉันเสร็จแล้ว" เพื่อบันทึกความคืบหน้า</p>
              )}
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex justify-center space-x-3">
                <button
                  onClick={() => {
                    setShowResult(false)
                    setPlaying(true)
                  }}
                  className="px-6 py-2 bg-gray-500 text-white rounded-md font-medium hover:bg-gray-600"
                >
                  ดูต่อ
                </button>
                <button
                  onClick={() => {
                    // เรียก onComplete เพื่อให้ parent component จัดการการ complete
                    if (onComplete && contentSlug) {
                      console.log("User clicked complete button in video popup")
                      onComplete({ contentId: contentSlug, completed: true })
                    }
                    setShowResult(false)
                  }}
                  className="px-6 py-2 bg-[#008268] text-white rounded-md font-medium hover:bg-[#006e58]"
                >
                  ฉันเสร็จแล้ว
                </button>
              </div>
            </div>
          </div>
        </div>
      )} */}
    </div>
  )
}

export default VideoWithQuestion
