import { api } from "@/lib/api";

// Fetch all assigned student-path data
export async function fetchAllAssignedStudentPaths() {
  try {
    const response = await api.get<any>("/assign-path"); // Adjust endpoint if needed
    return response.data;
  } catch (error) {
    console.error("Failed to fetch assigned student paths:", error);
    throw error;
  }
}

// Remove assigned path for a student
export async function removeAssignedStudentPath(
  pathSlug: string,
  userId: string
) {
  try {
    const response = await api.delete<any>(
      `/assigned-paths/${pathSlug}/${userId}`
    );
    return response.data;
  } catch (error) {
    console.error("Failed to remove assigned student path:", error);
    throw error;
  }
}

export async function removeAssignedStudentPathById(
  userSlug: string
) {
  try {
    const response = await api.delete<any>(
      `/assign-path/${userSlug}`
    );
    return response.data;
  } catch (error) {
    console.error("Failed to remove assigned student path by ID:", error);
    throw error;
  }
}

// Fetch assigned path data for a specific user by user_slug
export async function fetchAssignedStudentPathBySlug(user_slug: string) {
  try {
    const response = await api.get<any>(`/assign-path/${user_slug}`); // Adjust endpoint if needed
    return response.data;
  } catch (error) {
    console.error("Failed to fetch assigned path by user_slug:", error);
    throw error;
  }
}

// Update assigned paths for a user (edit assigned paths)
export async function updateAssignedStudentPaths(
  user_slug: string,
  assigned_paths: { path_id: number }[]
) {
  try {
    const response = await api.put<any>(
      `/assign-path/${user_slug}`,
      { assigned_paths }
    );
    return response.data;
  } catch (error) {
    console.error("Failed to update assigned student paths:", error);
    throw error;
  }
}

// Create assigned paths for a user (POST)
export async function createAssignedStudentPaths(
  user_slug: string,
  assign: { 
    path_id: number,
    learning_type: boolean,
  }[]
) {
  try {
    const response = await api.post<any>(
      `/assign-path`,
      { user_slug, assign }
    );
    return response.data;
  } catch (error) {
    console.error("Failed to create assigned student paths:", error);
    throw error;
  }
}

