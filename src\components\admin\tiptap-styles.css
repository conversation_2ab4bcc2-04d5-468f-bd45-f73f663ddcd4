/* Tiptap Editor Styles */
.tiptap-editor .ProseMirror {
  outline: none;
}

.tiptap-editor .is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* Table styles */
.tiptap-editor table {
  border-collapse: collapse;
  margin: 0;
  overflow: hidden;
  table-layout: fixed;
  width: 100%;
}

.tiptap-editor table td,
.tiptap-editor table th {
  border: 1px solid #ced4da;
  box-sizing: border-box;
  min-width: 1em;
  padding: 3px 5px;
  position: relative;
  vertical-align: top;
}

.tiptap-editor table td > *,
.tiptap-editor table th > * {
  margin-bottom: 0;
}

.tiptap-editor table th {
  background-color: #f8f9fa;
  font-weight: bold;
  text-align: left;
}

.tiptap-editor table .selectedCell:after {
  background: rgba(200, 200, 255, 0.4);
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
}

.tiptap-editor table .column-resize-handle {
  background-color: #adf;
  bottom: -2px;
  position: absolute;
  right: -2px;
  pointer-events: none;
  top: 0;
  width: 4px;
}

.tiptap-editor table p {
  margin: 0;
}

/* Image resize styles */
.tiptap-editor img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
}

.tiptap-editor .image-resizer {
  border: 2px solid #3b82f6;
  border-radius: 4px;
  position: relative;
}

.tiptap-editor .image-resizer .resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border: 1px solid #fff;
  border-radius: 50%;
}

.tiptap-editor .image-resizer .resize-handle.nw {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.tiptap-editor .image-resizer .resize-handle.ne {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.tiptap-editor .image-resizer .resize-handle.sw {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.tiptap-editor .image-resizer .resize-handle.se {
  bottom: -4px;
  right: -4px;
  cursor: se-resize;
}

/* Link styles */
.tiptap-editor a {
  color: #3b82f6;
  text-decoration: underline;
}

.tiptap-editor a:hover {
  color: #1d4ed8;
}

/* Highlight styles */
.tiptap-editor mark {
  background-color: #fef08a;
  border-radius: 2px;
  padding: 0.1em 0.2em;
}

/* Blockquote styles */
.tiptap-editor blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #6b7280;
  font-style: italic;
}

/* List styles */
.tiptap-editor ul,
.tiptap-editor ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.tiptap-editor li {
  margin: 0.25rem 0;
}

/* Heading styles */
.tiptap-editor h1, .tiptap-editor .ProseMirror h1 {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
  margin: 1.5rem 0 1rem 0;
  color: #1f2937;
}

.tiptap-editor h2, .tiptap-editor .ProseMirror h2 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
  margin: 1.25rem 0 0.875rem 0;
  color: #374151;
}

.tiptap-editor h3, .tiptap-editor .ProseMirror h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  margin: 1rem 0 0.75rem 0;
  color: #4b5563;
}

.tiptap-editor h4, .tiptap-editor .ProseMirror h4 {
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.4;
  margin: 0.875rem 0 0.625rem 0;
  color: #6b7280;
}

.tiptap-editor h5, .tiptap-editor .ProseMirror h5 {
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 1.4;
  margin: 0.75rem 0 0.5rem 0;
  color: #6b7280;
}

.tiptap-editor h6, .tiptap-editor .ProseMirror h6 {
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.4;
  margin: 0.625rem 0 0.5rem 0;
  color: #9ca3af;
}

/* Paragraphs */
.tiptap-editor p {
  margin: 0.75rem 0;
  line-height: 1.6;
  color: #374151;
}

/* Lists */
.tiptap-editor .tiptap-bullet-list,
.tiptap-editor .tiptap-ordered-list {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.tiptap-editor .tiptap-bullet-list {
  list-style-type: disc;
}

.tiptap-editor .tiptap-ordered-list {
  list-style-type: decimal;
}

.tiptap-editor .tiptap-list-item {
  margin: 0.25rem 0;
  line-height: 1.6;
}

.tiptap-editor .tiptap-list-item p {
  margin: 0;
}

/* Nested lists */
.tiptap-editor .tiptap-bullet-list .tiptap-bullet-list {
  list-style-type: circle;
  margin: 0.25rem 0;
}

.tiptap-editor .tiptap-bullet-list .tiptap-bullet-list .tiptap-bullet-list {
  list-style-type: square;
}

/* Blockquotes */
.tiptap-editor .tiptap-blockquote {
  border-left: 4px solid #e5e7eb;
  margin: 1.5rem 0;
  padding: 1rem 1.5rem;
  background-color: #f9fafb;
  font-style: italic;
  color: #6b7280;
  border-radius: 0 0.375rem 0.375rem 0;
}

.tiptap-editor .tiptap-blockquote p {
  margin: 0.5rem 0;
}

.tiptap-editor .tiptap-blockquote p:first-child {
  margin-top: 0;
}

.tiptap-editor .tiptap-blockquote p:last-child {
  margin-bottom: 0;
}

/* Text formatting */
.tiptap-editor strong {
  font-weight: 600;
}

.tiptap-editor em {
  font-style: italic;
}

.tiptap-editor u {
  text-decoration: underline;
}

.tiptap-editor s {
  text-decoration: line-through;
}

/* Links */
.tiptap-editor a {
  color: #2563eb;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s ease;
}

.tiptap-editor a:hover {
  color: #1d4ed8;
}

/* Highlighting */
.tiptap-editor mark {
  background-color: #fef08a;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Horizontal rule */
.tiptap-editor hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 2rem 0;
}

/* Text alignment */
.tiptap-editor [style*="text-align: left"] {
  text-align: left;
}

.tiptap-editor [style*="text-align: center"] {
  text-align: center;
}

.tiptap-editor [style*="text-align: right"] {
  text-align: right;
}

/* Focus styles */
.tiptap-editor .ProseMirror:focus {
  outline: none;
}

/* Selection styles */
.tiptap-editor .ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Placeholder for empty editor */
.tiptap-editor .is-empty::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}
