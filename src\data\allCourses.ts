import { CourseType } from "@/types/courses";

// This function returns an array of available courses with detailed information
export const getCoursesData: CourseType[] = [
  {
    id: "WnH5F0qZF938",
    name: "โรคปอดอักเสพ (Pneumonia)",
    description: "คอร์สนี้ออกแบบมาเพื่อเสริมสร้างความรู้และทักษะด้านโรคปอดอักเสบสำหรับแพทย์ พยาบาล และบุคลากรทางการแพทย์ โดยเนื้อหาครอบคลุมตั้งแต่พยาธิสภาพของโรค สาเหตุที่พบบ่อยในแต่ละกลุ่มอายุ การประเมินอาการเบื้องต้น การวินิจฉัยโดยใช้ภาพรังสีและการตรวจทางห้องปฏิบัติการ รวมถึงแนวทางการรักษาและการเลือกใช้ยาปฏิชีวนะอย่างเหมาะสมตามแนวทางเวชปฏิบัติล่าสุด พร้อมกรณีศึกษาเพื่อฝึกวิเคราะห์และตัดสินใจทางคลินิกอย่างเป็นระบบ",
    certify: true,

    teacher: {
      name: "อาจารย์สมชาย",
      avatar: "https://imgcdn.stablediffusionweb.com/2024/9/30/9cde6098-e149-4ee3-9bd7-c671fc585565.jpg", // ใช้ path ที่เก็บใน public
      description: "ผู้เชี่ยวชาญในการดูแลผู้ป่วย ICU และผู้ป่วยวิกฤต",
    },
    coverImage: "/e-med/img/courses05.jpg", // รูปปกคอร์ส (เก็บใน public)
    time: 10800, // 3 ชั่วโมง
    level: "ยาก",

    flexibility: {
      duration: "ประมาณ 3 ชั่วโมง",
      description: "เรียนผ่านวิดีโอคอร์ส สามารถเรียนได้ตามสะดวก",
    },
    lesson: [
      {
        id: "627BieXQX84E",
        name: "รู้จักกับโรคปอดอักเสบ",
        description: "เรียนรู้เกี่ยวกับโรคปอดอักเสพ",
        time: 2700, // 45 นาที
        content: [
          {
            id: "iL79E9pow1kh",
            name: "ไขข้อสงสัยโณคปอดอักเสบ",
            typecontent: "video",
            details: "https://www.youtube.com/watch?v=9fogHcVuUcA",
            time: 1800, // 30 นาที
          }
        ],
      },
      {
        id: "yjLvq2n9296q",
        name: "วิธีป้องกันและรักษาโรคปอดอักเสบ",
        description: "เรียนรู้วิธีการป้องกันและรักษาโรคปอดอักเสบเบื้องต้น",
        time: 5400, // 1 ชั่วโมง 30 นาที
        content: [
          {
            id: "8l25jy3S82Xz",
            name: "ป้องกันโรคปอดอักเสบด้วยวัคซีน PCV และ PPSV",
            typecontent: "video",
            details: "https://www.youtube.com/watch?v=aKWIMKpVw4Q",
            time: 2700, // 45 นาที
          },
          {
            id: "1hmwg2xDM3Y7",
            name: "การดูแลผู้ป่วยโรคปอดอักเสบ",
            typecontent: "pdf",
            details: "/doc-react.pdf",
            time: 2700, // 45 นาที
          },
        ],
      },
    ],

    status: "published",

    instruction: "คอร์สนี้เหมาะสำหรับแพทย์ พยาบาล และบุคลากรทางการแพทย์ที่ต้องการเสริมสร้างความรู้และทักษะด้านโรคปอดอักเสบ ก",
    title: undefined,
    difficulty: ""
  },


    {
      id: "QlH3I647MCuN",
      name: "โรคซึมเศร้า (Depression)",
      description: "บทเรียนออนไลน์นี้จะช่วยให้คุณเข้าใจเกี่ยวกับโรคซึมเศร้าอย่างถ่องแท้ ตั้งแต่สาเหตุ อาการ วิธีวินิจฉัย การดูแลรักษา รวมถึงวิธีดูแลตนเองและคนรอบข้างที่มีภาวะซึมเศร้า เหมาะสำหรับบุคคลทั่วไป บุคลากรทางการแพทย์ และผู้ที่ต้องการเสริมสร้างความรู้ด้านสุขภาพจิต",
      certify: true,
      instruction: "คอร์สนี้เหมาะสำหรับแพทย์ พยาบาล และบุคลากรทางการแพทย์ที่ต้องการเสริมสร้างความรู้และทักษะด้านโรคปอดอักเสบ",
      teacher: {
        name: "อาจารย์พิมพ์ลักษณ์",
        avatar: "https://imgcdn.stablediffusionweb.com/2024/9/30/9cde6098-e149-4ee3-9bd7-c671fc585565.jpg", // ใช้ path ที่เก็บใน public
        description: "ผู้เชี่ยวชาญด้านการดูแลผู้ป่วยและการบริหารยาในโรงพยาบาล",
      },
      coverImage: "/e-med/img/courses06.jpg", // รูปปกคอร์ส (เก็บใน public)
      time: 10800, // 3 ชั่วโมง
      level: "ปานกลาง",
      flexibility: {
        duration: "ประมาณ 3 ชั่วโมง",
        description: "สามารถเรียนตามสะดวกเวลา เรียนจบภายใน 3 ชั่วโมง",
      },
      lesson: [
        {
          id: "914VEz100Wjs",
          name: "ความรู้เกี่ยวกับโรคซึมเศร้า (Depression)",
          description: "เรียนรู้วิธีการตรวจสอบยาก่อนให้แก่ผู้ป่วย",
          time: 2700, // 45 นาที
          content: [
            {
              id: "EP9sCqtUAe85",
              name: "ความรู้เกี่ยวกับโรคซึมเศร้า",
              typecontent: "video",
              details: "https://www.youtube.com/watch?v=X-eTBRQzjyo", // URL ของวิดีโอ
              time: 1350, // 22 นาที 30 วินาที
            },
            {
              id: "3MFK6k2rhsr9",
              name: "เอกสารความรู้เกี่ยวกับโรคซึมเศร้า",
              typecontent: "pdf",
              details: "/doc-react.pdf",
              time: 1350, // 22 นาที 30 วินาที
            },
          ],
        },
        {
          id: "c4mT32O7RboV",
          name: "ภาวะซึมเศร้า โรคทางใจ หรือาการผิดปกติทางอารมณ์",
          description: "เทคนิคและวิธีการให้ยาแก่ผู้ป่วยทางช่องปากและอื่นๆ",
          time: 5400, // 1 ชั่วโมง 30 นาที
          content: [
            {
              id: "2xmX4V5OKG3l",
              name: "เรียนรู้เกี่ยวกับภาวะซึมเศร้าและโรคทางใจ",
              typecontent: "video",
              details: "https://www.youtube.com/watch?v=NnCegGJVgUQ", // URL ของวิดีโอ
              time: 2700, // 45 นาที
            },
          ],
        },
      ],
      status: "published",
      title: undefined,
      difficulty: ""
    },

  {
    id: "19x4uNv4RgED",
    name: "ไขข้อสงสัยโรคอ้วนในเด็ก",
    description: "รายการ Q&A ช่วยไขข้อสงสัยเกี่ยวกับโรคอ้วนในเด็กจากโรงพยาบาลสินแพทย์ ศรีนครินทร์",
    instruction: "คอร์สนี้เหมาะสำหรับแพทย์ พยาบาล และบุคลากรทางการแพทย์ที่ต้องการเสริมสร้างความรู้และทักษะด้านโรคปอดอักเสบ",
    certify: false,
    teacher: {
      name: "หมอสมชาย",
      avatar: "/e-med/img/courses04.jpg", // ใช้ path ที่เก็บใน public
      description: "ผู้เชี่ยวชาญด้านโรคเด็กและการดูแลสุขภาพ",
    },
    coverImage: "/e-med/img/courses04.jpg", // รูปปกคอร์ส (เก็บใน public)
    time: 7200, // 2 ชั่วโมง
    level: "เบื้องต้น",

    flexibility: {
      duration: "ประมาณ 2 ชั่วโมง",
      description: "เรียนรู้ได้ตามสะดวกและเข้าใจง่าย",
    },
    lesson: [
      {
        id: "FKjXR0m26Dt7",
        name: "โรคอ้วนในเด็กคืออะไร?",
        description: "เรียนรู้เกี่ยวกับโรคอ้วนในเด็กและสาเหตุที่ทำให้เกิดโรคนี้",
        time: 3600, // 1 ชั่วโมง
        content: [
          {
            id: "WK4x4Ybuaw0v",
            name: "สาเหตุของโรคอ้วนในเด็ก",
            typecontent: "video",
            details: "https://www.youtube.com/watch?v=6tDXyOZ_nxs", // URL ของวิดีโอ
            time: 1800, // 30 นาที
          },
          {
            id: "KJ400l5e5Ses",
            name: "วิธีการป้องกันโรคอ้วนในเด็ก",
            typecontent: "pdf",
            details: "/doc-react.pdf",
            time: 1800, // 30 นาที
          },
        ],
      },
      {
        id: "17E9Wq6pJLKT",
        name: "การรักษาโรคอ้วนในเด็ก",
        description: "แนวทางการรักษาและการจัดการกับโรคอ้วนในเด็กอย่างมีประสิทธิภาพ",
        time: 3600, // 1 ชั่วโมง
        content: [
          {
            id: "WQ8z221MLvvf",
            name: "วิธีการรักษาโรคอ้วน",
            typecontent: "video",
            details: "https://www.youtube.com/watch?v=6tDXyOZ_nxs", // URL ของวิดีโอ
            time: 1800, // 30 นาที
          },
          {
            id: "LLjBD86vP4dE",
            name: "การดูแลจากแพทย์",
            typecontent: "pdf",
            details: "/doc-react.pdf",
            time: 1800, // 30 นาที
          },
        ],
      },
    ],
    status: "",
    title: undefined,
    difficulty: ""
  }
  

  
];
