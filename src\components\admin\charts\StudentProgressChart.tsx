"use client"

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js"
import { Line } from "react-chartjs-2"

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

export default function StudentProgressChart() {
  // Mock data showing student progress over time
  const data = {
    labels: [
      "สัปดาห์ 1", "สัปดาห์ 2", "สัปดาห์ 3", "สัปดาห์ 4", 
      "สัปดาห์ 5", "สัปดาห์ 6", "สัปดาห์ 7", "สัปดาห์ 8"
    ],
    datasets: [
      {
        label: "ความคืบหน้าเฉลี่ย",
        data: [12, 25, 38, 52, 64, 73, 81, 87],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        fill: true,
        tension: 0.4,
      },
      {
        label: "เป้าหมาย",
        data: [15, 30, 45, 60, 75, 85, 90, 95],
        borderColor: "rgb(16, 185, 129)",
        backgroundColor: "rgba(16, 185, 129, 0.1)",
        fill: false,
        borderDash: [5, 5],
        tension: 0.4,
      },
    ],
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          font: {
            family: "Inter, sans-serif",
          },
        },
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "white",
        bodyColor: "white",
        borderColor: "rgba(59, 130, 246, 0.5)",
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.parsed.y}%`
          }
        }
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            family: "Inter, sans-serif",
          },
        },
      },
      y: {
        beginAtZero: true,
        max: 100,
        grid: {
          color: "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          font: {
            family: "Inter, sans-serif",
          },
          callback: function(value: any) {
            return value + "%"
          }
        },
      },
    },
    interaction: {
      intersect: false,
      mode: "index" as const,
    },
  }

  // Progress distribution data
  const progressDistribution = [
    { range: "0-25%", count: 156, color: "bg-red-500" },
    { range: "26-50%", count: 234, color: "bg-yellow-500" },
    { range: "51-75%", count: 345, color: "bg-blue-500" },
    { range: "76-100%", count: 510, color: "bg-green-500" },
  ]

  const totalStudents = progressDistribution.reduce((sum, item) => sum + item.count, 0)

  return (
    <div className="w-full">
      {/* Chart */}
      <div className="h-64 mb-6">
        <Line data={data} options={options} />
      </div>

      {/* Progress Distribution */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-700 mb-3">การกระจายความคืบหน้า</h4>
        <div className="space-y-2">
          {progressDistribution.map((item, index) => {
            const percentage = ((item.count / totalStudents) * 100).toFixed(1)
            return (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-4 h-4 rounded ${item.color} mr-2`} />
                  <span className="text-sm text-gray-700">{item.range}</span>
                </div>
                <div className="text-right">
                  <span className="text-sm font-medium text-gray-800">{item.count} คน</span>
                  <span className="text-xs text-gray-500 ml-2">({percentage}%)</span>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
        <div className="text-center">
          <p className="text-sm text-gray-500">ความคืบหน้าเฉลี่ย</p>
          <p className="text-lg font-semibold text-gray-800">67%</p>
          <p className="text-xs text-green-600">+5.2% จากสัปดาห์ที่แล้ว</p>
        </div>
        <div className="text-center">
          <p className="text-sm text-gray-500">นักเรียนที่เสร็จสิ้น</p>
          <p className="text-lg font-semibold text-gray-800">510 คน</p>
          <p className="text-xs text-green-600">+12.8% จากสัปดาห์ที่แล้ว</p>
        </div>
      </div>

      {/* Performance Insights */}
      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-blue-800 mb-2">ข้อมูลเชิงลึก</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 41% ของนักเรียนมีความคืบหน้าตามเป้าหมาย</li>
          <li>• อัตราการเสร็จสิ้นเพิ่มขึ้น 12.8% ในสัปดาห์นี้</li>
          <li>• นักเรียนส่วนใหญ่อยู่ในช่วง 51-100% ของคอร์ส</li>
        </ul>
      </div>
    </div>
  )
}
