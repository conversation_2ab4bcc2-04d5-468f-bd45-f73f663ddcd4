'use client';

import React, { Suspense, useRef, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Environment, Html, useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface GLBModelProps {
  modelPath: string;
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: [number, number, number];
  autoRotate?: boolean;
  enableControls?: boolean;
  className?: string;
  height?: string;
}

// Loading component
const Loader = () => (
  <Html center>
    <div className="flex items-center justify-center">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white opacity-80"></div>
    </div>
  </Html>
);

// Error component
const ErrorComponent = () => (
  <Html center>
    <div className="text-white text-xs opacity-80 text-center">
      <div>Model Error</div>
      <div className="text-xs mt-1">Check console</div>
    </div>
  </Html>
);

// GLB Model component
const GLBModelComponent: React.FC<{
  modelPath: string;
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: [number, number, number];
  autoRotate?: boolean;
}> = ({ 
  modelPath, 
  position = [0, 0, 0], 
  rotation = [0, 0, 0], 
  scale = [1, 1, 1],
  autoRotate = false
}) => {
  const { scene } = useGLTF(modelPath);
  const modelRef = useRef<THREE.Group>(null);
  const [isReady, setIsReady] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);

  useEffect(() => {
    try {
      if (scene) {
        // Don't clone the scene, use it directly
        scene.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.castShadow = true;
            child.receiveShadow = true;
            
            // Fix material issues
            if (child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach((mat) => {
                  mat.needsUpdate = true;
                  if (mat instanceof THREE.MeshStandardMaterial) {
                    mat.envMapIntensity = 0.5;
                  }
                });
              } else {
                child.material.needsUpdate = true;
                if (child.material instanceof THREE.MeshStandardMaterial) {
                  child.material.envMapIntensity = 0.5;
                }
              }
            }
          }
        });
        setIsReady(true);
        setHasError(false);
      }
    } catch (error) {
      console.error('GLB Model Error:', error);
      setHasError(true);
    }
  }, [scene]);

  useFrame(() => {
    if (modelRef.current && autoRotate && isReady) {
      modelRef.current.rotation.y += 0.01;
    }
  });

  if (hasError) {
    console.error('GLB Model Error');
    return <ErrorComponent />;
  }

  if (!scene || !isReady) {
    return <Loader />;
  }

  return (
    <group
      ref={modelRef}
      position={position}
      rotation={rotation}
      scale={scale}
    >
      <primitive object={scene} dispose={null} />
    </group>
  );
};

// Main GLB Model component
const GLBModel: React.FC<GLBModelProps> = ({
  modelPath,
  position = [0, 0, 0],
  rotation = [0, 0, 0],
  scale = [1, 1, 1],
  autoRotate = true,
  enableControls = false,
  className = '',
  height = '100%'
}) => {
  return (
    <div className={`w-full h-full ${className}`} style={{ height }}>
      <Canvas
        shadows
        camera={{
          position: [0, 1, 3],
          fov: 50,
          near: 0.1,
          far: 1000
        }}
        gl={{ 
          antialias: true, 
          alpha: true,
          preserveDrawingBuffer: true,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]}
        frameloop="demand"
      >
        {/* Lighting setup */}
        <ambientLight intensity={0.8} />
        <directionalLight
          position={[5, 5, 5]}
          intensity={1}
          castShadow
          shadow-mapSize-width={512}
          shadow-mapSize-height={512}
          shadow-camera-near={0.1}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />
        <pointLight position={[-3, 3, 3]} intensity={0.4} />

        {/* Environment for better lighting */}
        <Environment preset="apartment" />

        {/* GLB Model */}
        <Suspense fallback={<Loader />}>
          <GLBModelComponent
            modelPath={modelPath}
            position={position}
            rotation={rotation}
            scale={scale}
            autoRotate={autoRotate}
          />
        </Suspense>

        {/* Controls */}
        {enableControls && (
          <OrbitControls
            enablePan={false}
            enableZoom={false}
            enableRotate={true}
            autoRotate={autoRotate}
            autoRotateSpeed={1}
          />
        )}
      </Canvas>
    </div>
  );
};

// Preload the model for better performance
useGLTF.preload('/e-med/models/SP.glb');

export default GLBModel;
