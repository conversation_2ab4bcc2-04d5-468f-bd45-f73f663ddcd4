import { api } from "@/lib/api";

// Types for authentication API requests and responses
export interface LoginRequest {
    email: string;
    password: string;
}

export interface LoginResponse {
    token: string;
}

export interface SignupRequest {
    username: string;
    password: string;
    user_fname: string;
    user_lname: string;
    user_email: string;
}

export interface SignupResponse {
    message: string;
    user?: {
        id: string;
        username: string;
        email: string;
    };
}

export interface UserMeResponse {
    id: number;
    username: string;
    user_fname: string;
    user_lname: string;
    user_email: string;
    user_position: string;
    user_picture: string;
    role: string;
}

export interface RequestOTPRequest {
    email: string;
}

export interface RequestOTPResponse {
    message: string;
    success: boolean;
}

export interface ResetPasswordRequest {
    email: string;
    otp: string;
    password: string;
}

export interface ResetPasswordResponse {
    message: string;
    success: boolean;
}

// Authentication API functions
export const authAPI = {
    /**
     * Login user with username and password
     * POST /auth/login
     */
    async login(credentials: LoginRequest): Promise<LoginResponse> {
        try {
            const response = await api.post<LoginResponse>('/auth/login', credentials);
            return response;
        } catch (error) {
            console.error('Login failed:', error);
            throw new Error(error instanceof Error ? error.message : 'Login failed');
        }
    },

    /**
     * Get current user information
     * GET /auth/me
     */
    async getCurrentUser(): Promise<UserMeResponse> {
        try {
            const response = await api.get<UserMeResponse>('/auth/me');
            return response;
        } catch (error) {
            console.error('Failed to get current user:', error);
            throw new Error(error instanceof Error ? error.message : 'Failed to get current user');
        }
    },

    /**
     * Request OTP for password reset
     * POST /auth/request-otp
     */
    async requestOTP(requestData: RequestOTPRequest): Promise<RequestOTPResponse> {
        try {
            const response = await api.post<RequestOTPResponse>('/auth/request-otp', requestData);
            return response;
        } catch (error) {
            console.error('Failed to request OTP:', error);
            throw new Error(error instanceof Error ? error.message : 'Failed to request OTP');
        }
    },

    /**
     * Verify OTP for password reset
     * POST /auth/verify-otp
     */
    async verifyOTP({email, otp}: {email: string, otp: string}): Promise<ResetPasswordResponse> {
        try {
            const response = await api.post<ResetPasswordResponse>('/auth/verify-otp', { email, otp });
            return response;
        } catch (error) {
            console.error('Failed to verify OTP:', error);
            throw new Error(error instanceof Error ? error.message : 'Failed to verify OTP');
        }
    },

    /**
     * Reset password using OTP
     * POST /auth/reset-password
     */
    async resetPassword(resetData: ResetPasswordRequest): Promise<ResetPasswordResponse> {
        try {
            const response = await api.post<ResetPasswordResponse>('/auth/reset-password', resetData);
            return response;
        } catch (error) {
            console.error('Failed to reset password:', error);
            throw new Error(error instanceof Error ? error.message : 'Failed to reset password');
        }
    },

    /**
     * Sign up new user
     * POST /auth/signup
     */
    async signup(userData: SignupRequest): Promise<SignupResponse> {
        try {
            const response = await api.post<SignupResponse>('/auth/signup', userData);
            return response;
        } catch (error) {
            console.error('Signup failed:', error);
            throw new Error(error instanceof Error ? error.message : 'Signup failed');
        }
    },
};

// Convenience functions for easier usage
export const login = authAPI.login;
export const getCurrentUser = authAPI.getCurrentUser;
export const requestOTP = authAPI.requestOTP;
export const resetPassword = authAPI.resetPassword;
export const signup = authAPI.signup;

// Helper functions for authentication management
export const authHelpers = {
    /**
     * Check if user is authenticated by checking for access token
     */
    isAuthenticated(): boolean {
        if (typeof window === 'undefined') return false;
        const token = localStorage.getItem('access_token');
        return !!token;
    },

    /**
     * Get stored access token
     */
    getAccessToken(): string | null {
        if (typeof window === 'undefined') return null;
        return localStorage.getItem('access_token');
    },

    /**
     * Store access token in localStorage
     */
    setAccessToken(token: string): void {
        if (typeof window === 'undefined') return;
        localStorage.setItem('access_token', token);
    },

    /**
     * Remove access token and logout user
     */
    logout(): void {
        if (typeof window === 'undefined') return;
        localStorage.removeItem('access_token');
    },
};

// Export everything as default for backwards compatibility
export default {
    ...authAPI,
    ...authHelpers,
};

