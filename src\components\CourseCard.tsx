import Link from "next/link"
import { learningPathsData } from "@/data/learningPaths"

interface CourseCardProps {
  id: string
  name: string
  description: string
  coverImage: string
  certify: boolean
  path: string
  level: string
  lessonCount: number
  duration: number
 
}

export default function CourseCard({
  id,
  name,
  description,
  coverImage,
  certify,
  path,
  level,
  lessonCount,
  duration,
}: CourseCardProps) {
  const hours = duration / 3600

  // ฟังก์ชันหา paths ที่มีคอร์สนี้
  const getCoursePaths = (courseId: string) => {
    try {
      if (!learningPathsData || !Array.isArray(learningPathsData)) {
        return []
      }
      return learningPathsData.filter((path) => {
        if (!path || !path.courseIds || !Array.isArray(path.courseIds)) {
          return false
        }
        return path.courseIds.includes(courseId)
      })
    } catch (error) {
      console.error("Error getting course paths:", error)
      return []
    }
  }

  const coursePaths = getCoursePaths(id)

  return (
    <Link href={`/courses/${id}`}>
      <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        {/* Course Image */}
        <div className="relative h-48">
          <img src={coverImage || "/placeholder.svg"} alt={name} className="w-full h-full object-cover" />
        </div>

        {/* Card Content */}
        <div className="p-6">
          {/* Certificate Badge */}
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-[#164A7E] font-semibold flex items-center gap-2">
              <svg
                aria-hidden="true"
                focusable="false"
                data-prefix="fas"
                data-icon="certificate"
                className="w-4 h-4 text-yellow-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 512 512"
              >
                <path
                  fill="currentColor"
                  d="M211 7.3C205 1 196-1.4 187.6 .8s-14.9 8.9-17.1 17.3L154.7 80.6l-62-17.5c-8.4-2.4-17.4 0-23.5 6.1s-8.5 15.1-6.1 23.5l17.5 62L18.1 170.6c-8.4 2.1-15 8.7-17.3 17.1S1 205 7.3 211l46.2 45L7.3 301C1 307-1.4 316 .8 324.4s8.9 14.9 17.3 17.1l62.5 15.8-17.5 62c-2.4 8.4 0 17.4 6.1 23.5s15.1 8.5 23.5 6.1l62-17.5 15.8 62.5c2.1 8.4 8.7 15 17.1 17.3s17.3-.2 23.4-6.4l45-46.2 45 46.2c6.1 6.2 15 8.7 23.4 6.4s14.9-8.9 17.1-17.3l15.8-62.5 62 17.5c8.4 2.4 17.4 0 23.5-6.1s8.5-15.1 6.1-23.5l-17.5-62 62.5-15.8c8.4-2.1 15-8.7 17.3-17.1s-.2-17.3-6.4-23.4l-46.2-45 46.2-45c6.2-6.1 8.7-15 6.4-23.4s-8.9-14.9-17.3-17.1l-62.5-15.8 17.5-62c2.4-8.4 0-17.4-6.1-23.5s-15.1-8.5-23.5-6.1l-62 17.5L341.4 18.1c-2.1-8.4-8.7-15-17.1-17.3S307 1 301 7.3L256 53.5 211 7.3z"
                ></path>
              </svg>
              {certify ? (
                <span className="text-[#008067] font-bold">Certificate Available</span>
              ) : (
                <span className="text-red-500 font-bold">No Certificate</span>
              )}
            </span>
          </div>

          {/* Course Title */}
          <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-1">{name}</h3>

          {/* Course Description */}
          <p className="text-gray-600 text-sm mb-3 line-clamp-2 h-[6vh]">{description}</p>

          {/* Path Tags */}
          {coursePaths.length > 0 && (
            <div className="flex flex-wrap gap-1 ">
              {coursePaths.map((path) => (
                <span
                  key={path.id}
                  className="px-2 py-1 text-xs bg-[#e1f1ee] border  font-bold text-[#008067] rounded-full"
                >
                   {path.name}
                </span>
              ))}
            </div>
          )}

          {/* Course Stats */}
          <div className="flex items-center justify-between pt-4 ">
            {/* Level Indicator */}
            <div className="flex items-center gap-1">
              <div className="flex gap-0.5">
                {["เบื้องต้น", "ปานกลาง", "ยาก"].map((l, i) => {
                  let circleColor = "bg-gray-200"
                  if (level === "เบื้องต้น") {
                    if (i === 0) circleColor = "bg-green-500"
                  } else if (level === "ปานกลาง") {
                    if (i === 0) circleColor = "bg-[#ffce66]"
                    if (i === 1) circleColor = "bg-[#FFB211]"
                  } else if (level === "ยาก") {
                    if (i === 0) circleColor = "bg-red-200"
                    if (i === 1) circleColor = "bg-red-300"
                    if (i === 2) circleColor = "bg-red-500"
                  }
                  return <div key={l} className={`w-2 h-2 rounded-full ${circleColor}`} />
                })}
              </div>
              <span className="text-sm text-gray-600 ml-1">{level}</span>
            </div>

            {/* Lesson Count */}
            <div className="flex items-center gap-1">
              <svg
                className="w-4 h-4 text-gray-400"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
              <span className="text-sm text-gray-600">{lessonCount} บทเรียน</span>
            </div>

            {/* Duration */}
            <div className="flex items-center gap-1">
              <svg
                className="w-4 h-4 text-gray-400"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm text-gray-600">{hours} ชม.</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}
