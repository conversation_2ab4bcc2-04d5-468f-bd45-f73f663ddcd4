import React from "react";
import Link from "next/link";
import CourseCard from "@/components/CourseCard";

interface Course {
  id: string;
  coverImage: string;
  certify: boolean;
  name: string;
  description: string;
  level: string;
  lesson: { length: number };
  time: number;
}

const RecommendedCourses = ({ filteredCourses }: { filteredCourses: Course[] }) => {
  // สุ่ม 3 คอร์สจาก filteredCourses
  const getRandomCourses = () => {
    const shuffled = [...filteredCourses].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, 3);
  };

  const randomCourses = getRandomCourses();

  return (
    <div className="">
      <div className="content-section max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 p-10">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 min-h-[300px]">
          {randomCourses.length > 0 ? (
            randomCourses.map((course) => (
              <CourseCard
                key={course.id}
                id={course.id}
                name={course.name}
                description={course.description}
                coverImage={course.coverImage}
                certify={course.certify}
                level={course.level}
                lessonCount={course.lesson.length}
                duration={course.time} path={""}              />
            ))
          ) : (
            <p className="text-center text-gray-500 mt-6">No courses found.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default RecommendedCourses;
