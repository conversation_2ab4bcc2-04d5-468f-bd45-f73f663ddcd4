"use client"

import type React from "react"
import { use, useEffect, useState } from "react"
import { Search, ChevronLeft, ChevronRight, MoreHorizontal, UserPlus, Eye, Trash2 } from "lucide-react"
import { ChevronUp, ChevronDown } from "lucide-react"
import { getMedicalUsersData } from "@/data/allUsers"
import { learningPathsData } from "@/data/learningPaths"
import {
  getAssignedPathsByUserId,
  getPathsAssignedToUser,
  removeUserFromPath,
  updateUsersForPath,
} from "@/data/assignedPaths"
import type { MedicalUser } from "@/data/allUsers"
import type { LearningPath } from "@/types/learning-paths"
import type { AssignedPath } from "@/types/assigned-paths"
import { fetchAllAssignedStudentPaths, removeAssignedStudentPathById } from "@/hook/assignpathService"
import StudentProgressChart from './charts/StudentProgressChart';
import { AssignStudent } from "@/types/api/assignpath"
import Swal from "sweetalert2"
import { showDeleteConfirmDialog } from "@/lib/sweetAlert"


export function AssignStudentsTable() {
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedRows, setSelectedRows] = useState<string[]>([])
  const [sortField, setSortField] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [selectAll, setSelectAll] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [selectedUser, setSelectedUser] = useState<MedicalUser | null>(null)
  const [selectedPaths, setSelectedPaths] = useState<string[]>([])
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false)
  const [student, setStudent] = useState<AssignStudent[] | null>(null)

  const itemsPerPage = 10

  // กรองเฉพาะ user ที่มี role เป็น student
  // const students = getMedicalUsersData.filter((user) => user.role === "student")

  // ฟังก์ชันค้นหา
  const filteredStudents = (student ?? []).filter(
    (student) => {
      // fallback: ถ้าไม่มี fname/lname ให้แยกจาก full_name
      let fname = student.fname;
      let lname = student.lname;
      // fallback จาก full_name ถ้ามี
      if ((!fname || !lname) && (student as any).full_name) {
        const parts = (student as any).full_name.split(" ");
        fname = parts[0] || "";
        lname = parts.slice(1).join(" ") || "";
      }
      return (
        fname.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lname.toLowerCase().includes(searchQuery.toLowerCase()) ||
        student.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (student.position?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false)
      );
    }
  )

  // ฟังก์ชันเรียงลำดับ
  const sortedStudents = [...filteredStudents].sort((a, b) => {
    let af = a.fname, al = a.lname, bf = b.fname, bl = b.lname;
    if ((!af || !al) && (a as any).full_name) {
      const parts = (a as any).full_name.split(" ");
      af = parts[0] || "";
      al = parts.slice(1).join(" ") || "";
    }
    if ((!bf || !bl) && (b as any).full_name) {
      const parts = (b as any).full_name.split(" ");
      bf = parts[0] || "";
      bl = parts.slice(1).join(" ") || "";
    }
    if (!sortField) return 0
    if (sortField === "fullname") {
      const fullnameA = `${af} ${al}`
      const fullnameB = `${bf} ${bl}`
      return sortDirection === "asc" ? fullnameA.localeCompare(fullnameB) : fullnameB.localeCompare(fullnameA)
    }
    const fieldA = a[sortField as keyof typeof a]
    const fieldB = b[sortField as keyof typeof b]
    if (typeof fieldA === "string" && typeof fieldB === "string") {
      return sortDirection === "asc" ? fieldA.localeCompare(fieldB) : fieldB.localeCompare(fieldA)
    }
    return 0
  })

  // ฟังก์ชันแบ่งหน้า
  const totalPages = Math.ceil(sortedStudents.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedStudents = sortedStudents.slice(startIndex, startIndex + itemsPerPage)

  // ฟังก์ชันดึง learning paths ที่ assign ให้ user
  const getAssignedPaths = (userId: string): { path: LearningPath; assignment: AssignedPath }[] => {
    const assignments = getAssignedPathsByUserId(userId)
    return assignments
      .map((assignment) => {
        const path = learningPathsData.find((p) => p.id === assignment.pathId)
        return path ? { path, assignment } : null
      })
      .filter(Boolean) as { path: LearningPath; assignment: AssignedPath }[]
  }

  // ฟังก์ชันเปิด dialog สำหรับ assign paths
  const openAssignDialog = (user: MedicalUser) => {
    setSelectedUser(user)
    setSelectedPaths(getPathsAssignedToUser(user.id))
    setIsAssignDialogOpen(true)
  }

  // ฟังก์ชันบันทึกการ assign
  const handleAssignPaths = () => {
    if (selectedUser) {
      const currentPaths = getPathsAssignedToUser(selectedUser.id)

      // ลบ user ออกจาก paths ที่ไม่ได้เลือก
      currentPaths.forEach((pathId) => {
        if (!selectedPaths.includes(pathId)) {
          removeUserFromPath(pathId, selectedUser.id)
        }
      })

      // เพิ่ม user ให้กับ paths ที่เลือกใหม่
      selectedPaths.forEach((pathId) => {
        if (!currentPaths.includes(pathId)) {
          const currentUsers = learningPathsData.find((p) => p.id === pathId)
            ? getAssignedPathsByUserId(selectedUser.id)
              .map((a) => a.userId)
              .join(",")
              .split(",")
              .filter((id) => id)
            : []
          updateUsersForPath(pathId, [...new Set([...currentUsers, selectedUser.id])])
        }
      })

      console.log("Updated assignments for user:", selectedUser.id)
      setIsAssignDialogOpen(false)
      setSelectedUser(null)
      setSelectedPaths([])
    }
  }

  // ฟังก์ชันเลือกแถว
  const toggleRowSelection = (id: string) => {
    if (selectedRows.includes(id)) {
      setSelectedRows(selectedRows.filter((rowId) => rowId !== id))
    } else {
      setSelectedRows([...selectedRows, id])
    }
  }

  // ฟังก์ชันเลือกทั้งหมด
  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedRows([])
    } else {
      setSelectedRows(paginatedStudents.map((student) => student.user_slug))
    }
    setSelectAll(!selectAll)
  }

  // ฟังก์ชันเรียงลำดับ
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // ฟังก์ชัน toggle dropdown
  const toggleDropdown = (id: string) => {
    setActiveDropdown(activeDropdown === id ? null : id)
  }

  // ฟังก์ชันแสดงไอคอนการเรียงลำดับ
  function renderSortIcon(field: string): React.ReactNode {
    if (sortField === field) {
      return sortDirection === "asc" ? (
        <ChevronUp className="ml-1 h-4 w-4 text-gray-700" />
      ) : (
        <ChevronDown className="ml-1 h-4 w-4 text-gray-700" />
      )
    }
    return (
      <span className="ml-1 inline-flex flex-col">
        <ChevronUp className="h-3 w-3 -mb-1 text-gray-400" />
        <ChevronDown className="h-3 w-3 text-gray-400" />
      </span>
    )
  }

  useEffect(() => {
    async function fetchData() {
      fetchAllAssignedStudentPaths().then((data) => {
        setStudent(data)
        // console.log("Assigned paths data fetched:", data[0])
      }).catch((error) => {
        console.error("Error fetching assigned paths:", error)
      })
    }

    fetchData()
  }, [])
  return (
    <div className="w-full">
      {/* ส่วนค้นหาและปุ่มเพิ่ม */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
        <div className="relative w-full sm:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="ค้นหานักเรียน..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full bg-gray-50 sm:w-[300px] focus:outline-none focus:ring-2 focus:ring-[#008268]"
          />
        </div>

        <button
          onClick={() => {
            window.location.href = `/e-med/admin/assign-paths/new`
          }}
          className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-md w-full sm:w-auto flex items-center justify-center gap-2"
        >
          มอบหมายเส้นทางเรียนรู้
          <UserPlus className="h-4 w-4" />
        </button>
      </div>

      {/* ตาราง */}
      <div className="overflow-x-auto border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-3 py-3 text-center">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={toggleSelectAll}
                  className="h-4 w-4 rounded border-gray-300 bg-gray-50 text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                />
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("fullname")}
              >
                <div className="flex items-center">
                  ชื่อ-นามสกุล
                  {renderSortIcon("fullname")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("position")}
              >
                <div className="flex items-center">
                  ตำแหน่ง
                  {renderSortIcon("position")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                เส้นทางการเรียนรู้
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("status")}
              >
                <div className="flex items-center">
                  สถานะ
                  {renderSortIcon("status")}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                จัดการ
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedStudents.map((student) => {
              // const assignedPaths = getAssignedPaths(student.user_slug)
              const assignedPaths = student.assigned_paths || [];
              return (
                <tr key={student.user_slug} className="hover:bg-gray-50">
                  <td className="px-3 py-4 whitespace-nowrap text-center">
                    <input
                      type="checkbox"
                      checked={selectedRows.includes(student.user_slug)}
                      onChange={() => toggleRowSelection(student.user_slug)}
                      className="h-4 w-4 rounded border-gray-300 bg-white text-[#008268] focus:ring-[#008268] focus:ring-offset-0"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 relative">
                        {student.picture ? (
                          <img
                            src={student.picture || "/placeholder.svg"}
                            alt={`${student.fname} ${student.lname}`}
                            width={40}
                            height={40}
                            className="rounded-full object-cover"
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <span className="text-gray-500 font-medium text-sm">
                              {student.fname?.charAt(0)}
                              {student.lname?.charAt(0)}
                            </span>
                          </div>
                        )}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {student.fname} {student.lname}
                        </div>
                        <div className="text-sm text-gray-500">{student.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{student.position}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-2 max-w-xs">
                      {assignedPaths.length > 0 ? (
                        assignedPaths.map((path: any) => (
                          <span
                            key={`${path.path_id}-${student.user_slug}`}
                            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200"
                          >
                            {path.path_name}
                          </span>
                        ))
                      ) : (
                        <span className="text-sm text-gray-500">ยังไม่ได้รับมอบหมาย</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${student.status === "1" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-700"
                        }`}
                    >
                      {student.status === "1" ? "ใช้งาน" : "ไม่ใช้งาน"}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium relative">
                    <button onClick={() => toggleDropdown(student.user_slug)} className="text-gray-500 hover:text-gray-700">
                      <MoreHorizontal className="h-5 w-5" />
                    </button>

                    {activeDropdown === student.user_slug && (
                      <div className="fixed right-10 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                        <div className="py-1" role="menu" aria-orientation="vertical">
                          <button
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => {
                              window.location.href = `/e-med/admin/assign-paths/edit/${student.user_slug}`
                              setActiveDropdown(null)
                            }}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            แก้ไขเส้นทางเรียนรู้
                          </button>
                          <button
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                            onClick={async () => {
                              // use sweetalert or confirm dialog to confirm deletion
                              const result = await showDeleteConfirmDialog(
                                `คุณต้องการลบเส้นทางการเรียนรู้ของ ${student.fname} ${student.lname} หรือไม่?`
                              );
                              if (result.isConfirmed) {
                                // ลบเส้นทางการเรียนรู้ที่มอบหมายให้กับนักเรียน
                                removeAssignedStudentPathById(student.user_slug).then(() => {
                                  // รีเฟรชข้อมูลนักเรียน
                                  fetchAllAssignedStudentPaths().then((data) => {
                                    setStudent(data)
                                  }).catch((error) => {
                                    console.error("Error fetching assigned paths:", error)
                                  })
                                }).catch((error) => {
                                  console.error("Error removing assigned paths:", error)
                                })
                              }
                            }}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            ลบ
                          </button>
                          {/* <button
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => {
                              // window.location.href = `/e-med/admin/assign-paths/new?studentId=${student.user_slug}`
                              setActiveDropdown(null)
                            }}
                          >
                            <
                            
                          </button> */}
                        </div>
                      </div>
                    )}
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>

      {/* ส่วนแสดงจำนวนที่เลือกและการแบ่งหน้า */}
      <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4">
        <div className="text-sm text-gray-500">
          เลือก {selectedRows.length} จาก {filteredStudents.length} รายการ
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700">
            หน้า {currentPage} จาก {totalPages}
          </span>
          <div className="flex gap-1">
            <button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Dialog สำหรับ assign learning paths */}
      {isAssignDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="mb-4">
              <h3 className="text-lg font-medium text-gray-900">จัดการเส้นทางการเรียนรู้</h3>
              <p className="text-sm text-gray-500 mt-1">
                เลือกเส้นทางการเรียนรู้ที่ต้องการมอบให้กับ {selectedUser && `${selectedUser.firstname} ${selectedUser.lastname}`}
              </p>
            </div>

            <div className="space-y-4 max-h-96 overflow-y-auto mb-6">
              {learningPathsData
                .filter((path) => path.status === "published")
                .map((path) => (
                  <div key={path.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id={path.id}
                      checked={selectedPaths.includes(path.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedPaths([...selectedPaths, path.id])
                        } else {
                          setSelectedPaths(selectedPaths.filter((id) => id !== path.id))
                        }
                      }}
                      className="h-4 w-4 rounded border-gray-300 text-[#008268] focus:ring-[#008268]"
                    />
                    <div className="space-y-1 flex-1">
                      <label htmlFor={path.id} className="text-sm font-medium leading-none cursor-pointer">
                        {path.name}
                      </label>
                      <p className="text-xs text-gray-500 line-clamp-2">{path.description}</p>
                      <div className="flex items-center gap-2">
                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                          {path.courseCount} คอร์ส
                        </span>
                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          เผยแพร่แล้ว
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
            </div>

            <div className="flex justify-end gap-3">
              <button
                onClick={() => setIsAssignDialogOpen(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                ยกเลิก
              </button>
              <button
                onClick={handleAssignPaths}
                className="px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-gray-800"
              >
                บันทึกการมอบหมาย
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
