"use client"

import { useEffect, useState, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import {
  BookOpen,
  Award,
  Calendar,
  ChevronRight,
  Clock,
  ChevronLeft,
  ChevronDown,
  Filter,
  CheckCircle,
  Circle,
  Target,
} from "lucide-react"
import { profileService, UserCourse } from '@/hook/profileService'
import { authAPI, UserMeResponse } from '@/hook/auth'
import Navbar from "@/components/headers"
import CourseCard from "@/components/progressCard"
import type { MedicalUser } from "@/types/users"
import type { LearningStatus } from "@/types/progress"
import DashboardSidebar from "@/components/DashboardSidebar"
import PathPreviewCard from "@/components/PathPreviewCard"

// ประเภทของการกรองคอร์ส
type FilterType = "all" | "completed" | "in_progress"

// Utility function to handle image format - based on working course-editor pattern
const formatImageSrc = (imageData: string | null | undefined): string => {
  if (process.env.NODE_ENV === "development") {
    console.log('formatImageSrc input:', {
      hasData: !!imageData,
      length: imageData?.length,
      firstChars: imageData?.substring(0, 50)
    })
  }

  if (!imageData || imageData.trim() === '') {
    if (process.env.NODE_ENV === "development") {
      console.log('No image data, using placeholder')
    }
    return "/placeholder.svg"
  }

  const trimmedData = imageData.trim()

  // If it's already a complete data URL (like from file upload), return as is
  if (trimmedData.startsWith('data:image/')) {
    console.log('Already has data:image prefix, using as is')
    return trimmedData
  }

  // For base64 from database, add data URL prefix (same as course-editor pattern)
  // This is the key pattern that works in course-editor
  console.log('Converting base64 to data URL (course-editor pattern)')
  return `data:image/jpeg;base64,${trimmedData}`
}

export default function DashboardPage() {
  const router = useRouter()
  const [user, setUser] = useState<UserMeResponse | null>(null)
  const [userCourses, setUserCourses] = useState<UserCourse[]>([])
  const [filteredCourses, setFilteredCourses] = useState<UserCourse[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentFilter, setCurrentFilter] = useState<FilterType>("all")
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const coursesScrollRef = useRef<HTMLDivElement>(null)
  const [overview, setOverview] = useState<{ total_courses: number; total_certificates: number } | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasNextPage, setHasNextPage] = useState(false)
  const [hasPrevPage, setHasPrevPage] = useState(false)

  // Update fetchUserData to accept page
  const fetchUserCourses = async (page = 1) => {
    try {
      const result = await profileService.getUserCoursesDashboard(page)
      if (Array.isArray(result)) {
        setUserCourses(result)
        setFilteredCourses(result)
        setHasNextPage(false)
        setHasPrevPage(false)
      } else {
        setUserCourses(result.data)
        setFilteredCourses(result.data)
        setHasNextPage(result.pagination.has_next)
        setHasPrevPage(result.pagination.has_prev)
      }
    } catch (coursesError) {
      setUserCourses([])
      setFilteredCourses([])
      setHasNextPage(false)
      setHasPrevPage(false)
    }
  }

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        // Check if user is authenticated - check both localStorage and cookies
        const tokenFromStorage = localStorage.getItem('access_token')
        const tokenFromCookie = document.cookie.match(/(?:^|;\s*)access_token=([^;]+)/)
        const token = tokenFromStorage || (tokenFromCookie ? decodeURIComponent(tokenFromCookie[1]) : null)

        if (!token) {
          router.push("/login")
          return
        }

        // Fetch current user data from API
        try {
          const userData = await authAPI.getCurrentUser()
          setUser(userData)
        } catch (userError) {
          console.error('Failed to fetch user data:', userError)
          if (userError instanceof Error && (userError.message.includes('401') || userError.message.includes('unauthorized'))) {
            localStorage.removeItem('access_token')
            document.cookie = 'access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
            router.push("/login")
            return
          }
        }

        // Fetch user courses dashboard (separate try-catch to not block user data)
        await fetchUserCourses(1)
        setCurrentPage(1)

        // Fetch overview dashboard
        try {
          const overviewData = await profileService.getUserOverviewDashboard()
          setOverview(overviewData)
        } catch (overviewError) {
          console.error('Failed to fetch overview dashboard:', overviewError)
          setOverview(null)
        }
      } catch (error) {
        console.error('Failed to fetch data:', error)
        // Generic error handling
        setUserCourses([])
        setFilteredCourses([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [router])

  // กรองคอร์สตามสถานะ
  useEffect(() => {
    if (!Array.isArray(userCourses)) {
      setFilteredCourses([])
      return
    }

    if (currentFilter === "all") {
      setFilteredCourses(userCourses)
    } else if (currentFilter === "completed") {
      setFilteredCourses(userCourses.filter((course) => course.course_graduated))
    } else {
      setFilteredCourses(userCourses.filter((course) => !course.course_graduated))
    }
  }, [currentFilter, userCourses])

  // คำนวณจำนวนคอร์สที่เรียนจบแล้ว
  const completedCourses = Array.isArray(userCourses)
    ? userCourses.filter((course) => course.course_graduated).length
    : 0

  // ฟังก์ชันเลื่อนการ์ดคอร์ส (fetch next page data only, no UI update)
  const scrollCourses = async (direction: "left" | "right") => {
    let nextPage = currentPage
    if (direction === "right") {
      nextPage = currentPage + 1
    } else if (direction === "left" && currentPage > 1) {
      nextPage = currentPage - 1
    } else {
      // Optionally scroll if already at first page
      if (coursesScrollRef.current) {
        const scrollAmount = 300
        coursesScrollRef.current.scrollBy({ left: direction === "left" ? -scrollAmount : scrollAmount, behavior: "smooth" })
      }
      return
    }
    // Just fetch, do not update state/UI
    try {
      await profileService.getUserCoursesDashboard(nextPage).then((result) => {
        if (Array.isArray(result)) {
          setUserCourses(result)
          setFilteredCourses(result)
        } else {
          setUserCourses(result.data)
          setFilteredCourses(result.data)

        }
        setCurrentPage(nextPage)
        if ('pagination' in result && result.pagination) {
          setHasNextPage(result.pagination.has_next)
          setHasPrevPage(result.pagination.has_prev)
        } else {
          setHasNextPage(false)
          setHasPrevPage(false)
        }
      })
    } catch (e) {
      // Optionally handle error
      console.error('Fetch failed:', e)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#008268]"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">ไม่พบข้อมูลผู้ใช้</h1>
          <p className="text-gray-600 mb-6">กรุณาเข้าสู่ระบบเพื่อดูข้อมูลแดชบอร์ด</p>
          <button
            onClick={() => router.push("/login")}
            className="px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58]"
          >
            เข้าสู่ระบบ
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#f9fafb] w-full">
        <div className="flex w-full">
          {/* Sidebar */}
          <DashboardSidebar />

          {/* Main Content - ปรับให้เต็มหน้าจอ */}
          <div className="flex-1 ml-[60px] pt-[64px] w-[calc(100%-60px)]">
            <div className="w-full p-6">
              {/* Spacer instead of header */}
              <div className="mb-6"></div>

              {/* Main Dashboard Content */}
              <div className="grid grid-cols-1 lg:grid-cols-[1fr_3fr] gap-6 w-full">
                {/* User Profile Card */}
                <div className="bg-white rounded-xl shadow-sm p-6 h-fit lg:sticky lg:top-20">
                  <div className="flex flex-col items-center">
                    <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-[#e6f2f0] mb-4">
                      {user?.user_picture ? (
                        <Image
                          src={formatImageSrc(user.user_picture)}
                          alt={user.user_fname}
                          width={96}
                          height={96}
                          className="object-cover w-full h-full"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-[#004c41] to-[#2fbcc1] flex items-center justify-center text-white">
                          <span className="text-2xl font-medium">
                            {user?.user_fname.charAt(0)}
                            {user?.user_lname.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>
                    <h2 className="text-xl font-bold text-gray-800 text-center">
                      {user?.user_fname} {user?.user_lname}
                    </h2>
                    <p className="text-sm text-gray-600 mb-4 text-center">{user?.user_position || 'ผู้ใช้งาน'}</p>

                    <div className="grid grid-cols-2 gap-4 w-full mt-2">
                      <div className="bg-gray-50 p-3 rounded-lg text-center">
                        <span className="text-2xl font-bold text-gray-800">
                          {overview ? overview.total_courses : 0}
                        </span>
                        <div className="text-xs text-gray-500">คอร์สเรียน</div>
                      </div>
                      <div className="bg-gray-50 p-3 rounded-lg text-center">
                        <span className="text-2xl font-bold text-gray-800">{overview ? overview.total_certificates : 0}</span>
                        <div className="text-xs text-gray-500">ใบรับรอง</div>
                      </div>
                    </div>
                  </div>

                  {/* Pathways  Courses Section */}

                  <div className="mt-6">
                    <h3 className="text-sm font-medium text-gray-700 mb-3 flex justify-between items-center">
                      <span>เส้นทางการเรียนของคุณ</span>
                      <Link href="/profile/certificate-pathways" className="text-xs text-[#008268] hover:underline">
                        ดูทั้งหมด
                      </Link>
                    </h3>
                    <div className="h-[500px] overflow-y-auto hide-scrollbar">
                      <PathPreviewCard userId={user?.id.toString()} />
                    </div>

                  </div>
                </div>

                {/* Pathway ends */}

                {/* Main Content Area */}
                <div className="flex flex-col gap-6 w-full">
                  {/* Featured Course Banner */}
                  <div className="bg-gradient-to-r from-[#008268] to-[#2fbcc1] rounded-xl p-6 text-white relative overflow-hidden h-[180px] flex items-center w-full">
                    <div className="absolute right-0 top-0 w-40 h-40 opacity-20">
                      <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fill="#FFFFFF"
                          d="M45.3,-59.1C58.9,-51.1,70.2,-37.3,76.4,-21.1C82.6,-4.9,83.7,13.7,77.2,29.7C70.7,45.7,56.5,59.1,40.3,67.7C24.1,76.3,5.9,80,-11.4,77.2C-28.7,74.4,-45.1,65.1,-57.4,51.5C-69.8,37.9,-78.1,19.9,-79.1,1.1C-80.1,-17.8,-73.9,-35.6,-61.8,-47.8C-49.7,-60,-24.9,-66.5,-3.2,-62.8C18.5,-59.1,31.7,-67.1,45.3,-59.1Z"
                          transform="translate(100 100)"
                        />
                      </svg>
                    </div>
                    <div className="relative z-10">
                      <h2 className="text-2xl font-bold mb-2">คอร์สแนะนำสำหรับคุณ</h2>
                      <p className="mb-4 max-w-xl">เลือกจากคอร์สออนไลน์ด้านการแพทย์กว่า 50 คอร์ส พร้อมเนื้อหาใหม่ทุกเดือน</p>
                      <Link
                        href="/courses"
                        className="inline-block px-4 py-2 bg-white text-[#008268] rounded-md font-medium hover:bg-gray-50 transition-colors"
                      >
                        ดูคอร์สทั้งหมด
                      </Link>
                    </div>
                  </div>

                  {/* Your Courses */}
                  <div className="mb-8 w-full">
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex items-center gap-2">
                        <h2 className="text-xl font-bold text-gray-800">คอร์สของคุณ</h2>

                        {/* Filter dropdown */}
                        <div className="relative ml-2">
                          <button
                            onClick={() => setShowFilterDropdown(!showFilterDropdown)}
                            className="flex items-center text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded hover:bg-gray-200"
                          >
                            <Filter size={14} className="mr-1" />
                            {currentFilter === "all"
                              ? "ทั้งหมด"
                              : currentFilter === "completed"
                                ? "เรียนจบแล้ว"
                                : "กำลังเรียน"}
                            <ChevronDown size={14} className="ml-1" />
                          </button>

                          {showFilterDropdown && (
                            <div className="absolute top-full left-0 mt-1 bg-white shadow-md rounded-md z-10 w-40 py-1">
                              <button
                                onClick={() => {
                                  setCurrentFilter("all")
                                  setShowFilterDropdown(false)
                                }}
                                className="flex items-center w-full px-3 py-2 text-sm text-left hover:bg-gray-100"
                              >
                                {currentFilter === "all" ? (
                                  <CheckCircle size={14} className="mr-2 text-green-600" />
                                ) : (
                                  <Circle size={14} className="mr-2 text-gray-300" />
                                )}
                                ทั้งหมด
                              </button>
                              <button
                                onClick={() => {
                                  setCurrentFilter("completed")
                                  setShowFilterDropdown(false)
                                }}
                                className="flex items-center w-full px-3 py-2 text-sm text-left hover:bg-gray-100"
                              >
                                {currentFilter === "completed" ? (
                                  <CheckCircle size={14} className="mr-2 text-green-600" />
                                ) : (
                                  <Circle size={14} className="mr-2 text-gray-300" />
                                )}
                                เรียนจบแล้ว
                              </button>
                              <button
                                onClick={() => {
                                  setCurrentFilter("in_progress")
                                  setShowFilterDropdown(false)
                                }}
                                className="flex items-center w-full px-3 py-2 text-sm text-left hover:bg-gray-100"
                              >
                                {currentFilter === "in_progress" ? (
                                  <CheckCircle size={14} className="mr-2 text-green-600" />
                                ) : (
                                  <Circle size={14} className="mr-2 text-gray-300" />
                                )}
                                กำลังเรียน
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => scrollCourses("left")}
                          className="p-1 rounded-full bg-gray-100 hover:bg-gray-200"
                          aria-label="Scroll left"
                        >
                          <ChevronLeft size={20} />
                        </button>
                        <button
                          onClick={() => scrollCourses("right")}
                          className="p-1 rounded-full bg-gray-100 hover:bg-gray-200"
                          aria-label="Scroll right"
                        >
                          <ChevronRight size={20} />
                        </button>
                        <Link href="/profile/my-courses" className="text-sm text-[#008268] hover:underline">
                          ดูทั้งหมด
                        </Link>
                      </div>
                    </div>

                    {/* แสดงคอร์สที่ลงทะเบียนแล้ว */}
                    {filteredCourses.length > 0 ? (
                      <div
                        ref={coursesScrollRef}
                        className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 w-full overflow-x-auto pb-4 hide-scrollbar"
                        style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
                      >
                        {filteredCourses.map((course) => {
                          // Debug logging based on course-editor pattern
                          if (course.course_image) {
                            console.log(`Course ${course.course_slug} - Image data:`, {
                              length: course.course_image.length,
                              startsWithData: course.course_image.startsWith('data:'),
                              first50: course.course_image.substring(0, 50)
                            })
                          }

                          const processedImageSrc = formatImageSrc(course.course_image)
                          console.log(`Course ${course.course_slug} - Processed src:`, processedImageSrc?.substring(0, 100))

                          return (
                            <CourseCard
                              key={course.course_slug}
                              id={course.course_slug}
                              description={course.course_description}
                              name={course.course_name}
                              teacherName={"-"}
                              coverImage={processedImageSrc}
                              progress={course.course_processing || 0}
                              completedLessons={course.course_lessoned || 0}
                              totalLessons={course.course_amount || 0}
                              duration={course.course_duration}
                              status={course.course_graduated ? "completed" : "in_progress"}
                              level={course.course_difficulty}
                              certify={false}
                            />
                          )
                        })}
                      </div>
                    ) : (
                      <div className="bg-white rounded-xl p-6 text-center w-full">
                        <p className="text-gray-600 mb-4">
                          {currentFilter === "all"
                            ? "คุณยังไม่มีคอร์สที่กำลังเรียนหรือเรียนจบแล้ว"
                            : currentFilter === "completed"
                              ? "คุณยังไม่มีคอร์สที่เรียนจบ"
                              : "คุณยังไม่มีคอร์สที่กำลังเรียน"}
                        </p>
                        <Link
                          href="/courses"
                          className="inline-block px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58] transition-colors"
                        >
                          ดูคอร์สเรียนทั้งหมด
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Custom CSS for hiding scrollbar */}
        <style jsx global>{`
          .hide-scrollbar::-webkit-scrollbar {
            display: none;
          }
          .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
          }
          .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          
          /* iPad Pro และอุปกรณ์ขนาดกลาง */
          @media (min-width: 768px) and (max-width: 1024px) {
            .grid-cols-2 {
              grid-template-columns: repeat(2, minmax(0, 1fr));
            }
            
            /* เพิ่ม padding ให้กับ content เพื่อให้มีพื้นที่มากขึ้น */
            .p-6 {
              padding: 1.25rem;
            }
            
            /* ปรับขนาด gap ระหว่างการ์ด */
            .gap-4 {
              gap: 1rem;
            }
          }
          
          /* สำหรับ iPad Pro แนวนอน */
          @media (min-width: 1024px) and (max-width: 1366px) {
            .grid-cols-3 {
              grid-template-columns: repeat(3, minmax(0, 1fr));
            }
          }
        `}</style>
      </div>
    </>
  )
}
