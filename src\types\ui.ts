import type { CourseType, Lesson, Content } from "./courses"
import type { UserProgress } from "./progress"
import type { Quiz } from "./quizzes"
import type { FinalExam } from "./finalexam"

// Props สำหรับ LearmingContent Component
export interface LearmingContentProps {
  content?: Content
  menuOpen?: {
    opened: boolean
    onMenuOpen: (menuOpen: boolean) => void
  }
  course?: CourseType
  lesson?: Lesson
  courses?: CourseType
  selectId?: {
    lessonId: string
    contentId: string
  }
  onComplete?: (result: { contentId: string; completed: boolean }) => void
  goToNextContent?: () => void
  userId?: string
}

// Props สำหรับ HrawersLearming Component
export interface HrawersLearmingProps {
  courses: CourseType[]
  select: {
    selectId: {
      lessonId: string
      contentId: string
    }
    setSelectId: (selectId: { lessonId: string; contentId: string }) => void
  }
  menuOpen: {
    opened: boolean
    onMenuOpen: (menuOpen: boolean) => void
  }
  userProgress: UserProgress
  hasFinalExam?: boolean
  userId?: string
}

// Props สำหรับ VideoPlayer Component
export interface VideoPlayerProps {
  videoUrl: string
  courseId?: string
  lessonId?: string
  contentId?: string
  userId?: string
  onComplete?: (result: { contentId: string; completed: boolean }) => void
  autoPlay?: boolean
  controls?: boolean
}

// Props สำหรับ QuizComponent Component
export interface QuizComponentProps {
  quiz: Quiz
  onComplete?: (result: { score: number; passed: boolean }) => void
}

// Props สำหรับ FinalExam Component
export interface FinalExamProps {
  examData: FinalExam
  onComplete: (passed: boolean, score: number) => void
}

// Props สำหรับ AdminSidebar Component
export interface AdminSidebarProps {
  activeItem?: string
}

// Props สำหรับ UserEditor Component
export interface UserEditorProps {
  userId?: string
}

// Props สำหรับ CourseEditor Component
export interface CourseEditorProps {
  courseId?: string
}

// Props สำหรับ QuizEditor Component
export interface QuizEditorProps {
  quizId?: string
}

// Props สำหรับ ExamEditor Component
export interface ExamEditorProps {
  examId?: string
}
