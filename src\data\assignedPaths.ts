import type { AssignedPath } from "@/types/assigned-paths"

// ข้อมูลการมอบหมายเส้นทางการเรียนรู้ให้กับนักเรียน
export const assignedPathsData: AssignedPath[] = [
  {
    pathId: "lp001",
    userId: "user-017,user-018,user-019",
    studentId: ""
  },
  {
    pathId: "lp002",
    userId: "user-017,user-019,user-020",
    studentId: ""
  },
  {
    pathId: "lp003",
    userId: "user-018,user-017",
    studentId: ""
  },
  {
    pathId: "lp004",
    userId: "user-019,user-020",
    studentId: ""
  },
  {
    pathId: "lp005",
    userId: "user-020,user-017",
    studentId: ""
  },
]

// ฟังก์ชันช่วยเหลือสำหรับจัดการข้อมูล
export const getAssignedPathsByUserId = (userId: string): AssignedPath[] => {
  return assignedPathsData.filter((assignment) => assignment.userId.split(",").includes(userId))
}

export const getAssignedPathsByPathId = (pathId: string): AssignedPath | undefined => {
  return assignedPathsData.find((assignment) => assignment.pathId === pathId)
}

export const getUsersAssignedToPath = (pathId: string): string[] => {
  const assignment = assignedPathsData.find((assignment) => assignment.pathId === pathId)
  return assignment ? assignment.userId.split(",") : []
}

export const getPathsAssignedToUser = (userId: string): string[] => {
  return assignedPathsData
    .filter((assignment) => assignment.userId.split(",").includes(userId))
    .map((assignment) => assignment.pathId)
}

// ฟังก์ชันตรวจสอบว่า user ได้รับ path นี้หรือไม่
export const isUserAssignedToPath = (userId: string, pathId: string): boolean => {
  const assignment = assignedPathsData.find((assignment) => assignment.pathId === pathId)
  return assignment ? assignment.userId.split(",").includes(userId) : false
}

// ฟังก์ชันเพิ่ม user ให้กับ path
export const addUserToPath = (pathId: string, userId: string): boolean => {
  const assignmentIndex = assignedPathsData.findIndex((assignment) => assignment.pathId === pathId)

  if (assignmentIndex > -1) {
    // Path มีอยู่แล้ว เพิ่ม user ใหม่
    const currentUsers = assignedPathsData[assignmentIndex].userId.split(",")
    if (!currentUsers.includes(userId)) {
      assignedPathsData[assignmentIndex].userId = [...currentUsers, userId].join(",")
      return true
    }
  } else {
    // Path ยังไม่มี สร้างใหม่
    assignedPathsData.push({
      pathId,
      userId,
      studentId: ""
    })
    return true
  }
  return false
}

// ฟังก์ชันลบ user ออกจาก path
export const removeUserFromPath = (pathId: string, userId: string): boolean => {
  const assignmentIndex = assignedPathsData.findIndex((assignment) => assignment.pathId === pathId)

  if (assignmentIndex > -1) {
    const currentUsers = assignedPathsData[assignmentIndex].userId.split(",")
    const updatedUsers = currentUsers.filter((id) => id !== userId)

    if (updatedUsers.length > 0) {
      // ยังมี user อื่นอยู่ อัพเดต userId
      assignedPathsData[assignmentIndex].userId = updatedUsers.join(",")
    } else {
      // ไม่มี user เหลือ ลบ assignment ทั้งหมด
      assignedPathsData.splice(assignmentIndex, 1)
    }
    return true
  }
  return false
}

// ฟังก์ชันอัพเดต users ทั้งหมดสำหรับ path
export const updateUsersForPath = (pathId: string, userIds: string[]): void => {
  const assignmentIndex = assignedPathsData.findIndex((assignment) => assignment.pathId === pathId)

  if (userIds.length > 0) {
    if (assignmentIndex > -1) {
      // อัพเดต assignment ที่มีอยู่
      assignedPathsData[assignmentIndex].userId = userIds.join(",")
    } else {
      // สร้าง assignment ใหม่
      assignedPathsData.push({
        pathId,
        userId: userIds.join(","),
        studentId: ""
      })
    }
  } else {
    // ไม่มี user เลย ลบ assignment
    if (assignmentIndex > -1) {
      assignedPathsData.splice(assignmentIndex, 1)
    }
  }
}

// ฟังก์ชันดึงข้อมูลรวมสำหรับ UI
export const getAssignedPathsWithDetails = (): AssignedPath[] => {
  return assignedPathsData.map((assignment) => ({
    pathId: assignment.pathId,
    userId: assignment.userId,
    studentId: assignment.studentId,
  }))
}

// ฟังก์ชันนับจำนวน students ใน path
export const getStudentCountByPath = (pathId: string): number => {
  const assignment = assignedPathsData.find((assignment) => assignment.pathId === pathId)
  return assignment ? assignment.userId.split(",").length : 0
}

// ฟังก์ชันดึงสถิติการมอบหมาย
export const getAssignmentStats = () => {
  const totalPaths = assignedPathsData.length
  const totalAssignments = assignedPathsData.reduce((sum, assignment) => {
    return sum + assignment.userId.split(",").length
  }, 0)
  const uniqueStudents = new Set(assignedPathsData.flatMap((assignment) => assignment.userId.split(","))).size

  return {
    totalPaths,
    totalAssignments,
    uniqueStudents,
    averageAssignmentsPerPath: totalPaths > 0 ? Math.round(totalAssignments / totalPaths) : 0,
  }
}
