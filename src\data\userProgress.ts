// ประเภทข้อมูลสำหรับความก้าวหน้าในการเรียน
export interface UserProgress {
    userId: string
    courseId: string
    status: "not_started" | "in_progress" | "completed"
    progress: number
    completedLessons: number
    totalLessons: number
    lastLesson?: {
      id: string
      name: string
    }
    lastAccessed: Date
    certificate?: {
      issued: boolean
      date?: Date
      url?: string
    }
  }
  
  // ข้อมูลตัวอย่างสำหรับความก้าวหน้าในการเรียน
  const userProgressData: UserProgress[] = [
    {
      userId: "user-017",
      courseId: "WnH5F0qZF938",
      status: "in_progress",
      progress: 45,
      completedLessons: 1,
      totalLessons: 2,
      lastLesson: {
        id: "627BieXQX84E",
        name: "การเตรียมอุปกรณ์",
      },
      lastAccessed: new Date("2023-11-27T10:15:00"),
    },
    {
      userId: "user-017",
      courseId: "QlH3I647MCuN",
      status: "completed",
      progress: 100,
      completedLessons: 2,
      totalLessons: 2,
      lastLesson: {
        id: "c4mT32O7RboV",
        name: "การให้ยาแก่ผู้ป่วย",
      },
      lastAccessed: new Date("2023-11-12T14:30:00"),
      certificate: {
        issued: true,
        date: new Date("2023-11-26T09:00:00"),
        url: "/certificates/QlH3I647MCuN-user-017.pdf",
      },
    },
    {
      userId: "user-017",
      courseId: "j0DPSgeg4b2q",
      status: "not_started",
      progress: 0,
      completedLessons: 0,
      totalLessons: 2,
      lastAccessed: new Date("2023-11-20T16:45:00"),
    },
    {
        userId: "user-017",
        courseId: "19x4uNv4RgED",
        status: "completed",
        progress: 100,
        completedLessons: 2,
        totalLessons: 2,
        lastLesson: {
          id: "17E9Wq6pJLKT",
          name: "การรักษาโรคอ้วนในเด็ก",
        },
        lastAccessed: new Date("2023-11-28T15:30:00"),
        certificate: {
          issued: true,
          date: new Date("2023-11-28T16:00:00"),
          url: "/certificates/19x4uNv4RgED-user-017.pdf",
        },
      },
    {
      userId: "user-017",
      courseId: "WnH5F0qZF938",
      status: "completed",
      progress: 100,
      completedLessons: 2,
      totalLessons: 2,
      lastLesson: {
        id: "627BieXQX84E",
        name: "รู้จักกับโรคปอดอักเสบ",
      },
      lastAccessed: new Date("2023-11-25T14:20:00"),
      certificate: {
        issued: true,
        date: new Date("2023-11-25T15:00:00"),
        url: "/certificates/WnH5F0qZF938-user-017.pdf",
      },
    },
    {
      userId: "user-018",
      courseId: "WnH5F0qZF938",
      status: "in_progress",
      progress: 75,
      completedLessons: 1,
      totalLessons: 2,
      lastLesson: {
        id: "627BieXQX84E",
        name: "การเตรียมอุปกรณ์",
      },
      lastAccessed: new Date("2023-11-27T11:20:00"),
    },
  ]
  
  // ฟังก์ชันสำหรับดึงข้อมูลความก้าวหน้าในการเรียนของผู้ใช้
  export function getUserProgress(userId: string): UserProgress[] {
    return userProgressData.filter((progress) => progress.userId === userId)
  }
  
  // ฟังก์ชันสำหรับดึงข้อมูลความก้าวหน้าในการเรียนของผู้ใช้สำหรับคอร์สเฉพาะ
  export function getUserCourseProgress(userId: string, courseId: string): UserProgress | undefined {
    return userProgressData.find((progress) => progress.userId === userId && progress.courseId === courseId)
  }
  
  // ฟังก์ชันสำหรับอัพเดตความก้าวหน้าในการเรียน
  export function updateUserProgress(progress: UserProgress): void {
    const index = userProgressData.findIndex((p) => p.userId === progress.userId && p.courseId === progress.courseId)
    if (index !== -1) {
      userProgressData[index] = progress
    } else {
      userProgressData.push(progress)
    }
  }
  