import React from "react";

const generateMockData = () => {
  const data = [];
  for (let i = 0; i < 98; i++) {
    data.push(Math.floor(Math.random() * 5));
  }
  return data;
};

const contributions = generateMockData();
const levels = ["bg-gray-200", "bg-green-200", "bg-green-400", "bg-green-600", "bg-green-800"];
const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

const GitHubHeatmap = () => {
  return (
    <div className="w-full max-w-lg mx-auto shadow-lg rounded-2xl overflow-hidden border p-4">
      <div className="text-lg font-semibold text-center mb-4">GitHub Contributions</div>
      <div className="grid grid-cols-15 gap-1 items-center">
        <div></div>
        {Array.from({ length: 14 }).map((_, col) => (
          <span key={col} className="text-xs text-gray-500 text-center">{col + 1}</span>
        ))}
        {days.map((day, row) => (
          <React.Fragment key={row}>
            <span className="text-xs text-gray-500 text-right pr-2">{day}</span>
            {Array.from({ length: 14 }).map((_, col) => {
              const index = row * 14 + col;
              return (
                <div
                  key={index}
                  className={`w-4 h-4 rounded ${levels[contributions[index]]}`}
                  title={`Day ${index + 1}: ${contributions[index]} contributions`}
                />
              );
            })}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default GitHubHeatmap;