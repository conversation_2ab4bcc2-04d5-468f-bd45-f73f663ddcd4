"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { getCoursesData } from "@/data/allCourses"
import CourseCard from "@/components/CourseCard"
import Footer from "@/components/footer"
import Navbar from "@/components/headers"
import { CourseOverviewType } from "@/types/courseOverview"
export default function AllCoursesPage() {
  const coursesData = getCoursesData
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredCourses, setFilteredCourses] = useState<CourseOverviewType[]>([])
  const [dataOverview, setDataOverview] = useState<CourseOverviewType[]>([])

  useEffect(() => {
    const courseOverview = getCoursesData.map((course) => ({
      id: course.id,
      name: course.name,
      description: course.description,
      coverImage: course.coverImage || "",
      time: Math.round(course.time),
      level: course.level,
      countLesson: course.lesson.length,
      certify: course.certify,
    }))
    console.log(courseOverview)
    setDataOverview(courseOverview)
    setFilteredCourses(courseOverview)
  }, [])

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const query = event.target.value.toLowerCase()
    setSearchQuery(query)

    const results = dataOverview.filter((course) => {
      const courseText = `
        ${course.name.toLowerCase()} 
        ${course.description.toLowerCase()} 
        ${course.level.toLowerCase()} 
        ${Math.round(course.time / 3600)} 
      `

      return courseText.includes(query)
    })

    setFilteredCourses(results)
  }

  return (
    <div>
      <Navbar />
    <div className="relative overflow-hidden min-h-screen bg-[#D0E2DF] flex flex-col px-10 py-12">
      {/* Header */}
      <h1 className="text-center mt-[10vh] mb-12 text-4xl md:text-4xl font-semibold" style={{ backgroundImage: 'linear-gradient(to right, #21A8AF, #008268)', WebkitBackgroundClip: 'text', color: 'transparent' }}>
        หลักสูตรที่เปิดสอนทั้งหมด
    </h1>

      {/* Search Section */}
      <div className="search-section w-full mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div className="relative w-full max-w-xl mx-auto">
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearch}
            placeholder="Search for a course..."
            className="w-full p-3 pl-10 rounded-lg bg-white text-gray-800 shadow-md focus:ring-2 focus:ring-indigo-400 focus:outline-none"
          />
          <div className="absolute inset-y-0 left-2 flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 16 16"
              fill="currentColor"
              className="w-5 h-5 text-gray-500"
            >
              <path
                fillRule="evenodd"
                d="M9.965 11.026a5 5 0 1 1 1.06-1.06l2.755 2.754a.75.75 0 1 1-1.06 1.06l-2.755-2.754ZM10.5 7a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Courses Section */}
      <div className="content-section max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="flex justify-end items-end lg:pt-5 mb-5 mr-2 text-lg font-semibold text-[#293D97]">
          {filteredCourses.length} {filteredCourses.length === 1 ? "หลักสูตร" : "หลักสูตร"}
        </h1>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 min-h-[300px]">
          {filteredCourses.length > 0 ? (
            filteredCourses.map((course) => (
              <CourseCard
                key={course.id}
                id={course.id}
                name={course.name}
                description={course.description}
                coverImage={course.coverImage}
                certify={course.certify}
                level={course.level}
                lessonCount={course.countLesson}
                duration={Math.round(course.time)} path={""}/>
            ))
          ) : (
            <div className="col-span-full text-center py-10">
              <p className="text-gray-500 text-lg">No courses found.</p>
            </div>
          )}
        </div>
      </div>
    </div>
    <Footer />
    </div>
  )
}

