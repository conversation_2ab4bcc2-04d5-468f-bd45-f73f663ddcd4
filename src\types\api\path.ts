export interface Course {
  course_slug: any;
  course_id: string;
  course_name: string;
}

export interface pathResponse {
  data: pathResponse | PromiseLike<pathResponse>;
  description: string;
  id: number;
  name: string;
  path: Course[];
  status: boolean;
}[];

export interface tableResponse {
  id: number;
  name: string;
  description: string;
  status: boolean;
  amount: number;
}

export interface Path {
  course_id: string;
}

export interface pathRequest {
  name: string;
  description: string;
  status: boolean; // Make status optional to prevent database errors  path: Path[];
}