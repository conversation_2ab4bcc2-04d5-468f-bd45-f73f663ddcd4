'use client';

import React, { Suspense, useRef, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Environment, Html, useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface MouseFollowGLBProps {
  modelPath: string;
  scale?: number;
}

const ModelLoader = () => (
  <Html center>
    <div className="animate-pulse text-white text-xs opacity-60">Loading...</div>
  </Html>
);

const ModelError = () => (
  <Html center>
    <div className="text-red-200 text-xs opacity-80">Model Error</div>
  </Html>
);

const MouseFollowModel: React.FC<{ modelPath: string; scale: number }> = ({ 
  modelPath, 
  scale 
}) => {
  const groupRef = useRef<THREE.Group>(null);
  const [mouse, setMouse] = useState({ x: 0, y: 0 });
  const [hasError, setHasError] = useState(false);
  
  // Use useGLTF hook
  const gltf = useGLTF(modelPath);

  // Mouse tracking
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      const x = (event.clientX / window.innerWidth) * 2 - 1;
      const y = -(event.clientY / window.innerHeight) * 2 + 1;
      setMouse({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Setup model properties when it loads
  useEffect(() => {
    if (gltf?.scene) {
      try {
        gltf.scene.traverse((child: THREE.Object3D) => {
          if (child instanceof THREE.Mesh) {
            child.castShadow = true;
            child.receiveShadow = true;
          }
        });
        setHasError(false);
      } catch (error) {
        console.error('Model setup error:', error);
        setHasError(true);
      }
    }
  }, [gltf]);

  useFrame(() => {
    if (groupRef.current && gltf?.scene && !hasError) {
      try {
        // Smooth mouse following
        const targetRotationY = mouse.x * 0.3; // Horizontal mouse movement
        const targetRotationX = mouse.y * 0.2; // Vertical mouse movement
        
        // Smooth interpolation
        groupRef.current.rotation.y = THREE.MathUtils.lerp(
          groupRef.current.rotation.y,
          targetRotationY,
          0.05
        );
        groupRef.current.rotation.x = THREE.MathUtils.lerp(
          groupRef.current.rotation.x,
          targetRotationX,
          0.05
        );
      } catch (error) {
        console.error('Animation error:', error);
      }
    }
  });

  // Handle errors
  if (hasError) {
    return <ModelError />;
  }

  // Don't render anything if scene is not loaded yet
  if (!gltf?.scene) {
    return <ModelLoader />;
  }

  return (
    <group ref={groupRef} scale={scale} position={[0, -0.1, 0]} rotation={[0, Math.PI / 2, 0]}>
      <primitive object={gltf.scene} />
    </group>
  );
};

const MouseFollowGLB: React.FC<MouseFollowGLBProps> = ({ 
  modelPath, 
  scale = 1.5 
}) => {
  return (
    <div className="w-full h-full" style={{ background: 'transparent' }}>
      <Canvas
        camera={{ position: [0, 0, 2], fov: 60 }}
        gl={{ 
          antialias: true, 
          alpha: true,
          preserveDrawingBuffer: true
        }}
        style={{ background: 'transparent' }}
        onCreated={({ gl }) => {
          gl.setClearColor(0x000000, 0); // Transparent background
        }}
      >
        <ambientLight intensity={0.6} />
        <directionalLight position={[2, 2, 2]} intensity={0.8} />
        <pointLight position={[-2, 2, 2]} intensity={0.4} />
        <Environment preset="apartment" />
        
        <Suspense fallback={<ModelLoader />}>
          <MouseFollowModel 
            modelPath={modelPath}
            scale={scale}
          />
        </Suspense>
      </Canvas>
    </div>
  );
};

// Preload the model to prevent loading issues
useGLTF.preload('/e-med/models/SP.glb');

export default MouseFollowGLB;
