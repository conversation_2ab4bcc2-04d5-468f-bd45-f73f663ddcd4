"use client"

import React from "react"

interface CourseCardSkeletonProps {
    count?: number
}

const CourseCardSkeleton: React.FC<CourseCardSkeletonProps> = ({ count = 4 }) => {
    return (
        <>
            {Array.from({ length: count }).map((_, index) => (
                <div
                    key={`course-skeleton-${index}`}
                    className="bg-white rounded-xl shadow-sm border p-4 flex-shrink-0 w-72 animate-pulse"
                >
                    {/* Course Image Skeleton */}
                    <div className="w-full h-40 bg-gray-200 rounded-lg mb-4"></div>

                    {/* Course Title Skeleton */}
                    <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>

                    {/* Course Description Skeleton */}
                    <div className="space-y-2 mb-4">
                        <div className="h-3 bg-gray-200 rounded w-full"></div>
                        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>

                    {/* Progress Section Skeleton */}
                    <div className="mb-4">
                        <div className="flex justify-between items-center mb-2">
                            <div className="h-3 bg-gray-200 rounded w-16"></div>
                            <div className="h-3 bg-gray-200 rounded w-12"></div>
                        </div>
                        {/* Progress bar skeleton */}
                        <div className="w-full h-2 bg-gray-200 rounded-full">
                            <div className="h-full bg-gray-300 rounded-full w-1/3"></div>
                        </div>
                    </div>

                    {/* Course Stats Skeleton */}
                    <div className="flex items-center gap-4 text-xs text-gray-500 mb-4">
                        <div className="flex items-center gap-1">
                            <div className="w-3 h-3 bg-gray-200 rounded"></div>
                            <div className="h-3 bg-gray-200 rounded w-16"></div>
                        </div>
                        <div className="flex items-center gap-1">
                            <div className="w-3 h-3 bg-gray-200 rounded"></div>
                            <div className="h-3 bg-gray-200 rounded w-12"></div>
                        </div>
                    </div>

                    {/* Action Button Skeleton */}
                    <div className="h-10 bg-gray-200 rounded-lg w-full"></div>
                </div>
            ))}
        </>
    )
}

export default CourseCardSkeleton
